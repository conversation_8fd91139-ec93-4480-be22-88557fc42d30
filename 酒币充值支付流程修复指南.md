# 酒币充值支付流程修复指南

## 🚨 问题诊断

从你的截图可以看出，酒币充值支付失败的原因是：**缺少支付订单创建接口**

### 错误分析
- **症状**：前端显示"设计重不存在"错误
- **原因**：前端直接跳转支付页面，但没有创建对应的支付订单
- **对比**：其他模块（如余额充值）都有完整的订单创建流程

## ✅ 已完成的修复

### 1. **新增支付订单创建接口**
- 📁 `app/controller/ApiWineCoin.php`
- 🔧 新增 `createRechargeOrder()` 方法
- 🎯 创建酒币充值订单 + 支付订单

### 2. **集成支付成功处理**
- 📁 `app/model/Payorder.php`
- 🔧 添加 `winecoin_recharge` 支付类型处理
- 🔧 新增 `winecoin_recharge_pay()` 方法
- 🎯 支付成功后自动充值酒币

### 3. **完善数据库结构**
- 📁 `install_winecoin_recharge.sql`
- 🔧 新增 `ddwx_wine_coin_recharge_order` 表
- 🎯 存储充值订单详细信息

### 4. **修复前端调用流程**
- 📁 `uniapp/pagesExt/winecoin/recharge.vue`
- 🔧 修改 `goRecharge()` 方法
- 🎯 先创建订单，再跳转支付

## 🚀 完整支付流程

### 修复后的正确流程
```
1. 用户选择充值金额
   ↓
2. 调用 ApiWineCoin/createRechargeOrder
   ↓
3. 创建 wine_coin_recharge_order (业务订单)
   ↓
4. 创建 payorder (支付订单)
   ↓
5. 返回 payorderid 给前端
   ↓
6. 跳转 /pagesExt/pay/pay?orderid={payorderid}
   ↓
7. 用户完成支付
   ↓
8. 支付成功回调 Payorder::winecoin_recharge_pay()
   ↓
9. 调用 WineCoin::rechargeSuccess() 充值酒币
   ↓
10. 完成！用户获得酒币
```

## 📋 安装步骤

### 第1步：执行数据库脚本
```sql
-- 执行完整的安装脚本
mysql -u用户名 -p数据库名 < install_winecoin_recharge.sql
```

**重要**：确保修改脚本中的 `aid` 值为你的实际平台ID！

### 第2步：验证数据库表
```sql
-- 检查套餐配置表
SELECT * FROM ddwx_winecoin_recharge_package WHERE aid = 你的平台ID;

-- 检查充值订单表
DESC ddwx_wine_coin_recharge_order;
```

### 第3步：测试充值流程
1. 登录前端小程序/H5
2. 进入酒币钱包 → 充值
3. 选择充值金额
4. 点击立即充值
5. 完成支付测试

## 🔍 测试验证

### 成功标志
- ✅ 点击充值显示"创建订单中..."
- ✅ 成功跳转到支付页面
- ✅ 支付完成后酒币正确到账
- ✅ 赠送酒币/积分正确发放

### 调试方法
1. **查看控制台**：前端开发者工具 Console
2. **检查网络**：Network 标签页看 API 请求
3. **查看数据库**：
   ```sql
   -- 查看充值订单
   SELECT * FROM ddwx_wine_coin_recharge_order ORDER BY id DESC LIMIT 5;
   
   -- 查看支付订单
   SELECT * FROM ddwx_payorder WHERE type = 'winecoin_recharge' ORDER BY id DESC LIMIT 5;
   
   -- 查看酒币流水
   SELECT * FROM ddwx_wine_coin_log WHERE type = 'recharge' ORDER BY id DESC LIMIT 5;
   ```

## 📊 支付类型对比

| 模块 | 业务订单表 | 支付类型 | 创建接口 | 支付处理 |
|------|------------|----------|----------|----------|
| 余额充值 | recharge_order | recharge | ApiMoney/recharge | 自动处理 |
| 商城订单 | shop_order | shop | ApiShop/settlement | shop_pay() |
| 酒币充值 | wine_coin_recharge_order | winecoin_recharge | **ApiWineCoin/createRechargeOrder** | **winecoin_recharge_pay()** |

## 🛠️ 故障排查

### 问题1：创建订单失败
**可能原因**：
- 数据库表不存在
- 充值套餐未配置
- 金额超出限制

**解决方案**：
```sql
-- 检查表是否存在
SHOW TABLES LIKE '%winecoin_recharge%';

-- 检查套餐配置
SELECT * FROM ddwx_winecoin_recharge_package WHERE aid = 你的平台ID;
```

### 问题2：支付失败
**可能原因**：
- payorderid 无效
- 支付类型未识别
- 订单状态异常

**解决方案**：
```sql
-- 检查支付订单
SELECT * FROM ddwx_payorder WHERE type = 'winecoin_recharge' AND id = {payorderid};
```

### 问题3：酒币未到账
**可能原因**：
- 支付回调失败
- WineCoin::rechargeSuccess() 执行失败
- 数据库事务回滚

**解决方案**：
- 查看服务器错误日志
- 检查数据库约束
- 验证 wine_coin 字段类型

## 📞 技术支持

如果按照指南操作仍有问题，请提供：

1. **错误截图**：前端控制台 + Network 请求
2. **数据库状态**：相关表的数据
3. **服务器日志**：PHP 错误日志
4. **测试环境**：平台ID、测试金额等

## 🎉 修复总结

本次修复解决了酒币充值支付流程的核心问题：

1. **补全缺失接口**：创建充值订单的 API 接口
2. **标准化流程**：与其他模块保持一致的支付处理
3. **完善数据结构**：专用的充值订单表
4. **优化用户体验**：清晰的加载状态和错误提示

现在酒币充值支付流程已经完全修复，用户可以正常充值酒币了！🎊 