<!DOCTYPE html>
<html>
<head>
    <title>酒币账单API测试</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
            font-weight: 600;
        }

        .bill-item {
            background: white;
            margin: 12px 0;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            border-left: 5px solid #007bff;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .bill-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .bill-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .bill-type {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .bill-time {
            font-size: 14px;
            color: #666;
        }

        .amount {
            font-weight: 700;
            font-size: 20px;
            margin: 8px 0;
        }
        .amount.positive {
            color: #28a745;
            text-shadow: 0 1px 2px rgba(40, 167, 69, 0.2);
        }
        .amount.negative {
            color: #dc3545;
            text-shadow: 0 1px 2px rgba(220, 53, 69, 0.2);
        }

        .status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }
        .status.completed {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.pending {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.failed {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .bill-remark {
            font-size: 14px;
            color: #666;
            margin-top: 8px;
            line-height: 1.4;
        }

        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
        }

        button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }
        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #545b62);
            color: white;
        }

        .detail-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin: 20px 0;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍷 酒币账单系统测试</h1>

        <div class="button-group">
            <h3 style="width: 100%; margin: 0 0 15px 0; color: #333;">📊 账单加载</h3>
            <button class="btn-primary" onclick="loadBills()">📋 加载全部账单</button>
            <button class="btn-danger" onclick="loadBills('withdraw')">💸 只看提现</button>
            <button class="btn-success" onclick="loadBills('recharge')">💰 只看充值</button>
        </div>

        <div class="button-group">
            <h3 style="width: 100%; margin: 0 0 15px 0; color: #333;">🔍 详情测试</h3>
            <button class="btn-danger" onclick="viewDetail('withdraw_6')">💸 测试提现详情 (withdraw_6)</button>
            <button class="btn-success" onclick="viewDetail('normal_1')">💰 测试普通流水详情 (normal_1)</button>
            <button class="btn-secondary" onclick="viewDetail('1')">🔄 测试兼容格式 (ID: 1)</button>
            <button class="btn-info" onclick="testDetailFormats()">🧪 批量测试详情格式</button>
        </div>

        <div id="result"></div>
    </div>

    <script>
        function loadBills(type = '') {
            const url = '/ApiWineCoin/log' + (type ? '?type=' + type : '');
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    console.log('API响应:', data);
                    displayBills(data);
                })
                .catch(error => {
                    console.error('请求失败:', error);
                    document.getElementById('result').innerHTML = '<p style="color: red;">请求失败: ' + error.message + '</p>';
                });
        }
        
        function displayBills(data) {
            const resultDiv = document.getElementById('result');
            
            if (data.status !== 1) {
                resultDiv.innerHTML = '<div class="error">❌ 加载失败: ' + data.msg + '</div>';
                return;
            }

            if (!data.logs || data.logs.length === 0) {
                resultDiv.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;"><div style="font-size: 48px; margin-bottom: 16px;">📭</div><div>暂无账单记录</div></div>';
                return;
            }

            let html = '<div class="detail-card">';
            html += '<div class="detail-header">';
            html += '<h2 class="detail-title">📋 账单列表 <span style="color: #007bff;">(' + data.count + ' 条)</span></h2>';
            html += '</div>';
            
            data.logs.forEach(bill => {
                const amountClass = bill.amount.startsWith('+') ? 'positive' : 'negative';
                const statusClass = bill.status || 'completed';
                
                html += `
                    <div class="bill-item">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <strong>${bill.type_desc}</strong>
                                <span class="status ${statusClass}">${bill.status_desc}</span>
                            </div>
                            <div class="amount ${amountClass}">${bill.amount} 酒币</div>
                        </div>
                        <div style="margin-top: 8px; color: #666; font-size: 14px;">
                            <div>时间: ${bill.createtime}</div>
                            <div>备注: ${bill.remark}</div>
                            ${bill.balance_after ? '<div>余额: ' + bill.balance_after + ' 酒币</div>' : ''}
                            <div style="margin-top: 5px;">
                                <button onclick="viewDetail('${bill.id}')" style="font-size: 12px; padding: 3px 8px;">查看详情</button>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            resultDiv.innerHTML = html;
        }
        
        function viewDetail(id) {
            const url = '/ApiWineCoin/detail?id=' + id;

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    console.log('详情数据:', data);
                    if (data.status === 1) {
                        displayDetail(data.detail);
                    } else {
                        alert('详情加载失败: ' + data.msg);
                    }
                })
                .catch(error => {
                    console.error('详情请求失败:', error);
                    alert('详情请求失败: ' + error.message);
                });
        }

        function displayDetail(detail) {
            let html = '<div style="border: 2px solid #007bff; padding: 20px; margin: 20px 0; border-radius: 8px; background: #f8f9fa;">';
            html += '<h3>📋 交易详情</h3>';
            html += '<div style="margin: 10px 0;"><strong>交易类型:</strong> ' + detail.type_desc + '</div>';
            html += '<div style="margin: 10px 0;"><strong>交易金额:</strong> <span style="color: ' + (detail.amount.startsWith('+') ? 'green' : 'red') + '; font-weight: bold;">' + detail.amount + '</span></div>';
            html += '<div style="margin: 10px 0;"><strong>交易时间:</strong> ' + detail.createtime + '</div>';
            html += '<div style="margin: 10px 0;"><strong>交易状态:</strong> <span class="status ' + detail.status + '">' + detail.status_desc + '</span></div>';

            if (detail.balance_after) {
                html += '<div style="margin: 10px 0;"><strong>账户余额:</strong> ' + detail.balance_after + '</div>';
            }

            html += '<div style="margin: 10px 0;"><strong>备注说明:</strong> ' + detail.remark + '</div>';

            // 提现专有信息
            if (detail.withdraw_info) {
                html += '<hr style="margin: 15px 0;">';
                html += '<h4>💸 提现信息</h4>';
                html += '<div style="margin: 8px 0;"><strong>订单号:</strong> ' + detail.withdraw_info.ordernum + '</div>';
                html += '<div style="margin: 8px 0;"><strong>提现金额:</strong> ' + detail.withdraw_info.withdraw_amount + '</div>';
                html += '<div style="margin: 8px 0;"><strong>手续费:</strong> ' + detail.withdraw_info.fee_amount + '</div>';
                html += '<div style="margin: 8px 0;"><strong>实际到账:</strong> ' + detail.withdraw_info.actual_amount + '</div>';
                html += '<div style="margin: 8px 0;"><strong>提现方式:</strong> ' + detail.withdraw_info.bank_type + '</div>';
                html += '<div style="margin: 8px 0;"><strong>收款账户:</strong> ' + detail.withdraw_info.bank_account + '</div>';

                if (detail.withdraw_info.bank_name) {
                    html += '<div style="margin: 8px 0;"><strong>银行名称:</strong> ' + detail.withdraw_info.bank_name + '</div>';
                }

                html += '<div style="margin: 8px 0;"><strong>收款人:</strong> ' + detail.withdraw_info.account_name + '</div>';
                html += '<div style="margin: 8px 0;"><strong>申请时间:</strong> ' + detail.withdraw_info.apply_time + '</div>';

                if (detail.withdraw_info.audit_time) {
                    html += '<div style="margin: 8px 0;"><strong>审核时间:</strong> ' + detail.withdraw_info.audit_time + '</div>';
                }

                if (detail.withdraw_info.complete_time) {
                    html += '<div style="margin: 8px 0;"><strong>完成时间:</strong> ' + detail.withdraw_info.complete_time + '</div>';
                }

                if (detail.withdraw_info.audit_remark) {
                    html += '<div style="margin: 8px 0;"><strong>审核备注:</strong> ' + detail.withdraw_info.audit_remark + '</div>';
                }

                // 状态历史
                if (detail.status_history && detail.status_history.length > 0) {
                    html += '<hr style="margin: 15px 0;">';
                    html += '<h4>📈 状态历史</h4>';
                    detail.status_history.forEach(function(history) {
                        html += '<div style="margin: 5px 0; padding: 5px; background: #e9ecef; border-radius: 3px;">';
                        html += '<span style="font-weight: bold;">' + history.time + '</span> - ' + history.status;
                        if (history.remark) {
                            html += ' (' + history.remark + ')';
                        }
                        html += '</div>';
                    });
                }
            }

            // 普通交易信息
            if (detail.transaction_info) {
                html += '<hr style="margin: 15px 0;">';
                html += '<h4>💰 交易信息</h4>';
                html += '<div style="margin: 8px 0;"><strong>交易ID:</strong> ' + detail.transaction_info.transaction_id + '</div>';
                html += '<div style="margin: 8px 0;"><strong>交易前余额:</strong> ' + detail.transaction_info.balance_before + ' 酒币</div>';
                html += '<div style="margin: 8px 0;"><strong>交易后余额:</strong> ' + detail.transaction_info.balance_after + ' 酒币</div>';

                if (detail.transaction_info.other_user) {
                    html += '<div style="margin: 8px 0;"><strong>对方用户:</strong> ' + detail.transaction_info.other_user.nickname + '</div>';
                }

                if (detail.transaction_info.related_order) {
                    html += '<div style="margin: 8px 0;"><strong>关联订单:</strong> ' + detail.transaction_info.related_order.title + '</div>';
                }
            }

            html += '</div>';

            // 显示详情
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = html + resultDiv.innerHTML;
        }

        function testDetailFormats() {
            const testIds = ['withdraw_6', 'normal_1', '1', 'withdraw_999', 'normal_999'];
            let results = '<div style="border: 2px solid #ffc107; padding: 20px; margin: 20px 0; border-radius: 8px; background: #fff3cd;">';
            results += '<h3>🧪 批量测试结果</h3>';

            testIds.forEach(function(id) {
                const url = '/ApiWineCoin/detail?id=' + id;

                fetch(url)
                    .then(response => response.json())
                    .then(data => {
                        const status = data.status === 1 ? '✅ 成功' : '❌ 失败';
                        const color = data.status === 1 ? 'green' : 'red';
                        results += '<div style="margin: 8px 0; color: ' + color + ';">';
                        results += '<strong>' + id + ':</strong> ' + status;
                        if (data.status === 0) {
                            results += ' - ' + data.msg;
                        } else {
                            results += ' - ' + data.detail.type_desc;
                        }
                        results += '</div>';

                        // 更新显示
                        document.getElementById('result').innerHTML = results + '</div>' + document.getElementById('result').innerHTML;
                    })
                    .catch(error => {
                        results += '<div style="margin: 8px 0; color: red;">';
                        results += '<strong>' + id + ':</strong> ❌ 网络错误 - ' + error.message;
                        results += '</div>';

                        // 更新显示
                        document.getElementById('result').innerHTML = results + '</div>' + document.getElementById('result').innerHTML;
                    });
            });
        }
        
        // 页面加载时自动加载账单
        window.onload = function() {
            loadBills();
        };
    </script>
</body>
</html>
