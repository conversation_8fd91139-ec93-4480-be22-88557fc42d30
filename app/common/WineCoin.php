<?php
/**
 * 酒币核心业务类
 * 基于现有余额系统架构设计，保持一致性
 */

namespace app\common;
use think\facade\Db;
use think\facade\Log;

class WineCoin
{
    /**
     * 添加用户酒币 - 参考 Member::addmoney 实现
     * @param int $aid 平台ID
     * @param int $mid 用户ID
     * @param float $amount 变动金额（正数增加，负数减少）
     * @param string $remark 备注
     * @param string $type 变动类型
     * @param int $relation_id 关联ID
     * @param int $from_mid 来源用户ID
     * @param int $to_bid 目标商户ID
     * @param array $extra_data 扩展数据
     * @return array
     */
    public static function addWineCoin($aid, $mid, $amount, $remark, $type = '', $relation_id = 0, $from_mid = 0, $to_bid = 0, $extra_data = []) {
        if($amount == 0) return ['status' => 1, 'msg' => '金额为0，无需处理'];
        
        // 获取用户信息并加锁
        $member = Db::name('member')->where('aid', $aid)->where('id', $mid)->lock(true)->find();
        if(!$member) return ['status' => 0, 'msg' => '用户不存在'];
        
        // 计算变动后余额
        $balance_before = $member['wine_coin'];
        $balance_after = $balance_before + $amount;
        
        // 检查余额是否足够（扣减时）
        if($balance_after < 0 && $amount < 0) {
            return ['status' => 0, 'msg' => '酒币余额不足'];
        }
        
        // 更新用户酒币余额
        Db::name('member')->where('aid', $aid)->where('id', $mid)->update(['wine_coin' => $balance_after]);
        
        // 记录流水
        $log_data = [
            'aid' => $aid,
            'mid' => $mid,
            'bid' => $to_bid,
            'amount' => $amount,
            'balance_after' => $balance_after,
            'type' => $type,
            'relation_type' => '',
            'relation_id' => $relation_id,
            'from_mid' => $from_mid,
            'to_mid' => $mid,
            'from_bid' => 0,
            'to_bid' => $to_bid,
            'remark' => $remark,
            'createtime' => time()
        ];
        Db::name('wine_coin_log')->insert($log_data);
        
        return ['status' => 1, 'msg' => '操作成功', 'balance' => $balance_after];
    }
    
    /**
     * 添加商户酒币 - 参考 Business::addmoney 实现
     * @param int $aid 平台ID
     * @param int $bid 商户ID
     * @param float $amount 变动金额
     * @param string $remark 备注
     * @param string $type 变动类型
     * @param int $relation_id 关联ID
     * @param int $from_mid 来源用户ID
     * @return array
     */
    public static function addBusinessWineCoin($aid, $bid, $amount, $remark, $type = '', $relation_id = 0, $from_mid = 0) {
        if($amount == 0) return ['status' => 1, 'msg' => '金额为0，无需处理'];
        
        $business = Db::name('business')->where('aid', $aid)->where('id', $bid)->lock(true)->find();
        if(!$business) return ['status' => 0, 'msg' => '商户不存在'];
        
        $balance_before = $business['wine_coin'];
        $balance_after = $balance_before + $amount;
        
        // 检查余额是否足够（扣减时）
        if($balance_after < 0 && $amount < 0) {
            return ['status' => 0, 'msg' => '商户酒币余额不足'];
        }
        
        // 更新商户酒币余额
        Db::name('business')->where('aid', $aid)->where('id', $bid)->update(['wine_coin' => $balance_after]);
        
        // 记录商户酒币流水
        $log_data = [
            'aid' => $aid,
            'bid' => $bid,
            'type' => $amount > 0 ? 1 : 2, // 1:收入 2:支出，根据金额正负判断
            'money' => abs($amount),
            'before_balance' => $balance_before,
            'after_balance' => $balance_after,
            'relation_type' => $type,
            'relation_id' => $relation_id,
            'from_mid' => $from_mid,
            'remark' => $remark,
            'createtime' => time()
        ];
        Db::name('business_wine_coin_log')->insert($log_data);
        
        return ['status' => 1, 'msg' => '操作成功', 'balance' => $balance_after];
    }
    
    /**
     * 酒币转账 - 用户间转账
     * @param int $aid 平台ID
     * @param int $from_mid 转出用户ID
     * @param int $to_mid 转入用户ID
     * @param float $amount 转账金额
     * @param string $remark 转账备注
     * @return array
     */
    public static function transfer($aid, $from_mid, $to_mid, $amount, $remark = '') {
        if($amount <= 0) return ['status' => 0, 'msg' => '转账金额必须大于0'];
        if($from_mid == $to_mid) return ['status' => 0, 'msg' => '不能向自己转账'];
        
        // 检查转账对象是否存在
        $to_member = Db::name('member')->where('aid', $aid)->where('id', $to_mid)->find();
        if(!$to_member) return ['status' => 0, 'msg' => '转账对象不存在'];
        
        // 获取系统设置
        $setting = self::getSetting($aid);
        if(!$setting['transfer_enable']) return ['status' => 0, 'msg' => '转账功能已关闭'];
        
        // 检查转账限制
        if($amount < $setting['min_transfer_amount']) {
            return ['status' => 0, 'msg' => '转账金额不能小于' . $setting['min_transfer_amount']];
        }
        if($amount > $setting['max_transfer_amount']) {
            return ['status' => 0, 'msg' => '转账金额不能大于' . $setting['max_transfer_amount']];
        }
        
        // 检查每日转账限额
        $today_transfer = self::getTodayTransferAmount($aid, $from_mid);
        if($today_transfer + $amount > $setting['daily_transfer_limit']) {
            return ['status' => 0, 'msg' => '超出每日转账限额'];
        }
        
        // 计算手续费
        $fee_amount = 0;
        if($setting['transfer_fee_enable']) {
            $fee_amount = $amount * $setting['transfer_fee_rate'];
            if($fee_amount < $setting['transfer_fee_min']) $fee_amount = $setting['transfer_fee_min'];
            if($fee_amount > $setting['transfer_fee_max']) $fee_amount = $setting['transfer_fee_max'];
        }
        
        $total_amount = $amount + $fee_amount;
        $actual_amount = $amount;
        
        Db::startTrans();
        try {
            // 扣减转出方酒币（包含手续费）
            $result1 = self::addWineCoin($aid, $from_mid, -$total_amount, "转账给用户{$to_mid}：{$remark}", 'transfer_out', 0, $from_mid, 0);
            if($result1['status'] == 0) {
                Db::rollback();
                return $result1;
            }
            
            // 增加转入方酒币
            $result2 = self::addWineCoin($aid, $to_mid, $actual_amount, "收到用户{$from_mid}转账：{$remark}", 'transfer_in', 0, $from_mid, 0);
            if($result2['status'] == 0) {
                Db::rollback();
                return $result2;
            }
            
            // 记录手续费（如果有）
            if($fee_amount > 0) {
                self::addWineCoin($aid, $from_mid, 0, "转账手续费", 'transfer_fee', 0, $from_mid, 0, ['fee_amount' => $fee_amount]);
            }
            
            // 生成转账单号
            $ordernum = date('ymdHis') . $aid . rand(1000, 9999);
            
            // 记录转账记录
            $transfer_data = [
                'aid' => $aid,
                'ordernum' => $ordernum,
                'from_mid' => $from_mid,
                'to_mid' => $to_mid,
                'amount' => $amount,
                'fee_amount' => $fee_amount,
                'actual_amount' => $actual_amount,
                'transfer_type' => 1, // 用户转用户
                'status' => 1, // 成功
                'remark' => $remark,
                'createtime' => time(),
                'complete_time' => time()
            ];
            Db::name('wine_coin_transfer')->insert($transfer_data);
            
            Db::commit();
            return ['status' => 1, 'msg' => '转账成功', 'ordernum' => $ordernum];
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('酒币转账失败：' . $e->getMessage());
            return ['status' => 0, 'msg' => '转账失败，请稍后重试'];
        }
    }
    
    /**
     * 用户向商户付款
     * @param int $aid 平台ID
     * @param int $from_mid 付款用户ID
     * @param int $to_bid 收款商户ID
     * @param float $amount 付款金额
     * @param string $remark 付款备注
     * @param string $relation_type 关联类型
     * @param int $relation_id 关联ID
     * @return array
     */
    public static function payToBusiness($aid, $from_mid, $to_bid, $amount, $remark = '', $relation_type = '', $relation_id = 0) {
        if($amount <= 0) return ['status' => 0, 'msg' => '付款金额必须大于0'];
        
        // 检查商户是否存在
        $business = Db::name('business')->where('aid', $aid)->where('id', $to_bid)->find();
        if(!$business) return ['status' => 0, 'msg' => '商户不存在'];
        
        Db::startTrans();
        try {
            // 扣减用户酒币
            $result1 = self::addWineCoin($aid, $from_mid, -$amount, "向商户{$to_bid}付款：{$remark}", 'payment', $relation_id, $from_mid, $to_bid);
            if($result1['status'] == 0) {
                Db::rollback();
                return $result1;
            }
            
            // 增加商户酒币
            $result2 = self::addBusinessWineCoin($aid, $to_bid, $amount, "收到用户{$from_mid}付款：{$remark}", 'income', $relation_id, $from_mid);
            if($result2['status'] == 0) {
                Db::rollback();
                return $result2;
            }
            
            // 生成支付单号
            $ordernum = date('ymdHis') . $aid . rand(1000, 9999);
            
            // 记录支付订单
            $pay_order_data = [
                'aid' => $aid,
                'ordernum' => $ordernum,
                'payer_mid' => $from_mid,
                'payee_bid' => $to_bid,
                'payee_mid' => 0,
                'amount' => $amount,
                'fee_amount' => 0,
                'actual_amount' => $amount,
                'pay_type' => 3, // 商品支付
                'qrcode_id' => 0,
                'relation_type' => $relation_type,
                'relation_id' => $relation_id,
                'remark' => $remark,
                'status' => 1, // 已支付
                'pay_time' => time(),
                'createtime' => time()
            ];
            Db::name('wine_coin_pay_order')->insert($pay_order_data);
            
            Db::commit();
            return ['status' => 1, 'msg' => '支付成功', 'ordernum' => $ordernum];
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('酒币支付失败：' . $e->getMessage());
            return ['status' => 0, 'msg' => '支付失败，请稍后重试'];
        }
    }

    /**
     * 酒币充值处理 - 支付成功后调用
     * @param int $order_id 充值订单ID
     * @return array
     */
    public static function rechargeSuccess($order_id) {
        $order = Db::name('winecoin_recharge_order')->where('id', $order_id)->find();
        if(!$order) return ['status' => 0, 'msg' => '充值订单不存在'];
        if($order['status'] == 1) return ['status' => 1, 'msg' => '订单已处理'];

        Db::startTrans();
        try {
            // 更新订单状态
            Db::name('winecoin_recharge_order')->where('id', $order_id)->update([
                'status' => 1,
                'pay_time' => time()
            ]);

            // 构建详细备注
            $remark = "酒币充值：支付{$order['amount']}元";
            if($order['bonus_amount'] > 0) {
                $remark .= "，获得{$order['wine_coin_amount']}酒币+赠送{$order['bonus_amount']}酒币";
            } else {
                $remark .= "，获得{$order['wine_coin_amount']}酒币";
            }
            
            // 增加用户酒币 - 只记录一次完整流水
            $total_amount = $order['wine_coin_amount'] + $order['bonus_amount'];
            $result = self::addWineCoin($order['aid'], $order['mid'], $total_amount, $remark, 'recharge', $order_id);
            if($result['status'] == 0) {
                Db::rollback();
                return $result;
            }

            Db::commit();
            return ['status' => 1, 'msg' => '充值成功'];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('酒币充值处理失败：' . $e->getMessage());
            return ['status' => 0, 'msg' => '充值处理失败'];
        }
    }

    /**
     * 申请提现
     * @param int $aid 平台ID
     * @param int $mid 用户ID
     * @param float $amount 提现金额
     * @param string $bank_type 提现方式
     * @param array $bank_info 银行信息
     * @return array
     */
    public static function applyWithdraw($aid, $mid, $amount, $bank_type, $bank_info = [], $bid = 0) {
        if($amount <= 0) return ['status' => 0, 'msg' => '提现金额必须大于0'];

        // 获取系统设置
        $setting = self::getSetting($aid);
        if(!$setting['withdraw_enable']) return ['status' => 0, 'msg' => '提现功能已关闭'];

        // 检查提现限制
        if($amount < $setting['min_withdraw_amount']) {
            return ['status' => 0, 'msg' => '提现金额不能小于' . $setting['min_withdraw_amount']];
        }
        if($amount > $setting['max_withdraw_amount']) {
            return ['status' => 0, 'msg' => '提现金额不能大于' . $setting['max_withdraw_amount']];
        }

        // 检查每日提现限额
        $today_withdraw = self::getTodayWithdrawAmount($aid, $mid);
        if($today_withdraw + $amount > $setting['daily_withdraw_limit']) {
            return ['status' => 0, 'msg' => '超出每日提现限额'];
        }

        // 检查用户余额
        $member = Db::name('member')->where('aid', $aid)->where('id', $mid)->find();
        if(!$member) return ['status' => 0, 'msg' => '用户不存在'];
        if($member['wine_coin'] < $amount) return ['status' => 0, 'msg' => '酒币余额不足'];

        // 计算手续费 - 支持商户个性化设置
        $fee_amount = 0;
        
        // 获取用户所属商户信息
        $user_business = null;
        if($bid > 0) {
            $user_business = Db::name('business')->where('aid', $aid)->where('id', $bid)->find();
        }
        
        // 优先使用商户个性化手续费设置
        $use_custom_fee = false;
        if($user_business && $user_business['wine_coin_withdraw_fee_enable'] == 1) {
            $use_custom_fee = true;
            $fee_amount = $amount * $user_business['wine_coin_withdraw_fee_rate'];
        } else if($setting['withdraw_fee_enable']) {
            // 使用全局手续费设置
            $fee_amount = $amount * $setting['withdraw_fee_rate'];
            if($fee_amount < $setting['withdraw_fee_min']) $fee_amount = $setting['withdraw_fee_min'];
            if($fee_amount > $setting['withdraw_fee_max']) $fee_amount = $setting['withdraw_fee_max'];
        }

        $actual_amount = $amount - $fee_amount;

        Db::startTrans();
        try {
            // 冻结用户酒币
            $result = self::addWineCoin($aid, $mid, -$amount, '申请提现，冻结酒币', 'withdraw_freeze', 0, $mid, 0);
            if($result['status'] == 0) {
                Db::rollback();
                return $result;
            }

            // 生成提现单号
            $ordernum = 'WD' . date('ymdHis') . $aid . rand(1000, 9999);

            // 创建提现申请
            $withdraw_data = [
                'aid' => $aid,
                'mid' => $mid,
                'bid' => 0,
                'ordernum' => $ordernum,
                'user_type' => 1, // 用户
                'amount' => $amount,
                'fee_amount' => $fee_amount,
                'actual_amount' => $actual_amount,
                'bank_type' => $bank_type,
                'bank_account' => $bank_info['account'] ?? '',
                'bank_name' => $bank_info['bank_name'] ?? '',
                'account_name' => $bank_info['account_name'] ?? '',
                'status' => $setting['withdraw_auto_audit'] ? 1 : 0, // 自动审核或待审核
                'audit_time' => $setting['withdraw_auto_audit'] ? time() : 0,
                'createtime' => time()
            ];
            $withdraw_id = Db::name('wine_coin_withdraw')->insertGetId($withdraw_data);

            // 如果自动审核，直接处理
            if($setting['withdraw_auto_audit']) {
                self::processWithdraw($withdraw_id);
            }

            Db::commit();
            return ['status' => 1, 'msg' => '提现申请成功', 'ordernum' => $ordernum];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('酒币提现申请失败：' . $e->getMessage());
            return ['status' => 0, 'msg' => '提现申请失败，请稍后重试'];
        }
    }

    /**
     * 处理提现申请
     * @param int $withdraw_id 提现申请ID
     * @param int $status 处理状态 1=通过 0=拒绝
     * @param string $remark 备注
     * @return array
     */
    public static function processWithdraw($withdraw_id, $status = 1, $remark = '') {
        $withdraw = Db::name('wine_coin_withdraw')->where('id', $withdraw_id)->find();
        if(!$withdraw) return ['status' => 0, 'msg' => '提现申请不存在'];
        if($withdraw['status'] != 0) return ['status' => 0, 'msg' => '提现申请已处理'];

        Db::startTrans();
        try {
            if($status == 1) {
                // 审核通过 - 改为状态1（审核通过），而不是直接完成
                Db::name('wine_coin_withdraw')->where('id', $withdraw_id)->update([
                    'status' => 1, // 审核通过，等待打款
                    'audit_time' => time(),
                    'audit_remark' => $remark
                ]);

                // 不在这里记录提现成功流水，应该在实际打款完成时记录
                // 这里只记录审核通过的状态变更
                self::addWineCoin($withdraw['aid'], $withdraw['mid'], 0, '提现审核通过，等待打款', 'withdraw_approved', $withdraw_id, $withdraw['mid'], 0, [
                    'withdraw_amount' => $withdraw['amount'],
                    'fee_amount' => $withdraw['fee_amount'],
                    'actual_amount' => $withdraw['actual_amount'],
                    'ordernum' => $withdraw['ordernum']
                ]);

            } else {
                // 审核拒绝，退回酒币
                Db::name('wine_coin_withdraw')->where('id', $withdraw_id)->update([
                    'status' => 2, // 已拒绝
                    'audit_time' => time(),
                    'audit_remark' => $remark
                ]);

                // 退回冻结的酒币
                self::addWineCoin($withdraw['aid'], $withdraw['mid'], $withdraw['amount'], '提现被拒绝，退回酒币', 'withdraw_refund', $withdraw_id, $withdraw['mid'], 0, [
                    'ordernum' => $withdraw['ordernum']
                ]);
            }

            Db::commit();
            return ['status' => 1, 'msg' => $status == 1 ? '提现审核通过' : '提现已拒绝'];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('处理提现申请失败：' . $e->getMessage());
            return ['status' => 0, 'msg' => '处理失败，请稍后重试'];
        }
    }
    
    /**
     * 标记提现完成（实际打款后调用）
     * @param int $withdraw_id 提现申请ID
     * @param string $remark 完成备注
     * @return array
     */
    public static function completeWithdraw($withdraw_id, $remark = '已完成打款') {
        $withdraw = Db::name('wine_coin_withdraw')->where('id', $withdraw_id)->find();
        if(!$withdraw) return ['status' => 0, 'msg' => '提现申请不存在'];
        if($withdraw['status'] != 1) return ['status' => 0, 'msg' => '提现申请状态错误'];

        Db::startTrans();
        try {
            // 标记为已完成
            Db::name('wine_coin_withdraw')->where('id', $withdraw_id)->update([
                'status' => 3, // 已完成
                'complete_time' => time(),
                'audit_remark' => $withdraw['audit_remark'] . ' | ' . $remark
            ]);

            // 记录提现成功流水
            self::addWineCoin($withdraw['aid'], $withdraw['mid'], 0, '提现完成：实际到账' . $withdraw['actual_amount'] . '元', 'withdraw_success', $withdraw_id, $withdraw['mid'], 0, [
                'withdraw_amount' => $withdraw['amount'],
                'fee_amount' => $withdraw['fee_amount'],
                'actual_amount' => $withdraw['actual_amount'],
                'ordernum' => $withdraw['ordernum'],
                'bank_type' => $withdraw['bank_type'],
                'bank_account' => $withdraw['bank_account']
            ]);

            Db::commit();
            return ['status' => 1, 'msg' => '提现已标记完成'];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('标记提现完成失败：' . $e->getMessage());
            return ['status' => 0, 'msg' => '操作失败，请稍后重试'];
        }
    }

    /**
     * 获取系统设置
     * @param int $aid 平台ID
     * @return array
     */
    public static function getSetting($aid) {
        $setting = Db::name('wine_coin_setting')->where('aid', $aid)->find();
        if(!$setting) {
            // 创建默认设置
            $default_setting = [
                'aid' => $aid,
                'enable' => 1,
                'recharge_enable' => 1,
                'transfer_enable' => 1,
                'withdraw_enable' => 1,
                'min_recharge_amount' => 1.00,
                'max_recharge_amount' => 10000.00,
                'min_transfer_amount' => 0.01,
                'max_transfer_amount' => 5000.00,
                'daily_transfer_limit' => 10000.00,
                'min_withdraw_amount' => 1.00,
                'max_withdraw_amount' => 5000.00,
                'daily_withdraw_limit' => 10000.00,
                'transfer_fee_enable' => 0,
                'transfer_fee_rate' => 0.0000,
                'transfer_fee_min' => 0.00,
                'transfer_fee_max' => 0.00,
                'withdraw_fee_enable' => 0,
                'withdraw_fee_rate' => 0.0000,
                'withdraw_fee_min' => 0.00,
                'withdraw_fee_max' => 0.00,
                'withdraw_auto_audit' => 0,
                'recharge_bonus_enable' => 0,
                'recharge_bonus_rules' => '',
                'createtime' => time(),
                'updatetime' => time()
            ];
            Db::name('wine_coin_setting')->insert($default_setting);
            $setting = $default_setting;
        }
        return $setting;
    }

    /**
     * 获取用户今日转账金额
     * @param int $aid 平台ID
     * @param int $mid 用户ID
     * @return float
     */
    public static function getTodayTransferAmount($aid, $mid) {
        $today_start = strtotime(date('Y-m-d 00:00:00'));
        $today_end = strtotime(date('Y-m-d 23:59:59'));

        return Db::name('wine_coin_transfer')
            ->where('aid', $aid)
            ->where('from_mid', $mid)
            ->where('status', 1)
            ->where('createtime', 'between', [$today_start, $today_end])
            ->sum('amount');
    }

    /**
     * 获取用户今日提现金额
     * @param int $aid 平台ID
     * @param int $mid 用户ID
     * @return float
     */
    public static function getTodayWithdrawAmount($aid, $mid) {
        $today_start = strtotime(date('Y-m-d 00:00:00'));
        $today_end = strtotime(date('Y-m-d 23:59:59'));

        return Db::name('wine_coin_withdraw')
            ->where('aid', $aid)
            ->where('mid', $mid)
            ->where('status', 'in', [0, 1, 3]) // 待审核、已通过、已完成
            ->where('createtime', 'between', [$today_start, $today_end])
            ->sum('amount');
    }

    /**
     * 获取用户酒币余额
     * @param int $aid 平台ID
     * @param int $mid 用户ID
     * @return float
     */
    public static function getBalance($aid, $mid) {
        return Db::name('member')->where('aid', $aid)->where('id', $mid)->value('wine_coin') ?: 0;
    }

    /**
     * 获取商户酒币余额
     * @param int $aid 平台ID
     * @param int $bid 商户ID
     * @return float
     */
    public static function getBusinessBalance($aid, $bid) {
        return Db::name('business')->where('aid', $aid)->where('id', $bid)->value('wine_coin') ?: 0;
    }

    /**
     * 风控检查
     * @param int $aid 平台ID
     * @param int $mid 用户ID
     * @param string $action 操作类型
     * @param float $amount 金额
     * @return array
     */
    public static function riskCheck($aid, $mid, $action, $amount) {
        // 检查频繁操作
        $recent_count = Db::name('wine_coin_log')
            ->where('aid', $aid)
            ->where('mid', $mid)
            ->where('type', $action)
            ->where('createtime', '>', time() - 3600) // 1小时内
            ->count();

        if($recent_count > 10) {
            return ['status' => 0, 'msg' => '操作过于频繁，请稍后再试'];
        }

        // 检查大额交易
        if($amount > 10000) {
            // 记录风控日志
            $risk_data = [
                'aid' => $aid,
                'mid' => $mid,
                'risk_type' => 'large_amount',
                'risk_level' => 2,
                'trigger_amount' => $amount,
                'trigger_count' => 1,
                'risk_score' => 80,
                'action_taken' => 'log_only',
                'status' => 0,
                'createtime' => time()
            ];
            Db::name('wine_coin_risk_log')->insert($risk_data);
        }

        return ['status' => 1, 'msg' => '风控检查通过'];
    }

    /**
     * 生成收款码
     * @param int $aid 平台ID
     * @param int $bid 商户ID
     * @param int $mid 用户ID
     * @param float $amount 固定金额（0为不固定）
     * @param int $expire_time 过期时间
     * @return array
     */
    public static function generateQRCode($aid, $bid = 0, $mid = 0, $amount = 0, $expire_time = 0) {
        $qrcode_content = json_encode([
            'aid' => $aid,
            'bid' => $bid,
            'mid' => $mid,
            'amount' => $amount,
            'timestamp' => time(),
            'sign' => md5($aid . $bid . $mid . $amount . time() . 'wine_coin_qrcode')
        ]);

        $qrcode_data = [
            'aid' => $aid,
            'bid' => $bid,
            'mid' => $mid,
            'qrcode_type' => $amount > 0 ? 1 : 2, // 1:固定金额 2:动态金额
            'qrcode_content' => $qrcode_content,
            'qrcode_image' => '', // 二维码图片路径，需要生成
            'amount' => $amount,
            'expire_time' => $expire_time,
            'used_count' => 0,
            'max_use_count' => 0,
            'status' => 1,
            'createtime' => time(),
            'updatetime' => time()
        ];

        $qrcode_id = Db::name('wine_coin_qrcode')->insertGetId($qrcode_data);

        return [
            'status' => 1,
            'msg' => '生成成功',
            'qrcode_id' => $qrcode_id,
            'qrcode_content' => $qrcode_content
        ];
    }
}
