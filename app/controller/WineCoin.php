<?php
/**
 * 会员酒币管理控制器
 */

namespace app\controller;
use think\facade\Db;
use think\facade\View;

class WineCoin extends Common
{
    public function initialize(){
        parent::initialize();
        if($this->user['bid'] > 0) showmsg('无访问权限');
    }

    /**
     * 会员酒币流水记录
     */
    public function winecoinlog(){
        if(request()->isAjax()){
            $page = input('param.page');
            $limit = input('param.limit');
            $order = 'id desc';
            
            $where = [];
            $where[] = ['aid','=',aid];
            
            // 添加搜索功能
            if(input('param.mid')) $where[] = ['mid','=',input('param.mid')];
            if(input('param.nickname')){
                // 根据昵称搜索会员ID
                $memberIds = Db::name('member')->where('aid',aid)->where('nickname','like','%'.input('param.nickname').'%')->column('id');
                if($memberIds) $where[] = ['mid','in',$memberIds];
                else $where[] = ['mid','=',-1]; // 没找到匹配用户时返回空结果
            }
            if(input('param.tel')){
                // 根据手机号搜索会员ID
                $memberIds = Db::name('member')->where('aid',aid)->where('tel','like','%'.input('param.tel').'%')->column('id');
                if($memberIds) $where[] = ['mid','in',$memberIds];
                else $where[] = ['mid','=',-1];
            }
            if(input('param.type')) $where[] = ['type','=',input('param.type')];
            if(input('param.ctime')){
                $ctime = explode(' ~ ',input('param.ctime'));
                $where[] = ['createtime','>=',strtotime($ctime[0])];
                $where[] = ['createtime','<',strtotime($ctime[1]) + 86400];
            }
            
            $count = Db::name('wine_coin_log')->where($where)->count();
            $data = Db::name('wine_coin_log')->where($where)->page($page,$limit)->order($order)->select()->toArray();
            
            // 类型映射 - 修复搜索类型匹配问题
            $type_map = [
                'recharge' => '充值',
                'consume' => '消费',
                'transfer_in' => '转账收入',
                'transfer_out' => '转账支出',
                'withdraw' => '提现',
                'withdraw_freeze' => '提现冻结',
                'withdraw_success' => '提现成功',
                'withdraw_refund' => '提现退回',
                'refund' => '退款',
                'bonus' => '奖励',
                'compensation' => '补偿',
                'adjustment' => '手动调整',
                'commission' => '佣金',
                'gift' => '赠送',
                'penalty' => '扣罚',
                'activity' => '活动',
                'system' => '系统',
                'manual' => '手动调整',
                'order' => '订单',
                'payment' => '支付',
                'cancel' => '取消',
                'freeze' => '冻结',
                'unfreeze' => '解冻',
                'income' => '收入',
                'expense' => '支出',
                'business_recharge' => '后台修改',
                'business_expense' => '后台修改'
            ];

            foreach($data as $k=>$v){
                $data[$k]['createtime'] = date('Y-m-d H:i:s',$v['createtime']);

                // 获取用户信息
                if($v['mid']){
                    $member = Db::name('member')->where('id',$v['mid'])->find();
                    $data[$k]['nickname'] = $member ? $member['nickname'] : '用户不存在';
                }

                // 获取商户信息
                if($v['bid']){
                    $business = Db::name('business')->where('id',$v['bid'])->find();
                    $data[$k]['business_name'] = $business ? $business['name'] : '商户不存在';
                }

                // 格式化金额显示
                $data[$k]['amount_text'] = ($v['amount'] > 0 ? '+' : '') . $v['amount'];
                $data[$k]['amount_color'] = $v['amount'] > 0 ? 'green' : 'red';

                // 计算变动前余额（因为表中只有balance_after字段）
                $data[$k]['balance_before'] = $v['balance_after'] - $v['amount'];

                // 类型中文显示
                $data[$k]['type_text'] = $type_map[$v['type']] ?? $v['type'];
            }
            
            return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$data]);
        }
        
        // 获取用户信息
        $member = null;
        if(input('param.mid')){
            $member = Db::name('member')->where('aid',aid)->where('id',input('param.mid'))->find();
        }
        
        View::assign('member',$member);
        return View::fetch();
    }

    /**
     * 手动调整会员酒币
     */
    public function adjust(){
        if(request()->isPost()){
            $mid = input('post.mid/d');
            $amount = input('post.amount/f');
            $remark = input('post.remark');
            $type = input('post.type');
            
            if(!$mid || !$amount || !$remark){
                return json(['status'=>0,'msg'=>'参数不完整']);
            }
            
            // 检查用户是否存在
            $member = Db::name('member')->where('aid',aid)->where('id',$mid)->find();
            if(!$member){
                return json(['status'=>0,'msg'=>'用户不存在']);
            }
            
            // 调用酒币操作方法
            $result = \app\common\WineCoin::addWineCoin(aid, $mid, $amount, $remark, $type);
            
            if($result['status'] == 1){
                \app\common\System::plog('手动调整会员酒币：用户ID'.$mid.'，金额'.$amount.'，备注：'.$remark);
                return json(['status'=>1,'msg'=>'操作成功']);
            }else{
                return json(['status'=>0,'msg'=>$result['msg']]);
            }
        }
        
        $mid = input('param.mid/d');
        $member = null;
        if($mid){
            $member = Db::name('member')->where('aid',aid)->where('id',$mid)->find();
        }
        
        View::assign('member',$member);
        return View::fetch();
    }

    /**
     * 酒币转账记录
     */
    public function transferlog(){
        if(request()->isAjax()){
            $page = input('param.page');
            $limit = input('param.limit');
            $order = 'id desc';
            
            $where = [];
            $where[] = ['aid','=',aid];
            
            if(input('param.from_mid')) $where[] = ['from_mid','=',input('param.from_mid')];
            if(input('param.to_mid')) $where[] = ['to_mid','=',input('param.to_mid')];
            if(input('param.status')) $where[] = ['status','=',input('param.status')];
            if(input('param.ctime')){
                $ctime = explode(' ~ ',input('param.ctime'));
                $where[] = ['createtime','>=',strtotime($ctime[0])];
                $where[] = ['createtime','<',strtotime($ctime[1]) + 86400];
            }
            
            $count = Db::name('wine_coin_transfer')->where($where)->count();
            $data = Db::name('wine_coin_transfer')->where($where)->page($page,$limit)->order($order)->select()->toArray();
            
            foreach($data as $k=>$v){
                $data[$k]['createtime'] = date('Y-m-d H:i:s',$v['createtime']);
                $data[$k]['complete_time'] = $v['complete_time'] ? date('Y-m-d H:i:s',$v['complete_time']) : '';
                
                // 获取转出用户信息
                if($v['from_mid']){
                    $from_member = Db::name('member')->where('id',$v['from_mid'])->find();
                    $data[$k]['from_nickname'] = $from_member ? $from_member['nickname'] : '用户不存在';
                    $data[$k]['from_tel'] = $from_member ? $from_member['tel'] : '';
                }
                
                // 获取转入用户信息
                if($v['to_mid']){
                    $to_member = Db::name('member')->where('id',$v['to_mid'])->find();
                    $data[$k]['to_nickname'] = $to_member ? $to_member['nickname'] : '用户不存在';
                    $data[$k]['to_tel'] = $to_member ? $to_member['tel'] : '';
                }
                
                // 状态文本
                $status_text = ['待处理','成功','失败'];
                $data[$k]['status_text'] = $status_text[$v['status']] ?? '未知';
                $data[$k]['status_color'] = ['orange','green','red'][$v['status']] ?? 'gray';
            }
            
            return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$data]);
        }
        
        return View::fetch();
    }

    /**
     * 酒币充值记录
     */
    public function rechargelog(){
        if(request()->isAjax()){
            $page = input('param.page');
            $limit = input('param.limit');
            $order = 'id desc';
            
            $where = [];
            $where[] = ['aid','=',aid];
            
            if(input('param.mid')) $where[] = ['mid','=',input('param.mid')];
            if(input('param.status')) $where[] = ['status','=',input('param.status')];
            if(input('param.paytype')) $where[] = ['paytype','=',input('param.paytype')];
            if(input('param.ctime')){
                $ctime = explode(' ~ ',input('param.ctime'));
                $where[] = ['createtime','>=',strtotime($ctime[0])];
                $where[] = ['createtime','<',strtotime($ctime[1]) + 86400];
            }
            
            $count = Db::name('winecoin_recharge_order')->where($where)->count();
            $data = Db::name('winecoin_recharge_order')->where($where)->page($page,$limit)->order($order)->select()->toArray();
            
            foreach($data as $k=>$v){
                $data[$k]['createtime'] = date('Y-m-d H:i:s',$v['createtime']);
                $data[$k]['pay_time'] = $v['pay_time'] ? date('Y-m-d H:i:s',$v['pay_time']) : '';
                
                // 获取用户信息
                if($v['mid']){
                    $member = Db::name('member')->where('id',$v['mid'])->find();
                    $data[$k]['nickname'] = $member ? $member['nickname'] : '用户不存在';
                    $data[$k]['tel'] = $member ? $member['tel'] : '';
                }
                
                // 状态文本
                $status_text = ['待支付','已支付','已取消'];
                $data[$k]['status_text'] = $status_text[$v['status']] ?? '未知';
                $data[$k]['status_color'] = ['orange','green','red'][$v['status']] ?? 'gray';
                
                // 支付方式文本
                $paytype_text = [
                    'wxpay' => '微信支付',
                    'alipay' => '支付宝支付',
                    'balance' => '余额支付',
                    'offline' => '线下支付'
                ];
                $data[$k]['paytype_text'] = $paytype_text[$v['paytype']] ?? $v['paytype'];
            }
            
            return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$data]);
        }
        
        return View::fetch();
    }

    /**
     * 酒币提现记录
     */
    public function withdrawlog(){
        if(request()->isAjax()){
            $page = input('page', 1);
            $limit = input('limit', 20);
            $order = 'id desc';

            $where = [];
            $where[] = ['aid','=',aid];

            if(input('mid')) $where[] = ['mid','=',input('mid')];
            if(input('status') !== '') $where[] = ['status','=',input('status')];
            if(input('ctime')){
                $ctime = explode(' ~ ',input('ctime'));
                $where[] = ['createtime','>=',strtotime($ctime[0])];
                $where[] = ['createtime','<',strtotime($ctime[1]) + 86400];
            }

            $count = Db::name('wine_coin_withdraw')->where($where)->count();
            $data = Db::name('wine_coin_withdraw')->where($where)->page($page,$limit)->order($order)->select()->toArray();
            
            foreach($data as $k=>$v){
                $data[$k]['createtime'] = date('Y-m-d H:i:s',$v['createtime']);
                $data[$k]['complete_time'] = $v['complete_time'] ? date('Y-m-d H:i:s',$v['complete_time']) : '';
                $data[$k]['audit_time'] = $v['audit_time'] ? date('Y-m-d H:i:s',$v['audit_time']) : '';
                
                // 获取用户信息（包含头像）
                if($v['mid']){
                    $member = Db::name('member')->where('id',$v['mid'])->field('id,nickname,tel,headimg,realname')->find();
                    $data[$k]['nickname'] = $member ? $member['nickname'] : '用户不存在';
                    $data[$k]['tel'] = $member ? $member['tel'] : '';
                    $data[$k]['headimg'] = $member ? $member['headimg'] : '';
                    $data[$k]['realname'] = $member ? $member['realname'] : '';
                } else {
                    $data[$k]['nickname'] = '用户不存在';
                    $data[$k]['tel'] = '';
                    $data[$k]['headimg'] = '';
                    $data[$k]['realname'] = '';
                }
                
                // 手续费显示
                $data[$k]['fee'] = number_format($v['fee_amount'], 2);
                
                // 构建账户信息显示
                $account_info = '';
                if($v['bank_type'] == 'alipay') {
                    $account_info = '支付宝：' . $v['bank_account'];
                } elseif($v['bank_type'] == 'bank') {
                    $account_info = $v['bank_name'] . '：' . substr($v['bank_account'], 0, 4) . '****' . substr($v['bank_account'], -4);
                }
                if($v['account_name']) {
                    $account_info .= '（' . $v['account_name'] . '）';
                }
                $data[$k]['account_info'] = $account_info;
                
                // 状态文本和颜色
                $status_text = ['待审核','审核通过','审核拒绝','已完成'];
                $data[$k]['status_text'] = $status_text[$v['status']] ?? '未知';
                $data[$k]['status_color'] = ['orange','blue','red','green'][$v['status']] ?? 'gray';
                
                // 格式化金额显示
                $data[$k]['amount'] = number_format($v['amount'], 2);
                $data[$k]['actual_amount'] = number_format($v['actual_amount'], 2);
            }
            
            return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$data]);
        }
        
        return View::fetch();
    }

    /**
     * 测试提现数据
     */
    public function testWithdrawData(){
        $where = [];
        $where[] = ['aid','=',aid];

        $count = Db::name('wine_coin_withdraw')->where($where)->count();
        $data = Db::name('wine_coin_withdraw')->where($where)->limit(5)->order('id desc')->select()->toArray();

        return json([
            'aid' => aid,
            'count' => $count,
            'data' => $data,
            'where' => $where
        ]);
    }

    /**
     * 处理提现申请
     */
    public function processWithdraw(){
        if(request()->isPost()){
            $id = input('post.id/d');
            $status = input('post.status/d');
            $remark = input('post.remark');
            
            if($status == 3) {
                // 标记完成
                $result = \app\common\WineCoin::completeWithdraw($id, $remark);
            } else {
                // 审核通过或拒绝
                $result = \app\common\WineCoin::processWithdraw($id, $status, $remark);
            }
            
            if($result['status'] == 1){
                $action = $status == 1 ? '审核通过' : ($status == 3 ? '标记完成' : '审核拒绝');
                \app\common\System::plog('处理酒币提现申请：ID'.$id.'，操作：'.$action.'，备注：'.$remark);
                return json(['status'=>1,'msg'=>$result['msg']]);
            }else{
                return json(['status'=>0,'msg'=>$result['msg']]);
            }
        }
    }
}
