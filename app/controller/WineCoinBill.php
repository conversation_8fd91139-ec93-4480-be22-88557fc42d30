<?php
/**
 * 酒币账单管理控制器
 * 统一管理所有酒币相关的账单记录
 */

namespace app\controller;
use think\facade\Db;
use think\facade\View;

class WineCoinBill extends Common
{
    public function initialize(){
        parent::initialize();
        // 权限检查：只有管理员可以访问
        if($this->user['bid'] > 0) showmsg('无访问权限');
    }

    /**
     * 酒币账单列表 - 主页面
     */
    public function index(){
        if(request()->isAjax()){
            $page = input('param.page');
            $limit = input('param.limit');
            // 处理排序
            if(input('param.field') && input('param.order')){
                $field = input('param.field');
                $orderType = input('param.order');
                // 字段名映射，防止SQL注入
                $fieldMap = [
                    'id' => 'id',
                    'amount' => 'amount', 
                    'after' => 'balance_after',
                    'createtime' => 'createtime'
                ];
                if(isset($fieldMap[$field])){
                    $order = $fieldMap[$field] . ' ' . ($orderType == 'asc' ? 'asc' : 'desc');
                } else {
                    $order = 'id desc';
                }
            } else {
                $order = 'id desc'; // 默认按ID降序
            }
            
            $where = [];
            $where[] = ['aid','=',aid];
            
            // 搜索条件
            if(input('param.bill_type')) {
                $bill_type = input('param.bill_type');
                $where[] = ['type','=', $bill_type];
            }
            // 用户相关搜索
            if(input('param.mid')) $where[] = ['mid','=',input('param.mid')];
            if(input('param.nickname')){
                // 根据昵称搜索会员ID
                $memberIds = Db::name('member')->where('aid',aid)->where('nickname','like','%'.input('param.nickname').'%')->column('id');
                if($memberIds) $where[] = ['mid','in',$memberIds];
                else $where[] = ['mid','=',-1];
            }
            if(input('param.member_tel')){
                // 根据手机号搜索会员ID
                $memberIds = Db::name('member')->where('aid',aid)->where('tel','like','%'.input('param.member_tel').'%')->column('id');
                if($memberIds) $where[] = ['mid','in',$memberIds];
                else $where[] = ['mid','=',-1];
            }
            // 商户相关搜索
            if(input('param.bid')) $where[] = ['bid','=',input('param.bid')];
            if(input('param.business_name')){
                // 根据商户名称搜索商户ID
                $businessIds = Db::name('business')->where('aid',aid)->where('name','like','%'.input('param.business_name').'%')->column('id');
                if($businessIds) $where[] = ['bid','in',$businessIds];
                else $where[] = ['bid','=',-1];
            }
            if(input('param.business_tel')){
                // 根据手机号搜索商户ID
                $businessIds = Db::name('business')->where('aid',aid)->where('tel','like','%'.input('param.business_tel').'%')->column('id');
                if($businessIds) $where[] = ['bid','in',$businessIds];
                else $where[] = ['bid','=',-1];
            }
            // 金额范围搜索
            if(input('param.amount_min')) {
                $min = input('param.amount_min');
                $where[] = ['amount','>=', $min];
                $where[] = ['amount','<=', -$min, '', 'OR'];
            }
            if(input('param.amount_max')) {
                $max = input('param.amount_max');
                $where[] = ['amount','<=', $max];
                $where[] = ['amount','>=', -$max];
            }
            // 时间范围搜索
            if(input('param.ctime')){
                $ctime = explode(' ~ ',input('param.ctime'));
                $where[] = ['createtime','>=',strtotime($ctime[0])];
                $where[] = ['createtime','<',strtotime($ctime[1]) + 86400];
            }
            
            $count = Db::name('wine_coin_log')->where($where)->count();
            $data = Db::name('wine_coin_log')->where($where)->page($page,$limit)->order($order)->select()->toArray();

            // 确保数据唯一性，防止重复
            $unique_data = [];
            $seen_ids = [];
            foreach($data as $item) {
                if(!in_array($item['id'], $seen_ids)) {
                    $unique_data[] = $item;
                    $seen_ids[] = $item['id'];
                }
            }
            $data = $unique_data;

            // 使用统一的类型映射
            $type_map = \app\common\WineCoinType::getTypeMap();

            // 使用统一的关联类型映射
            $relation_type_map = \app\common\WineCoinType::getRelationTypeMap();

            // 使用新的数组来确保正确的数据结构
            $result_data = [];
            foreach($data as $k=>$v){
                // 创建新的数据项，确保包含原始ID
                $item = [];
                $item['id'] = $v['id']; // 确保ID正确传递
                $item['aid'] = $v['aid'];
                $item['mid'] = $v['mid'];
                $item['bid'] = $v['bid'];
                $item['from_mid'] = $v['from_mid'];
                $item['to_mid'] = $v['to_mid'];
                $item['from_bid'] = $v['from_bid'];
                $item['to_bid'] = $v['to_bid'];
                $item['amount'] = $v['amount'];
                $item['balance_after'] = $v['balance_after'];
                $item['type'] = $v['type'];
                $item['relation_type'] = $v['relation_type'];
                $item['relation_id'] = $v['relation_id'];
                $item['remark'] = $v['remark'];
                $item['createtime'] = date('Y-m-d H:i:s',$v['createtime']);

                // 获取用户信息 - 包含头像、昵称、ID
                if($v['mid']){
                    $member = Db::name('member')->where('aid',aid)->where('id',$v['mid'])->field('id,nickname,tel,headimg')->find();
                    if($member){
                        $item['member_id'] = $member['id'];
                        $item['member_nickname'] = $member['nickname'];
                        $item['member_tel'] = $member['tel'];
                        $item['member_headimg'] = $member['headimg'] ?: '/static/img/default_avatar.png';
                        $item['member_info'] = 'ID:'.$member['id'].'<br/>昵称:'.$member['nickname'].'<br/>手机:'.$member['tel'];
                    } else {
                        $item['member_info'] = '用户不存在(ID:'.$v['mid'].')';
                        $item['member_id'] = $v['mid'];
                        $item['member_nickname'] = '用户不存在';
                    }
                }

                // 获取商户信息 - 包含头像、名称、ID
                if($v['bid']){
                    $business = Db::name('business')->where('aid',aid)->where('id',$v['bid'])->field('id,name,logo,tel')->find();
                    if($business){
                        $item['business_id'] = $business['id'];
                        $item['business_name'] = $business['name'];
                        $item['business_tel'] = $business['tel'];
                        $item['business_headimg'] = $business['logo'] ?: '/static/img/default_avatar.png';
                        $item['business_info'] = 'ID:'.$business['id'].'<br/>名称:'.$business['name'].'<br/>手机:'.$business['tel'];
                    } else {
                        $item['business_info'] = '商户不存在(ID:'.$v['bid'].')';
                        $item['business_id'] = $v['bid'];
                        $item['business_name'] = '商户不存在';
                    }
                }

                // 获取来源用户信息
                if($v['from_mid']){
                    $from_member = Db::name('member')->where('aid',aid)->where('id',$v['from_mid'])->field('nickname,tel')->find();
                    $item['from_nickname'] = $from_member ? $from_member['nickname'] : '来源用户不存在';
                }

                // 获取目标用户信息
                if($v['to_mid']){
                    $to_member = Db::name('member')->where('aid',aid)->where('id',$v['to_mid'])->find();
                    $item['to_nickname'] = $to_member ? $to_member['nickname'] : '目标用户不存在';
                }

                // 获取来源商户信息
                if($v['from_bid']){
                    $from_business = Db::name('business')->where('aid',aid)->where('id',$v['from_bid'])->find();
                    $item['from_business_name'] = $from_business ? $from_business['name'] : '来源商户不存在';
                }

                // 获取目标商户信息
                if($v['to_bid']){
                    $to_business = Db::name('business')->where('aid',aid)->where('id',$v['to_bid'])->find();
                    $item['to_business_name'] = $to_business ? $to_business['name'] : '目标商户不存在';
                }

                // 格式化金额显示
                $item['amount_text'] = ($v['amount'] > 0 ? '+' : '') . $v['amount'];
                $item['amount_color'] = $v['amount'] > 0 ? 'green' : 'red';
                
                // 映射字段名称
                $item['after'] = $v['balance_after']; // 将balance_after映射为after

                // 类型中文显示
                $item['type_text'] = $type_map[$v['type']] ?? $v['type'];
                $item['relation_type_text'] = $relation_type_map[$v['relation_type']] ?? $v['relation_type'];

                // 构建账单描述
                $description = $item['type_text'];
                if($v['relation_type'] && $v['relation_id']) {
                    $description .= ' (' . $item['relation_type_text'] . ':' . $v['relation_id'] . ')';
                }
                $item['description'] = $description;

                // 构建参与方信息
                $participants = [];
                if($v['mid'] && isset($item['member_nickname'])) {
                    $participants[] = '用户:' . $item['member_nickname'];
                }
                if($v['bid'] && isset($item['business_name'])) {
                    $participants[] = '商户:' . $item['business_name'];
                }
                if($v['from_mid'] && isset($item['from_nickname'])) {
                    $participants[] = '转出用户:' . $item['from_nickname'];
                }
                if($v['to_mid'] && isset($item['to_nickname'])) {
                    $participants[] = '转入用户:' . $item['to_nickname'];
                }
                if($v['from_bid'] && isset($item['from_business_name'])) {
                    $participants[] = '转出商户:' . $item['from_business_name'];
                }
                if($v['to_bid'] && isset($item['to_business_name'])) {
                    $participants[] = '转入商户:' . $item['to_business_name'];
                }
                $item['participants'] = implode(' | ', $participants);
                
                // 添加到结果数组
                $result_data[] = $item;
            }



            return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$result_data]);
        }
        
        return View::fetch();
    }

    /**
     * 导出酒币账单数据
     */
    public function export(){
        set_time_limit(0);
        ini_set('memory_limit', '2000M');
        
        $where = [];
        $where[] = ['aid','=',aid];
        
        // 搜索条件（与列表页面相同的过滤条件）
        if(input('param.bill_type')) {
            $bill_type = input('param.bill_type');
            $where[] = ['type','=', $bill_type];
        }
        if(input('param.mid')) $where[] = ['mid','=',input('param.mid')];
        if(input('param.bid')) $where[] = ['bid','=',input('param.bid')];
        if(input('param.amount_min')) {
            $min = input('param.amount_min');
            $where[] = ['amount','>=', $min];
            $where[] = ['amount','<=', -$min, '', 'OR'];
        }
        if(input('param.amount_max')) {
            $max = input('param.amount_max');
            $where[] = ['amount','<=', $max];
            $where[] = ['amount','>=', -$max];
        }
        if(input('param.ctime')){
            $ctime = explode(' ~ ',input('param.ctime'));
            $where[] = ['createtime','>=',strtotime($ctime[0])];
            $where[] = ['createtime','<',strtotime($ctime[1]) + 86400];
        }
        
        $list = Db::name('wine_coin_log')->where($where)->order('id desc')->limit(10000)->select()->toArray(); // 添加限制，避免大量数据导出
        
        // 使用统一的类型映射
        $type_map = \app\common\WineCoinType::getTypeMap();
        
        $title = array();
        $title[] = '账单ID';
        $title[] = '用户信息';
        $title[] = '商户信息';
        $title[] = '账单类型';
        $title[] = '变动金额';
        $title[] = '变动后余额';
        $title[] = '账单描述';
        $title[] = '关联类型';
        $title[] = '关联ID';
        $title[] = '创建时间';
        
        $data = array();
        foreach($list as $v){
            $tdata = array();
            $tdata[] = $v['id'];
            
            // 用户信息
            $member_info = '';
            if($v['mid']){
                $member = Db::name('member')->where('aid',aid)->where('id',$v['mid'])->field('id,nickname,tel')->find();
                if($member){
                    $member_info = 'ID:'.$member['id'].' 昵称:'.$member['nickname'].' 手机:'.$member['tel'];
                } else {
                    $member_info = '用户不存在(ID:'.$v['mid'].')';
                }
            }
            $tdata[] = $member_info;
            
            // 商户信息
            $business_info = '';
            if($v['bid']){
                $business = Db::name('business')->where('aid',aid)->where('id',$v['bid'])->field('id,name,tel')->find();
                if($business){
                    $business_info = 'ID:'.$business['id'].' 名称:'.$business['name'].' 手机:'.$business['tel'];
                } else {
                    $business_info = '商户不存在(ID:'.$v['bid'].')';
                }
            }
            $tdata[] = $business_info;
            
            $tdata[] = $type_map[$v['type']] ?? $v['type'];
            $tdata[] = $v['amount'];
            $tdata[] = $v['balance_after']; // 使用正确的数据库字段名
            $tdata[] = $v['remark'];
            $tdata[] = $v['relation_type'];
            $tdata[] = $v['relation_id'];
            $tdata[] = date('Y-m-d H:i:s',$v['createtime']);
            
            $data[] = $tdata;
        }
        
        $this->export_excel($title,$data);
    }

    /**
     * 酒币账单统计
     */
    public function statistics(){
        // 今日统计
        $today_start = strtotime(date('Y-m-d 00:00:00'));
        $today_end = strtotime(date('Y-m-d 23:59:59'));
        
        // 本月统计
        $month_start = strtotime(date('Y-m-01 00:00:00'));
        $month_end = strtotime(date('Y-m-t 23:59:59'));
        
        // 今日各类型统计
        $today_stats = [];
        $today_stats['recharge'] = Db::name('wine_coin_log')->where('aid',aid)->where('type','recharge')->where('createtime','between',[$today_start,$today_end])->sum('amount');
        $today_stats['withdraw'] = abs(Db::name('wine_coin_log')->where('aid',aid)->where('type','withdraw')->where('createtime','between',[$today_start,$today_end])->sum('amount'));
        $today_stats['transfer'] = (Db::name('wine_coin_log')->where('aid',aid)->where('type','in',['transfer_out','transfer_in'])->where('createtime','between',[$today_start,$today_end])->where('amount','>',0)->sum('amount') + abs(Db::name('wine_coin_log')->where('aid',aid)->where('type','in',['transfer_out','transfer_in'])->where('createtime','between',[$today_start,$today_end])->where('amount','<',0)->sum('amount'))) / 2; // 转账需要除以2避免重复计算
        $today_stats['payment'] = abs(Db::name('wine_coin_log')->where('aid',aid)->where('type','payment')->where('createtime','between',[$today_start,$today_end])->sum('amount'));
        
        // 本月各类型统计
        $month_stats = [];
        $month_stats['recharge'] = Db::name('wine_coin_log')->where('aid',aid)->where('type','recharge')->where('createtime','between',[$month_start,$month_end])->sum('amount');
        $month_stats['withdraw'] = abs(Db::name('wine_coin_log')->where('aid',aid)->where('type','withdraw')->where('createtime','between',[$month_start,$month_end])->sum('amount'));
        $month_stats['transfer'] = (Db::name('wine_coin_log')->where('aid',aid)->where('type','in',['transfer_out','transfer_in'])->where('createtime','between',[$month_start,$month_end])->where('amount','>',0)->sum('amount') + abs(Db::name('wine_coin_log')->where('aid',aid)->where('type','in',['transfer_out','transfer_in'])->where('createtime','between',[$month_start,$month_end])->where('amount','<',0)->sum('amount'))) / 2;
        $month_stats['payment'] = abs(Db::name('wine_coin_log')->where('aid',aid)->where('type','payment')->where('createtime','between',[$month_start,$month_end])->sum('amount'));
        
        // 总体统计
        $total_stats = [];
        $total_stats['total_users'] = Db::name('member')->where('aid',aid)->where('wine_coin','>',0)->count();
        $total_stats['total_business'] = Db::name('business')->where('aid',aid)->where('wine_coin','>',0)->count();
        $total_stats['total_member_balance'] = Db::name('member')->where('aid',aid)->sum('wine_coin');
        $total_stats['total_business_balance'] = Db::name('business')->where('aid',aid)->sum('wine_coin');
        $total_stats['total_bills'] = Db::name('wine_coin_log')->where('aid',aid)->count();
        
        // 账单类型分布统计
        $type_distribution = Db::name('wine_coin_log')
            ->where('aid',aid)
            ->field('type, count(*) as count, sum(abs(amount)) as amount')
            ->group('type')
            ->select()
            ->toArray();
        
        $statistics = [
            'today_stats' => $today_stats,
            'month_stats' => $month_stats,
            'total_stats' => $total_stats,
            'type_distribution' => $type_distribution
        ];
        
        if(request()->isAjax()){
            return json(['status'=>1,'data'=>$statistics]);
        }
        
        View::assign('statistics',$statistics);
        return View::fetch();
    }

    /**
     * 查看账单详情
     */
    public function detail(){
        $id = input('param.id/d');
        if(!$id) return json(['status'=>0,'msg'=>'参数错误']);
        
        $bill = Db::name('wine_coin_log')->where('aid',aid)->where('id',$id)->find();
        if(!$bill) return json(['status'=>0,'msg'=>'账单不存在']);
        
        // 获取相关信息
        if($bill['mid']){
            $member = Db::name('member')->where('aid',aid)->where('id',$bill['mid'])->find();
            $bill['member_info'] = $member;
        }
        
        if($bill['bid']){
            $business = Db::name('business')->where('aid',aid)->where('id',$bill['bid'])->find();
            $bill['business_info'] = $business;
        }
        
        if($bill['from_mid']){
            $from_member = Db::name('member')->where('aid',aid)->where('id',$bill['from_mid'])->find();
            $bill['from_member_info'] = $from_member;
        }
        
        if($bill['to_mid']){
            $to_member = Db::name('member')->where('aid',aid)->where('id',$bill['to_mid'])->find();
            $bill['to_member_info'] = $to_member;
        }
        
        if($bill['from_bid']){
            $from_business = Db::name('business')->where('aid',aid)->where('id',$bill['from_bid'])->find();
            $bill['from_business_info'] = $from_business;
        }
        
        if($bill['to_bid']){
            $to_business = Db::name('business')->where('aid',aid)->where('id',$bill['to_bid'])->find();
            $bill['to_business_info'] = $to_business;
        }
        
        // 根据关联类型获取关联数据
        if($bill['relation_type'] && $bill['relation_id']){
            switch($bill['relation_type']){
                case 'order':
                    // 获取订单信息
                    $order = Db::name('shop_order')->where('id',$bill['relation_id'])->find();
                    $bill['relation_data'] = $order;
                    break;
                case 'transfer':
                    // 获取转账信息
                    $transfer = Db::name('wine_coin_transfer')->where('id',$bill['relation_id'])->find();
                    $bill['relation_data'] = $transfer;
                    break;
                case 'withdraw':
                    // 获取提现信息
                    $withdraw = Db::name('wine_coin_withdraw')->where('id',$bill['relation_id'])->find();
                    $bill['relation_data'] = $withdraw;
                    break;
                case 'recharge':
                    // 获取充值信息
                    $recharge = Db::name('winecoin_recharge_order')->where('id',$bill['relation_id'])->find();
                    $bill['relation_data'] = $recharge;
                    break;
            }
        }
        
        $bill['createtime_format'] = date('Y-m-d H:i:s', $bill['createtime']);
        
        return json(['status'=>1,'data'=>$bill]);
    }
} 