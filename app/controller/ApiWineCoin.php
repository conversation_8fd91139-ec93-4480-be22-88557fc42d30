<?php
/**
 * 酒币功能API控制器
 * 提供酒币钱包、充值、提现、转账、流水等功能接口
 */

namespace app\controller;
use think\facade\Db;
use think\facade\Log;

class ApiWineCoin extends ApiCommon
{
    public function initialize(){
        parent::initialize();
        $this->checklogin();
    }

    /**
     * 酒币钱包首页
     * 获取用户酒币余额、兑换比例、最近交易记录等
     */
    public function wallet()
    {
        try {
            // 获取用户信息
            $userinfo = Db::name('member')->where('id', mid)->field('id,nickname,headimg,wine_coin')->find();
            if (!$userinfo) {
                return $this->json(['status' => 0, 'msg' => '用户不存在']);
            }

            // 确保酒币字段不为空
            if (!isset($userinfo['wine_coin'])) {
                $userinfo['wine_coin'] = '0.00';
            }

            // 格式化酒币余额
            $userinfo['wine_coin'] = number_format($userinfo['wine_coin'], 2, '.', '');

            // 获取酒币兑换比例（默认1:1）
            $wineCoinRate = 1.0; // 可以从系统设置中获取
            $adminSet = Db::name('admin_set')->where('aid', aid)->find();
            if ($adminSet && isset($adminSet['wine_coin_rate'])) {
                $wineCoinRate = floatval($adminSet['wine_coin_rate']);
            }

            // 获取最近5条交易记录
            $recentLogs = Db::name('wine_coin_log')
                ->where('aid', aid)
                ->where('mid', mid)
                ->field('id,type,amount,balance_after,remark,createtime')
                ->order('createtime desc')
                ->limit(5)
                ->select()
                ->toArray();

            // 格式化交易记录
            foreach ($recentLogs as &$log) {
                $log['createtime'] = date('m-d H:i', $log['createtime']);
                $log['amount'] = number_format($log['amount'], 2, '.', '');
            }

            $data = [
                'status' => 1,
                'msg' => '获取成功',
                'userinfo' => $userinfo,
                'wine_coin_rate' => $wineCoinRate,
                'recent_logs' => $recentLogs
            ];

            return $this->json($data);

        } catch (\Exception $e) {
            Log::error('酒币钱包数据获取失败: ' . $e->getMessage());
            return $this->json(['status' => 0, 'msg' => '获取数据失败']);
        }
    }

    /**
     * 酒币充值页面数据
     */
    public function recharge()
    {
        try {
            // 获取用户信息
            $userinfo = Db::name('member')->where('id', mid)->field('id,nickname,headimg,wine_coin')->find();
            if (!$userinfo) {
                return $this->json(['status' => 0, 'msg' => '用户不存在']);
            }

            // 确保酒币字段不为空
            if (!isset($userinfo['wine_coin'])) {
                $userinfo['wine_coin'] = '0.00';
            }
            $userinfo['wine_coin'] = number_format($userinfo['wine_coin'], 2, '.', '');

            // 从酒币充值套餐配置表获取设置
            $packageConfig = Db::name('winecoin_recharge_package')->where('aid', aid)->find();
            
            if (!$packageConfig || $packageConfig['status'] != 1) {
                return $this->json(['status' => 0, 'msg' => '酒币充值功能未开启']);
            }
            
            // 解析充值套餐数据
            $givesets = [];
            if ($packageConfig['givedata']) {
                $packages = json_decode($packageConfig['givedata'], true) ?: [];
                foreach ($packages as $package) {
                    $givesets[] = [
                        'money' => floatval($package['money']),
                        'give' => floatval($package['give'] ?? 0),
                        'give_score' => floatval($package['give_score'] ?? 0)
                    ];
                }
            }

            // 构建充值设置
            $rechargeSet = [
                'can_recharge' => $packageConfig['status'],
                'can_input' => $packageConfig['can_input'],
                'min_amount' => floatval($packageConfig['min_amount']),
                'max_amount' => floatval($packageConfig['max_amount']),
                'give_sets' => $givesets,
                'recharge_desc' => $packageConfig['recharge_desc'] ?? '支持微信支付、支付宝支付，实时到账'
            ];

            $data = [
                'status' => 1,
                'msg' => '获取成功',
                'userinfo' => $userinfo,
                'recharge_set' => $rechargeSet
            ];

            return $this->json($data);

        } catch (\Exception $e) {
            Log::error('酒币充值数据获取失败: ' . $e->getMessage());
            return $this->json(['status' => 0, 'msg' => '获取数据失败']);
        }
    }

    /**
     * 创建酒币充值订单
     */
    public function createRechargeOrder()
    {
        if(!request()->isPost()) {
            return $this->json(['status' => 0, 'msg' => '请使用POST请求']);
        }

        try {
            $money = input('post.money/f', 0);
            $give_amount = input('post.give/f', 0);
            $give_score = input('post.give_score/d', 0);

            // 验证金额
            if($money <= 0) {
                return $this->json(['status' => 0, 'msg' => '充值金额必须大于0']);
            }

            // 获取充值配置
            $packageConfig = Db::name('winecoin_recharge_package')->where('aid', aid)->find();
            if (!$packageConfig || $packageConfig['status'] != 1) {
                return $this->json(['status' => 0, 'msg' => '酒币充值功能未开启']);
            }

            // 验证金额限制
            if($money < $packageConfig['min_amount']) {
                return $this->json(['status' => 0, 'msg' => '充值金额不能低于' . $packageConfig['min_amount'] . '元']);
            }
            if($money > $packageConfig['max_amount']) {
                return $this->json(['status' => 0, 'msg' => '充值金额不能超过' . $packageConfig['max_amount'] . '元']);
            }

            // 计算酒币数量（1:1兑换）
            $wine_coin_amount = $money;
            $total_wine_coin = $wine_coin_amount + $give_amount;

            // 生成订单号
            $ordernum = 'WC' . date('YmdHis') . aid . rand(1000, 9999);

            // 创建酒币充值订单
            $orderData = [
                'aid' => aid,
                'mid' => mid,
                'ordernum' => $ordernum,
                'payorderid' => 0,
                'amount' => $money,
                'wine_coin_amount' => $wine_coin_amount,
                'bonus_amount' => $give_amount,
                'bonus_score' => $give_score,
                'total_wine_coin' => $total_wine_coin,
                'status' => 0,
                'pay_time' => 0,
                'paytype' => '',
                'paytypeid' => 0,
                'paynum' => '',
                'platform' => defined('platform') ? platform : (input('param.platform') ?: 'wx'),
                'createtime' => time()
            ];

            $orderId = Db::name('winecoin_recharge_order')->insertGetId($orderData);

            if(!$orderId) {
                return $this->json(['status' => 0, 'msg' => '创建充值订单失败']);
            }

            // 创建支付订单
            $title = '酒币充值';
            if($give_amount > 0) {
                $title .= '（赠送' . $give_amount . '酒币）';
            }

            $payorderid = \app\model\Payorder::createorder(
                aid,
                0, // bid
                mid,
                'winecoin_recharge', // 支付类型
                $orderId,
                $ordernum,
                $title,
                $money
            );

            if(!$payorderid) {
                // 删除已创建的充值订单
                Db::name('winecoin_recharge_order')->where('id', $orderId)->delete();
                return $this->json(['status' => 0, 'msg' => '创建支付订单失败']);
            }

            // 更新充值订单的payorderid
            Db::name('winecoin_recharge_order')->where('id', $orderId)->update(['payorderid' => $payorderid]);

            return $this->json([
                'status' => 1,
                'msg' => '订单创建成功',
                'orderid' => $orderId,
                'payorderid' => $payorderid,
                'ordernum' => $ordernum
            ]);

        } catch (\Exception $e) {
            Log::error('创建酒币充值订单失败: ' . $e->getMessage());
            return $this->json(['status' => 0, 'msg' => '创建订单失败，请稍后重试']);
        }
    }

    /**
     * 酒币提现页面数据
     */
    public function withdraw()
    {
        try {
            // 获取用户信息
            $userinfo = Db::name('member')->where('id', mid)->field('id,nickname,headimg,wine_coin,realname,aliaccount,bankname,bankcarduser,bankcardnum')->find();
            if (!$userinfo) {
                return $this->json(['status' => 0, 'msg' => '用户不存在']);
            }

            // 确保酒币字段不为空
            if (!isset($userinfo['wine_coin'])) {
                $userinfo['wine_coin'] = '0.00';
            }
            $userinfo['wine_coin'] = number_format($userinfo['wine_coin'], 2, '.', '');

            // 获取酒币系统设置
            $wineCoinSetting = \app\common\WineCoin::getSetting(aid);
            
            // 构建提现设置数据
            $withdrawSet = [
                'can_withdraw' => $wineCoinSetting['withdraw_enable'], // 是否允许提现
                'min_amount' => floatval($wineCoinSetting['min_withdraw_amount']),  // 最小提现金额
                'max_amount' => floatval($wineCoinSetting['max_withdraw_amount']), // 最大提现金额
                'daily_limit' => floatval($wineCoinSetting['daily_withdraw_limit']), // 每日提现限额
                'fee_enable' => $wineCoinSetting['withdraw_fee_enable'], // 手续费开关
                'fee_rate' => floatval($wineCoinSetting['withdraw_fee_rate']),  // 手续费率（原始值）
                'fee_rate_percent' => floatval($wineCoinSetting['withdraw_fee_rate']) * 100, // 手续费率（百分比显示）
                'fee_min' => floatval($wineCoinSetting['withdraw_fee_min']),    // 最小手续费
                'fee_max' => floatval($wineCoinSetting['withdraw_fee_max']),     // 最大手续费
                'auto_audit' => $wineCoinSetting['withdraw_auto_audit'], // 自动审核
                'withdraw_desc' => '提现将收取' . (floatval($wineCoinSetting['withdraw_fee_rate']) * 100) . '%手续费，' . ($wineCoinSetting['withdraw_auto_audit'] ? '审核通过后' : '1-3个工作日') . '到账',
                'withdraw_methods' => ['alipay', 'bank'] // 支持的提现方式
            ];
            
            // 如果手续费功能关闭，设置手续费为0
            if (!$withdrawSet['fee_enable']) {
                $withdrawSet['fee_rate'] = 0;
                $withdrawSet['fee_rate_percent'] = 0;
                $withdrawSet['fee_min'] = 0;
                $withdrawSet['fee_max'] = 0;
                $withdrawSet['withdraw_desc'] = '提现无手续费，' . ($wineCoinSetting['withdraw_auto_audit'] ? '审核通过后' : '1-3个工作日') . '到账';
            }

            $data = [
                'status' => 1,
                'msg' => '获取成功',
                'userinfo' => $userinfo,
                'withdraw_set' => $withdrawSet
            ];

            return $this->json($data);

        } catch (\Exception $e) {
            Log::error('获取酒币提现数据失败: ' . $e->getMessage());
            return $this->json(['status' => 0, 'msg' => '获取数据失败，请稍后重试']);
        }
    }

    /**
     * 申请酒币提现
     */
    public function applyWithdraw()
    {
        if (!request()->isPost()) {
            return $this->json(['status' => 0, 'msg' => '请使用POST请求']);
        }

        try {
            $amount = input('post.amount/f', 0);
            $bank_type = input('post.bank_type/s', '');
            $bank_account = input('post.bank_account/s', '');
            $bank_name = input('post.bank_name/s', '');
            $account_name = input('post.account_name/s', '');

            // 参数验证
            if ($amount <= 0) {
                return $this->json(['status' => 0, 'msg' => '请输入有效的提现金额']);
            }

            if (empty($bank_type)) {
                return $this->json(['status' => 0, 'msg' => '请选择提现方式']);
            }

            if (empty($bank_account)) {
                return $this->json(['status' => 0, 'msg' => '请输入收款账号']);
            }

            if (empty($account_name)) {
                return $this->json(['status' => 0, 'msg' => '请输入账户姓名']);
            }

            // 银行卡需要填写银行名称
            if ($bank_type == 'bank' && empty($bank_name)) {
                return $this->json(['status' => 0, 'msg' => '请输入银行名称']);
            }

            // 构建银行信息
            $bank_info = [
                'account' => $bank_account,
                'bank_name' => $bank_name,
                'account_name' => $account_name
            ];

            // 调用提现申请逻辑
            $result = \app\common\WineCoin::applyWithdraw(aid, mid, $amount, $bank_type, $bank_info);

            return $this->json($result);

        } catch (\Exception $e) {
            Log::error('酒币提现申请失败: ' . $e->getMessage());
            return $this->json(['status' => 0, 'msg' => '提现申请失败，请稍后重试']);
        }
    }

    /**
     * 酒币转账页面数据
     */
    public function transfer()
    {
        try {
            // 获取用户信息
            $userinfo = Db::name('member')->where('id', mid)->field('id,nickname,headimg,wine_coin')->find();
            if (!$userinfo) {
                return $this->json(['status' => 0, 'msg' => '用户不存在']);
            }

            // 确保酒币字段不为空
            if (!isset($userinfo['wine_coin'])) {
                $userinfo['wine_coin'] = '0.00';
            }
            $userinfo['wine_coin'] = number_format($userinfo['wine_coin'], 2, '.', '');

            // 获取转账设置
            $transferSet = [
                'can_transfer' => 1,   // 是否允许转账
                'min_amount' => 0.01,  // 最小转账金额
                'max_amount' => 10000, // 最大转账金额
                'fee_rate' => 0,       // 转账手续费率
                'daily_limit' => 1000, // 每日转账限额
                'transfer_desc' => '转账即时到账，请确认收款人信息'
            ];

            // 从系统设置中获取转账配置
            $adminSet = Db::name('admin_set')->where('aid', aid)->find();
            if ($adminSet) {
                if (isset($adminSet['wine_coin_transfer_min'])) {
                    $transferSet['min_amount'] = floatval($adminSet['wine_coin_transfer_min']);
                }
                if (isset($adminSet['wine_coin_transfer_max'])) {
                    $transferSet['max_amount'] = floatval($adminSet['wine_coin_transfer_max']);
                }
                if (isset($adminSet['wine_coin_transfer_daily_limit'])) {
                    $transferSet['daily_limit'] = floatval($adminSet['wine_coin_transfer_daily_limit']);
                }
            }

            // 获取今日已转账金额
            $todayStart = strtotime(date('Y-m-d'));
            $todayEnd = $todayStart + 86400;
            $todayTransfer = Db::name('wine_coin_log')
                ->where('aid', aid)
                ->where('mid', mid)
                ->where('type', 'transfer_out')
                ->where('createtime', '>=', $todayStart)
                ->where('createtime', '<', $todayEnd)
                ->sum('amount');
            $transferSet['today_transferred'] = abs($todayTransfer ?: 0);

            $data = [
                'status' => 1,
                'msg' => '获取成功',
                'userinfo' => $userinfo,
                'transfer_set' => $transferSet
            ];

            return $this->json($data);

        } catch (\Exception $e) {
            Log::error('酒币转账数据获取失败: ' . $e->getMessage());
            return $this->json(['status' => 0, 'msg' => '获取数据失败']);
        }
    }

    /**
     * 获取用户提现申请记录
     */
    public function withdrawLog()
    {
        try {
            $page = input('page/d', 1);
            $limit = input('limit/d', 20);

            $where = [];
            $where[] = ['aid', '=', aid];
            $where[] = ['mid', '=', mid];

            // 获取总数
            $count = Db::name('wine_coin_withdraw')->where($where)->count();

            // 获取记录
            $logs = Db::name('wine_coin_withdraw')
                ->where($where)
                ->field('id,amount,fee_amount,actual_amount,bank_type,bank_account,account_name,status,createtime,audit_time,complete_time,audit_remark')
                ->order('createtime desc')
                ->page($page, $limit)
                ->select()
                ->toArray();

            // 格式化记录，转换为类似流水记录的格式
            $formattedLogs = [];
            foreach ($logs as $log) {
                $formattedLogs[] = [
                    'id' => $log['id'],
                    'type' => 'withdraw',
                    'amount' => '-' . number_format($log['amount'], 2, '.', ''), // 提现显示为负数
                    'balance_after' => '', // 提现记录不显示余额
                    'createtime' => date('Y-m-d H:i:s', $log['createtime']),
                    'type_desc' => '酒币提现',
                    'remark' => $this->getWithdrawRemark($log),
                    'withdraw_detail' => $log // 保留完整的提现详情
                ];
            }

            $data = [
                'status' => 1,
                'msg' => '获取成功',
                'count' => $count,
                'page' => $page,
                'limit' => $limit,
                'logs' => $formattedLogs
            ];

            return $this->json($data);

        } catch (\Exception $e) {
            Log::error('用户提现记录获取失败: ' . $e->getMessage());
            return $this->json(['status' => 0, 'msg' => '获取数据失败']);
        }
    }

    /**
     * 生成提现记录备注
     */
    private function getWithdrawRemark($withdraw)
    {
        $statusMap = [
            0 => '待审核',
            1 => '审核通过',
            2 => '审核拒绝',
            3 => '已完成'
        ];
        
        $bankTypeMap = [
            'alipay' => '支付宝',
            'bank' => '银行卡',
            'wechat' => '微信'
        ];

        $status = $statusMap[$withdraw['status']] ?? '未知状态';
        $bankType = $bankTypeMap[$withdraw['bank_type']] ?? $withdraw['bank_type'];
        
        $remark = "提现到{$bankType}，状态：{$status}";
        
        if ($withdraw['audit_remark']) {
            $remark .= "，备注：" . $withdraw['audit_remark'];
        }
        
        return $remark;
    }

    /**
     * 酒币流水记录 - 优化版本
     */
    public function log()
    {
        try {
            $page = input('page/d', 1);
            $limit = input('limit/d', 20);
            $type = input('type', ''); // 筛选类型

            // 获取统一格式的账单记录
            $result = $this->getUnifiedBills($page, $limit, $type);

            $data = [
                'status' => 1,
                'msg' => '获取成功',
                'count' => $result['total'],
                'page' => $page,
                'limit' => $limit,
                'logs' => $result['bills']
            ];

            return $this->json($data);

        } catch (\Exception $e) {
            Log::error('酒币流水获取失败: ' . $e->getMessage());
            return $this->json(['status' => 0, 'msg' => '获取数据失败']);
        }
    }

    /**
     * 获取统一格式的账单记录
     */
    private function getUnifiedBills($page, $limit, $type = '')
    {
        $bills = [];

        // 1. 获取普通流水记录（排除提现相关的内部流水）
        $normalBills = $this->getNormalBills($type);

        // 2. 获取提现记录（合并为单条记录）
        if (empty($type) || $type == 'withdraw') {
            $withdrawBills = $this->getWithdrawBills();
            $bills = array_merge($normalBills, $withdrawBills);
        } else {
            $bills = $normalBills;
        }

        // 3. 按时间排序
        usort($bills, function($a, $b) {
            return $b['createtime_timestamp'] - $a['createtime_timestamp'];
        });

        // 4. 分页处理
        $total = count($bills);
        $offset = ($page - 1) * $limit;
        $bills = array_slice($bills, $offset, $limit);

        // 5. 格式化显示数据
        foreach ($bills as &$bill) {
            $bill['createtime'] = date('Y-m-d H:i:s', $bill['createtime_timestamp']);
            // 保持amount为数字格式，让前端自行格式化
            $bill['amount'] = floatval($bill['amount']);
            unset($bill['createtime_timestamp']); // 移除排序用的时间戳
        }

        return [
            'total' => $total,
            'bills' => $bills
        ];
    }

    /**
     * 获取普通流水记录（排除提现相关）
     */
    private function getNormalBills($type = '')
    {
        $where = [
            ['aid', '=', aid],
            ['mid', '=', mid],
            ['type', 'not in', ['withdraw_freeze', 'withdraw_success', 'withdraw_fail', 'withdraw_refund']]
        ];

        if ($type && $type != 'withdraw') {
            $where[] = ['type', '=', $type];
        }

        $logs = Db::name('wine_coin_log')
            ->where($where)
            ->field('id,type,amount,balance_after,remark,createtime,from_mid,to_mid,relation_id,relation_type')
            ->order('createtime desc')
            ->select()
            ->toArray();

        $bills = [];
        foreach ($logs as $log) {
            $bills[] = [
                'id' => 'normal_' . $log['id'],
                'type' => $log['type'],
                'type_desc' => $this->getTypeIcon($log['type']) . ' ' . $this->getTypeDesc($log['type']),
                'amount' => floatval($log['amount']),
                'balance_after' => number_format($log['balance_after'], 2, '.', ''),
                'status' => 'completed',
                'status_desc' => $this->getStatusIcon('completed') . ' 已完成',
                'createtime_timestamp' => $log['createtime'],
                'remark' => $this->formatRemark($log),
                'original_data' => $log
            ];
        }

        return $bills;
    }

    /**
     * 获取提现记录（合并为单条记录）
     */
    private function getWithdrawBills()
    {
        $withdraws = Db::name('wine_coin_withdraw')
            ->where('aid', aid)
            ->where('mid', mid)
            ->field('id,ordernum,amount,fee_amount,actual_amount,bank_type,bank_account,bank_name,account_name,status,createtime,audit_time,complete_time,audit_remark')
            ->order('createtime desc')
            ->select()
            ->toArray();

        $bills = [];
        foreach ($withdraws as $withdraw) {
            $bills[] = [
                'id' => 'withdraw_' . $withdraw['id'],
                'type' => 'withdraw',
                'type_desc' => '酒币提现',
                'amount' => -floatval($withdraw['amount']), // 提现显示为负数
                'balance_after' => '', // 提现记录不显示余额
                'status' => $this->getWithdrawStatus($withdraw['status']),
                'status_desc' => $this->getWithdrawStatusDesc($withdraw['status']),
                'createtime_timestamp' => $withdraw['createtime'],
                'remark' => $this->getWithdrawRemark($withdraw),
                'withdraw_data' => $withdraw
            ];
        }

        return $bills;
    }

    /**
     * 格式化金额显示
     */
    private function formatAmount($amount, $type)
    {
        $prefix = '';
        if (in_array($type, ['recharge', 'transfer_in', 'reward', 'refund'])) {
            $prefix = '+';
        } elseif (in_array($type, ['purchase', 'transfer_out', 'withdraw', 'fee'])) {
            $prefix = '-';
        }

        return $prefix . number_format(abs($amount), 2, '.', '');
    }

    /**
     * 格式化备注信息
     */
    private function formatRemark($log)
    {
        // 如果是转账，添加对方用户信息
        if (in_array($log['type'], ['transfer_in', 'transfer_out'])) {
            $otherMid = $log['type'] == 'transfer_in' ? $log['from_mid'] : $log['to_mid'];
            if ($otherMid) {
                $otherUser = Db::name('member')->where('id', $otherMid)->field('nickname')->find();
                $otherName = $otherUser ? $otherUser['nickname'] : '未知用户';
                $action = $log['type'] == 'transfer_in' ? '收到来自' : '转账给';
                return $action . $otherName . '的转账';
            }
        }

        return $log['remark'] ?: $this->getTypeDesc($log['type']);
    }

    /**
     * 获取提现状态
     */
    private function getWithdrawStatus($status)
    {
        $statusMap = [
            0 => 'pending',
            1 => 'pending',
            2 => 'failed',
            3 => 'completed'
        ];
        return $statusMap[$status] ?? 'pending';
    }

    /**
     * 获取提现状态描述
     */
    private function getWithdrawStatusDesc($status)
    {
        $statusMap = [
            0 => '待审核',
            1 => '审核通过',
            2 => '审核拒绝',
            3 => '已完成'
        ];
        return $statusMap[$status] ?? '未知状态';
    }



    /**
     * 获取交易类型描述
     */
    private function getTypeDesc($type)
    {
        $typeMap = [
            'recharge' => '酒币充值',
            'purchase' => '购买商品',
            'transfer_in' => '收到转账',
            'transfer_out' => '发起转账',
            'withdraw' => '酒币提现',
            'reward' => '系统奖励',
            'refund' => '退款',
            'fee' => '手续费',
            'adjust' => '余额调整'
        ];

        return $typeMap[$type] ?? '其他交易';
    }

    /**
     * 合并提现相关的流水记录
     * 将同一次提现的多条记录（freeze、success等）合并为一条记录
     */
    private function mergeWithdrawLogs($logs)
    {
        $mergedLogs = [];
        $withdrawGroups = [];
        
        // 按relation_id分组提现相关记录
        foreach ($logs as $log) {
            if (strpos($log['type'], 'withdraw') !== false && $log['relation_id'] > 0) {
                $withdrawGroups[$log['relation_id']][] = $log;
            } else {
                // 非提现记录直接添加
                $mergedLogs[] = $log;
            }
        }
        
        // 合并每组提现记录
        foreach ($withdrawGroups as $relationId => $group) {
            $mergedLog = $this->createMergedWithdrawLog($relationId, $group);
            if ($mergedLog) {
                $mergedLogs[] = $mergedLog;
            }
        }
        
        // 按时间重新排序
        usort($mergedLogs, function($a, $b) {
            return $b['createtime'] - $a['createtime'];
        });
        
        return $mergedLogs;
    }
    
    /**
     * 创建合并后的提现记录
     */
    private function createMergedWithdrawLog($relationId, $group)
    {
        if (empty($group)) return null;
        
        // 获取提现申请详情
        $withdraw = Db::name('wine_coin_withdraw')->where('id', $relationId)->find();
        if (!$withdraw) return null;
        
        // 按状态优先级排序：success > approved > refund > freeze
        $typePriority = [
            'withdraw_success' => 4,
            'withdraw_approved' => 3, 
            'withdraw_refund' => 2,
            'withdraw_freeze' => 1
        ];
        
        usort($group, function($a, $b) use ($typePriority) {
            $priorityA = $typePriority[$a['type']] ?? 0;
            $priorityB = $typePriority[$b['type']] ?? 0;
            return $priorityB - $priorityA;
        });
        
        $primaryLog = $group[0]; // 最高优先级的记录
        
        // 构建合并后的记录
        $statusMap = [
            0 => '待审核',
            1 => '审核通过',
            2 => '审核拒绝', 
            3 => '已完成'
        ];
        
        $bankTypeMap = [
            'alipay' => '支付宝',
            'bank' => '银行卡',
            'wechat' => '微信'
        ];
        
        $status = $statusMap[$withdraw['status']] ?? '未知';
        $bankType = $bankTypeMap[$withdraw['bank_type']] ?? $withdraw['bank_type'];
        
        return [
            'id' => 'withdraw_' . $relationId, // 特殊ID标识这是合并记录
            'type' => 'withdraw_merged',
            'type_desc' => $this->getTypeIcon('withdraw_merged') . ' 酒币提现',
            'amount' => -$withdraw['amount'], // 提现显示负数
            'balance_after' => '', // 合并记录不显示余额
            'status' => $this->getWithdrawStatus($withdraw['status']),
            'status_desc' => $this->getStatusIcon($this->getWithdrawStatus($withdraw['status'])) . ' ' . $status,
            'remark' => "提现到{$bankType}，状态：{$status}，实际到账：{$withdraw['actual_amount']}元",
            'createtime' => $withdraw['createtime'],
            'withdraw_detail' => $withdraw, // 完整提现详情
            'withdraw_logs' => $group // 完整状态历史
        ];
    }

    /**
     * 获取提现详情
     */
    private function getWithdrawDetail($withdraw_id)
    {
        // 获取提现申请详情
        $withdraw = Db::name('wine_coin_withdraw')
            ->where('id', $withdraw_id)
            ->where('aid', aid)
            ->where('mid', mid)
            ->find();
            
        if (!$withdraw) {
            return $this->json(['status' => 0, 'msg' => '提现记录不存在']);
        }
        
        // 获取相关的流水记录（状态历史）
        $statusLogs = Db::name('wine_coin_log')
            ->where('aid', aid)
            ->where('mid', mid)
            ->where('relation_id', $withdraw_id)
            ->where('type', 'like', '%withdraw%')
            ->order('createtime asc')
            ->select()
            ->toArray();
            
        // 格式化状态历史
        $statusHistory = [];
        foreach ($statusLogs as $log) {
            $statusHistory[] = [
                'type' => $log['type'],
                'type_desc' => $this->getTypeDesc($log['type']),
                'amount' => number_format($log['amount'], 2, '.', ''),
                'remark' => $log['remark'],
                'createtime' => date('Y-m-d H:i:s', $log['createtime'])
            ];
        }
        
        // 构建详情数据
        $bankTypeMap = [
            'alipay' => '支付宝',
            'bank' => '银行卡',
            'wechat' => '微信'
        ];
        
        $statusMap = [
            0 => '待审核',
            1 => '审核通过',
            2 => '审核拒绝',
            3 => '已完成'
        ];
        
        $detail = [
            'id' => 'withdraw_' . $withdraw_id,
            'type' => 'withdraw_merged',
            'type_desc' => '酒币提现',
            'amount' => '-' . number_format($withdraw['amount'], 2, '.', ''),
            'balance_after' => '', // 合并记录不显示余额
            'createtime' => date('Y-m-d H:i:s', $withdraw['createtime']),
            'audit_time' => $withdraw['audit_time'] ? date('Y-m-d H:i:s', $withdraw['audit_time']) : '',
            'complete_time' => $withdraw['complete_time'] ? date('Y-m-d H:i:s', $withdraw['complete_time']) : '',
            'remark' => '提现到' . ($bankTypeMap[$withdraw['bank_type']] ?? $withdraw['bank_type']) . 
                       '，状态：' . ($statusMap[$withdraw['status']] ?? '未知') . 
                       '，实际到账：' . number_format($withdraw['actual_amount'], 2) . '元',
            
            // 提现专有字段
            'withdraw_info' => [
                'ordernum' => $withdraw['ordernum'],
                'amount' => number_format($withdraw['amount'], 2, '.', ''),
                'fee_amount' => number_format($withdraw['fee_amount'], 2, '.', ''),
                'actual_amount' => number_format($withdraw['actual_amount'], 2, '.', ''),
                'bank_type' => $withdraw['bank_type'],
                'bank_type_desc' => $bankTypeMap[$withdraw['bank_type']] ?? $withdraw['bank_type'],
                'bank_account' => $withdraw['bank_account'],
                'bank_name' => $withdraw['bank_name'],
                'account_name' => $withdraw['account_name'],
                'status' => $withdraw['status'],
                'status_desc' => $statusMap[$withdraw['status']] ?? '未知',
                'audit_remark' => $withdraw['audit_remark']
            ],
            
            // 状态变化历史
            'status_history' => $statusHistory
        ];
        
        return $this->json([
            'status' => 1,
            'msg' => '获取成功',
            'log' => $detail,
            'relation_info' => null,
            'other_user' => null
        ]);
    }

    

    /**
     * 搜索用户（用于转账）
     */
    public function searchUser()
    {
        $keyword = trim(input('param.keyword', ''));
        if (empty($keyword)) {
            return $this->json(['status' => 0, 'msg' => '请输入搜索关键词']);
        }

        try {
            $where = [];
            $where[] = ['aid', '=', aid];
            $where[] = ['id', '<>', mid]; // 排除自己

            // 支持按ID、昵称、手机号搜索
            $whereOr = [];
            if (is_numeric($keyword)) {
                $whereOr[] = ['id', '=', $keyword];
                $whereOr[] = ['tel', 'like', '%' . $keyword . '%'];
            }
            $whereOr[] = ['nickname', 'like', '%' . $keyword . '%'];

            $users = Db::name('member')
                ->where($where)
                ->where(function($query) use ($whereOr) {
                    $query->whereOr($whereOr);
                })
                ->field('id,nickname,headimg,tel')
                ->limit(10)
                ->select()
                ->toArray();

            // 隐藏手机号部分数字
            foreach ($users as &$user) {
                if ($user['tel']) {
                    $user['tel'] = substr($user['tel'], 0, 3) . '****' . substr($user['tel'], -4);
                }
            }

            return $this->json([
                'status' => 1,
                'msg' => '搜索成功',
                'users' => $users
            ]);

        } catch (\Exception $e) {
            Log::error('搜索用户失败: ' . $e->getMessage());
            return $this->json(['status' => 0, 'msg' => '搜索失败']);
        }
    }

    /**
     * 执行转账
     */
    public function doTransfer()
    {
        $toUserId = input('param.to_user_id/d');
        $amount = input('param.amount/f');
        $remark = trim(input('param.remark', ''));

        // 参数验证
        if (!$toUserId) {
            return $this->json(['status' => 0, 'msg' => '请选择收款人']);
        }

        if (!$amount || $amount <= 0) {
            return $this->json(['status' => 0, 'msg' => '请输入有效的转账金额']);
        }

        if ($toUserId == mid) {
            return $this->json(['status' => 0, 'msg' => '不能向自己转账']);
        }

        try {
            // 获取转账设置
            $transferSet = [
                'can_transfer' => 1,
                'min_amount' => 0.01,
                'max_amount' => 10000,
                'daily_limit' => 1000
            ];

            $adminSet = Db::name('admin_set')->where('aid', aid)->find();
            if ($adminSet) {
                if (isset($adminSet['wine_coin_transfer_min'])) {
                    $transferSet['min_amount'] = floatval($adminSet['wine_coin_transfer_min']);
                }
                if (isset($adminSet['wine_coin_transfer_max'])) {
                    $transferSet['max_amount'] = floatval($adminSet['wine_coin_transfer_max']);
                }
                if (isset($adminSet['wine_coin_transfer_daily_limit'])) {
                    $transferSet['daily_limit'] = floatval($adminSet['wine_coin_transfer_daily_limit']);
                }
            }

            // 验证转账金额
            if ($amount < $transferSet['min_amount']) {
                return $this->json(['status' => 0, 'msg' => '转账金额不能低于' . $transferSet['min_amount'] . '酒币']);
            }

            if ($amount > $transferSet['max_amount']) {
                return $this->json(['status' => 0, 'msg' => '转账金额不能超过' . $transferSet['max_amount'] . '酒币']);
            }

            // 验证每日限额
            $todayStart = strtotime(date('Y-m-d'));
            $todayEnd = $todayStart + 86400;
            $todayTransfer = Db::name('wine_coin_log')
                ->where('aid', aid)
                ->where('mid', mid)
                ->where('type', 'transfer_out')
                ->where('createtime', '>=', $todayStart)
                ->where('createtime', '<', $todayEnd)
                ->sum('amount');
            $todayTransferTotal = abs($todayTransfer ?: 0);

            if ($todayTransferTotal + $amount > $transferSet['daily_limit']) {
                return $this->json(['status' => 0, 'msg' => '超过每日转账限额']);
            }

            // 开启事务
            Db::startTrans();

            // 验证付款人余额
            $fromUser = Db::name('member')->where('aid', aid)->where('id', mid)->lock(true)->find();
            if (!$fromUser) {
                Db::rollback();
                return $this->json(['status' => 0, 'msg' => '用户不存在']);
            }

            $fromBalance = floatval($fromUser['wine_coin'] ?? 0);
            if ($fromBalance < $amount) {
                Db::rollback();
                return $this->json(['status' => 0, 'msg' => '酒币余额不足']);
            }

            // 验证收款人是否存在
            $toUser = Db::name('member')->where('aid', aid)->where('id', $toUserId)->lock(true)->find();
            if (!$toUser) {
                Db::rollback();
                return $this->json(['status' => 0, 'msg' => '收款人不存在']);
            }

            $toBalance = floatval($toUser['wine_coin'] ?? 0);

            // 扣除付款人酒币
            $newFromBalance = $fromBalance - $amount;
            Db::name('member')->where('aid', aid)->where('id', mid)->update(['wine_coin' => $newFromBalance]);

            // 增加收款人酒币
            $newToBalance = $toBalance + $amount;
            Db::name('member')->where('aid', aid)->where('id', $toUserId)->update(['wine_coin' => $newToBalance]);

            $currentTime = time();

            // 记录付款人流水
            $outLogData = [
                'aid' => aid,
                'mid' => mid,
                'from_mid' => mid,
                'to_mid' => $toUserId,
                'type' => 'transfer_out',
                'amount' => -$amount,
                'balance_after' => $newFromBalance,
                'remark' => $remark ?: '向 ' . $toUser['nickname'] . ' 转账',
                'createtime' => $currentTime
            ];
            Db::name('wine_coin_log')->insert($outLogData);

            // 记录收款人流水
            $inLogData = [
                'aid' => aid,
                'mid' => $toUserId,
                'from_mid' => mid,
                'to_mid' => $toUserId,
                'type' => 'transfer_in',
                'amount' => $amount,
                'balance_after' => $newToBalance,
                'remark' => $remark ?: '来自 ' . $fromUser['nickname'] . ' 的转账',
                'createtime' => $currentTime
            ];
            Db::name('wine_coin_log')->insert($inLogData);

            // 提交事务
            Db::commit();

            return $this->json([
                'status' => 1,
                'msg' => '转账成功',
                'data' => [
                    'amount' => $amount,
                    'to_user' => $toUser['nickname'],
                    'new_balance' => $newFromBalance
                ]
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('酒币转账失败: ' . $e->getMessage());
            return $this->json(['status' => 0, 'msg' => '转账失败，请稍后重试']);
        }
    }

    /**
     * 酒币交易详情 - 优化版本
     */
    public function detail()
    {
        $id = input('id', '');
        if (empty($id)) {
            return $this->json(['status' => 0, 'msg' => '参数错误']);
        }

        try {
            // 检查是否是提现记录
            if (strpos($id, 'withdraw_') === 0) {
                $withdraw_id = str_replace('withdraw_', '', $id);
                return $this->getWithdrawDetailOptimized($withdraw_id);
            }

            // 检查是否是普通流水记录
            if (strpos($id, 'normal_') === 0) {
                $log_id = str_replace('normal_', '', $id);
                return $this->getNormalTransactionDetail($log_id);
            }

            // 兼容旧格式：纯数字ID
            $log_id = intval($id);
            return $this->getNormalTransactionDetail($log_id);

        } catch (\Exception $e) {
            Log::error('酒币详情获取失败: ' . $e->getMessage());
            return $this->json(['status' => 0, 'msg' => '获取详情失败']);
        }
    }

    /**
     * 获取普通交易详情
     */
    private function getNormalTransactionDetail($log_id)
    {
        // 获取基本信息
        $log = Db::name('wine_coin_log')
            ->where('id', $log_id)
            ->where('aid', aid)
            ->where('mid', mid)
            ->find();

        if (!$log) {
            return $this->json(['status' => 0, 'msg' => '记录不存在']);
        }

        // 格式化基本信息
        $log['createtime'] = date('Y-m-d H:i:s', $log['createtime']);
        $log['amount'] = number_format($log['amount'], 2, '.', '');
        $log['balance_after'] = number_format($log['balance_after'], 2, '.', '');
        $log['type_desc'] = $this->getTypeDesc($log['type']);

        // 获取关联信息
        $relationInfo = $this->getRelationInfo($log);

        // 获取对方用户信息（转账情况）
        $otherUserInfo = null;
        if (in_array($log['type'], ['transfer_in', 'transfer_out'])) {
            $otherMid = $log['type'] == 'transfer_in' ? $log['from_mid'] : $log['to_mid'];
            if ($otherMid && $otherMid != mid) {
                $otherUserInfo = Db::name('member')
                    ->where('id', $otherMid)
                    ->field('id,nickname,headimg')
                    ->find();
            }
        }

        // 构建详情数据
        $detail = [
            'id' => 'normal_' . $log['id'],
            'type' => $log['type'],
            'type_desc' => $this->getTypeIcon($log['type']) . ' ' . $log['type_desc'],
            'amount' => $this->formatAmountWithSign($log['amount'], $log['type']),
            'balance_after' => $log['balance_after'] . ' 酒币',
            'createtime' => $log['createtime'],
            'status' => 'completed',
            'status_desc' => $this->getStatusIcon('completed') . ' 已完成',
            'remark' => $this->generateDetailRemark($log, $otherUserInfo, $relationInfo),
            'transaction_info' => $this->buildTransactionInfo($log, $otherUserInfo, $relationInfo)
        ];

        return $this->json([
            'status' => 1,
            'msg' => '获取成功',
            'detail' => $detail
        ]);
    }

    /**
     * 获取关联信息详情
     */
    private function getRelationInfo($log)
    {
        if (!$log['relation_id'] || !$log['relation_type']) {
            return null;
        }

        $relationInfo = null;

        switch ($log['relation_type']) {
            case 'order':
                // 获取订单信息
                $order = Db::name('order')
                    ->where('id', $log['relation_id'])
                    ->field('id,ordersn,product_name,sell_price,paytime,status')
                    ->find();
                if ($order) {
                    $relationInfo = [
                        'type' => 'order',
                        'type_name' => '订单',
                        'data' => [
                            'order_id' => $order['id'],
                            'order_sn' => $order['ordersn'],
                            'product_name' => $order['product_name'],
                            'amount' => number_format($order['sell_price'], 2, '.', ''),
                            'pay_time' => $order['paytime'] ? date('Y-m-d H:i:s', $order['paytime']) : '',
                            'status' => $this->getOrderStatusDesc($order['status'])
                        ]
                    ];
                }
                break;

            case 'recharge':
                // 获取充值订单信息
                $recharge = Db::name('winecoin_recharge_order')
                    ->where('id', $log['relation_id'])
                    ->find();
                if ($recharge) {
                    $relationInfo = [
                        'type' => 'recharge',
                        'type_name' => '充值订单',
                        'data' => [
                            'order_sn' => $recharge['ordersn'],
                            'amount' => number_format($recharge['amount'], 2, '.', ''),
                            'pay_type' => $recharge['paytype'],
                            'status' => $recharge['status'] == 1 ? '已完成' : '待支付'
                        ]
                    ];
                }
                break;

            case 'withdraw':
                // 获取提现信息
                $withdraw = Db::name('wine_coin_withdraw')
                    ->where('id', $log['relation_id'])
                    ->find();
                if ($withdraw) {
                    $relationInfo = [
                        'type' => 'withdraw',
                        'type_name' => '提现申请',
                        'data' => [
                            'amount' => number_format($withdraw['amount'], 2, '.', ''),
                            'fee' => number_format($withdraw['fee'], 2, '.', ''),
                            'actual_amount' => number_format($withdraw['actual_amount'], 2, '.', ''),
                            'status' => $this->getWithdrawStatusDesc($withdraw['status'])
                        ]
                    ];
                }
                break;

            case 'transfer':
                // 转账信息已在主记录中
                $relationInfo = [
                    'type' => 'transfer',
                    'type_name' => '转账'
                ];
                break;
        }

        return $relationInfo;
    }

    /**
     * 获取订单状态描述
     */
    private function getOrderStatusDesc($status)
    {
        $statusMap = [
            0 => '待支付',
            1 => '已支付',
            2 => '已完成',
            3 => '已取消',
            4 => '已退款'
        ];
        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 获取优化的提现详情
     */
    private function getWithdrawDetailOptimized($withdraw_id)
    {
        // 获取提现申请详情
        $withdraw = Db::name('wine_coin_withdraw')
            ->where('id', $withdraw_id)
            ->where('aid', aid)
            ->where('mid', mid)
            ->find();

        if (!$withdraw) {
            return $this->json(['status' => 0, 'msg' => '提现记录不存在']);
        }

        // 获取相关的流水记录（状态历史）
        $statusLogs = Db::name('wine_coin_log')
            ->where('aid', aid)
            ->where('mid', mid)
            ->where('relation_id', $withdraw_id)
            ->where('type', 'like', '%withdraw%')
            ->order('createtime asc')
            ->select()
            ->toArray();

        // 构建详情数据
        $detail = [
            'id' => 'withdraw_' . $withdraw_id,
            'type' => 'withdraw',
            'type_desc' => $this->getTypeIcon('withdraw') . ' 酒币提现',
            'amount' => '-' . number_format($withdraw['amount'], 2, '.', ''),
            'balance_after' => '', // 提现记录不显示余额
            'createtime' => date('Y-m-d H:i:s', $withdraw['createtime']),
            'status' => $this->getWithdrawStatus($withdraw['status']),
            'status_desc' => $this->getStatusIcon($this->getWithdrawStatus($withdraw['status'])) . ' ' . $this->getWithdrawStatusDesc($withdraw['status']),
            'remark' => $this->getWithdrawRemark($withdraw),

            // 提现专有信息
            'withdraw_info' => [
                'ordernum' => $withdraw['ordernum'],
                'withdraw_amount' => number_format($withdraw['amount'], 2, '.', '') . ' 酒币',
                'fee_amount' => number_format($withdraw['fee_amount'], 2, '.', '') . ' 酒币',
                'actual_amount' => number_format($withdraw['actual_amount'], 2, '.', '') . ' 元',
                'bank_type' => $this->getBankTypeDesc($withdraw['bank_type']),
                'bank_account' => $this->maskBankAccount($withdraw['bank_account']),
                'bank_name' => $withdraw['bank_name'],
                'account_name' => $this->maskAccountName($withdraw['account_name']),
                'apply_time' => date('Y-m-d H:i:s', $withdraw['createtime']),
                'audit_time' => $withdraw['audit_time'] ? date('Y-m-d H:i:s', $withdraw['audit_time']) : '',
                'complete_time' => $withdraw['complete_time'] ? date('Y-m-d H:i:s', $withdraw['complete_time']) : '',
                'audit_remark' => $withdraw['audit_remark']
            ],

            // 状态变化历史
            'status_history' => $this->formatStatusHistory($statusLogs)
        ];

        return $this->json([
            'status' => 1,
            'msg' => '获取成功',
            'detail' => $detail
        ]);
    }

    /**
     * 格式化金额显示（带正负号）
     */
    private function formatAmountWithSign($amount, $type)
    {
        $prefix = '';
        if (in_array($type, ['recharge', 'transfer_in', 'reward', 'refund', 'bonus', 'gift', 'compensation'])) {
            $prefix = '+';
        } elseif (in_array($type, ['purchase', 'transfer_out', 'withdraw', 'fee', 'payment', 'penalty'])) {
            $prefix = '-';
        }

        return $prefix . number_format(abs($amount), 2, '.', '') . ' 酒币';
    }

    /**
     * 生成详情备注
     */
    private function generateDetailRemark($log, $otherUserInfo, $relationInfo)
    {
        switch ($log['type']) {
            case 'transfer_in':
                return $otherUserInfo ? "收到来自 {$otherUserInfo['nickname']} 的转账" : "收到转账";
            case 'transfer_out':
                return $otherUserInfo ? "转账给 {$otherUserInfo['nickname']}" : "发起转账";
            case 'purchase':
            case 'payment':
                return $relationInfo ? "购买商品：{$relationInfo['title']}" : "购买商品";
            case 'recharge':
                return "酒币充值";
            case 'refund':
                return $relationInfo ? "订单退款：{$relationInfo['title']}" : "订单退款";
            case 'reward':
            case 'bonus':
                return "系统奖励";
            default:
                return $log['remark'] ?: $this->getTypeDesc($log['type']);
        }
    }

    /**
     * 构建交易信息
     */
    private function buildTransactionInfo($log, $otherUserInfo, $relationInfo)
    {
        $info = [
            'transaction_id' => $log['id'],
            'transaction_time' => date('Y-m-d H:i:s', $log['createtime']),
            'original_amount' => number_format($log['amount'], 2, '.', ''),
            'balance_before' => number_format($log['balance_after'] - $log['amount'], 2, '.', ''),
            'balance_after' => number_format($log['balance_after'], 2, '.', '')
        ];

        // 转账相关信息
        if (in_array($log['type'], ['transfer_in', 'transfer_out']) && $otherUserInfo) {
            $info['other_user'] = [
                'nickname' => $otherUserInfo['nickname'],
                'headimg' => $otherUserInfo['headimg']
            ];
        }

        // 订单相关信息
        if ($relationInfo) {
            $info['related_order'] = $relationInfo;
        }

        return $info;
    }

    /**
     * 获取银行类型描述
     */
    private function getBankTypeDesc($bankType)
    {
        $typeMap = [
            'alipay' => '支付宝',
            'wechat' => '微信',
            'bank' => '银行卡'
        ];
        return $typeMap[$bankType] ?? $bankType;
    }

    /**
     * 脱敏银行账号
     */
    private function maskBankAccount($account)
    {
        if (strlen($account) <= 4) {
            return $account;
        }
        return substr($account, 0, 4) . str_repeat('*', strlen($account) - 8) . substr($account, -4);
    }

    /**
     * 脱敏账户姓名
     */
    private function maskAccountName($name)
    {
        if (mb_strlen($name) <= 1) {
            return $name;
        }
        return mb_substr($name, 0, 1) . str_repeat('*', mb_strlen($name) - 1);
    }

    /**
     * 格式化状态历史
     */
    private function formatStatusHistory($statusLogs)
    {
        $history = [];
        foreach ($statusLogs as $log) {
            $history[] = [
                'time' => date('m-d H:i', $log['createtime']),
                'status' => $this->getTypeDesc($log['type']),
                'amount' => number_format($log['amount'], 2, '.', ''),
                'remark' => $log['remark']
            ];
        }
        return $history;
    }

    /**
     * 获取交易类型图标
     */
    private function getTypeIcon($type)
    {
        $iconMap = [
            'recharge' => '💰',
            'purchase' => '🛒',
            'payment' => '💳',
            'transfer_in' => '📥',
            'transfer_out' => '📤',
            'withdraw' => '💸',
            'withdraw_merged' => '💸',
            'refund' => '↩️',
            'reward' => '🎁',
            'bonus' => '🎉',
            'fee' => '💸',
            'compensation' => '🔄',
            'gift' => '🎁'
        ];

        return $iconMap[$type] ?? '💰';
    }

    /**
     * 获取状态图标
     */
    private function getStatusIcon($status)
    {
        $iconMap = [
            'completed' => '✅',
            'pending' => '⏳',
            'processing' => '🔄',
            'failed' => '❌',
            'cancelled' => '🚫',
            'audit_pending' => '👀',
            'audit_approved' => '✅',
            'audit_rejected' => '❌'
        ];

        return $iconMap[$status] ?? '❓';
    }
}