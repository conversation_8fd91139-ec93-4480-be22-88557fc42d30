<?php
/**
 * 酒币后台管理控制器
 * 基于现有后台管理架构设计
 */

namespace app\controller;
use think\facade\Db;
use think\facade\View;
use app\common\WineCoin;
use app\common\WineCoinHelper;

class WineCoinAdmin extends Common
{
    /**
     * 酒币系统设置
     */
    public function setting() {
        if(request()->isPost()) {
            $data = input('post.');
            
            // 验证数据
            if($data['min_recharge_amount'] < 0.01) {
                return $this->error('最小充值金额不能小于0.01');
            }
            if($data['max_recharge_amount'] < $data['min_recharge_amount']) {
                return $this->error('最大充值金额不能小于最小充值金额');
            }
            
            // 更新设置
            $setting_data = [
                'enable' => $data['enable'] ?? 0,
                'recharge_enable' => $data['recharge_enable'] ?? 0,
                'transfer_enable' => $data['transfer_enable'] ?? 0,
                'withdraw_enable' => $data['withdraw_enable'] ?? 0,
                'min_recharge_amount' => $data['min_recharge_amount'],
                'max_recharge_amount' => $data['max_recharge_amount'],
                'min_transfer_amount' => $data['min_transfer_amount'],
                'max_transfer_amount' => $data['max_transfer_amount'],
                'daily_transfer_limit' => $data['daily_transfer_limit'],
                'min_withdraw_amount' => $data['min_withdraw_amount'],
                'max_withdraw_amount' => $data['max_withdraw_amount'],
                'daily_withdraw_limit' => $data['daily_withdraw_limit'],
                'transfer_fee_enable' => $data['transfer_fee_enable'] ?? 0,
                'transfer_fee_rate' => $data['transfer_fee_rate'],
                'transfer_fee_min' => $data['transfer_fee_min'],
                'transfer_fee_max' => $data['transfer_fee_max'],
                'withdraw_fee_enable' => $data['withdraw_fee_enable'] ?? 0,
                'withdraw_fee_rate' => $data['withdraw_fee_rate'],
                'withdraw_fee_min' => $data['withdraw_fee_min'],
                'withdraw_fee_max' => $data['withdraw_fee_max'],
                'withdraw_auto_audit' => $data['withdraw_auto_audit'] ?? 0,
                'recharge_bonus_enable' => $data['recharge_bonus_enable'] ?? 0,
                'recharge_bonus_rules' => $data['recharge_bonus_rules'] ?? '',
                'updatetime' => time()
            ];
            
            $exists = Db::name('wine_coin_setting')->where('aid', aid)->find();
            if($exists) {
                Db::name('wine_coin_setting')->where('aid', aid)->update($setting_data);
            } else {
                $setting_data['aid'] = aid;
                $setting_data['createtime'] = time();
                Db::name('wine_coin_setting')->insert($setting_data);
            }
            
            return $this->success('设置保存成功');
        }
        
        // 获取当前设置
        $setting = WineCoin::getSetting(aid);
        
        View::assign('setting', $setting);
        return View::fetch();
    }
    
    /**
     * 酒币流水管理
     */
    public function logs() {
        $page = input('get.page/d', 1);
        $limit = input('get.limit/d', 20);
        $type = input('get.type', '');
        $mid = input('get.mid/d', 0);
        $start_date = input('get.start_date', '');
        $end_date = input('get.end_date', '');
        
        $where = ['aid' => aid];
        if($type) $where['type'] = $type;
        if($mid) $where['mid'] = $mid;
        if($start_date && $end_date) {
            $start_time = strtotime($start_date . ' 00:00:00');
            $end_time = strtotime($end_date . ' 23:59:59');
            $where['createtime'] = ['between', [$start_time, $end_time]];
        }
        
        $logs = Db::name('wine_coin_log')
            ->where($where)
            ->order('createtime desc')
            ->page($page, $limit)
            ->select()
            ->toArray();

        $total = Db::name('wine_coin_log')->where($where)->count();
        
        foreach($logs as &$log) {
            $log['createtime_format'] = date('Y-m-d H:i:s', $log['createtime']);
            $log['amount_format'] = ($log['amount'] > 0 ? '+' : '') . WineCoinHelper::formatAmount($log['amount']);
            $log['type_text'] = WineCoinHelper::getTypeText($log['type']);
            $log['balance_after_format'] = WineCoinHelper::formatAmount($log['balance_after']);
            
            // 获取用户信息
            if($log['mid'] > 0) {
                $member = Db::name('member')->where('aid', aid)->where('id', $log['mid'])->field('nickname,mobile')->find();
                $log['member'] = $member ?: ['nickname' => '未知用户', 'mobile' => ''];
            }
            
            // 获取商户信息
            if($log['bid'] > 0) {
                $business = Db::name('business')->where('aid', aid)->where('id', $log['bid'])->field('name')->find();
                $log['business'] = $business ?: ['name' => '未知商户'];
            }
        }
        
        // 统计信息
        $statistics = [
            'total_income' => Db::name('wine_coin_log')->where($where)->where('amount', '>', 0)->sum('amount'),
            'total_expense' => Db::name('wine_coin_log')->where($where)->where('amount', '<', 0)->sum('amount'),
            'total_count' => $total
        ];
        
        View::assign([
            'logs' => $logs,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'statistics' => $statistics,
            'search' => [
                'type' => $type,
                'mid' => $mid,
                'start_date' => $start_date,
                'end_date' => $end_date
            ]
        ]);
        
        return View::fetch();
    }
    
    /**
     * 提现审核管理
     */
    public function withdraws() {
        $page = input('get.page/d', 1);
        $limit = input('get.limit/d', 20);
        $status = input('get.status', '');
        $user_type = input('get.user_type/d', 0);
        
        $where = ['aid' => aid];
        if($status !== '') $where['status'] = $status;
        if($user_type > 0) $where['user_type'] = $user_type;
        
        $withdraws = Db::name('wine_coin_withdraw')
            ->where($where)
            ->order('createtime desc')
            ->page($page, $limit)
            ->select()
            ->toArray();
            
        $total = Db::name('wine_coin_withdraw')->where($where)->count();
        
        $status_map = [
            0 => '待审核',
            1 => '已通过',
            2 => '已拒绝',
            3 => '已完成'
        ];
        
        foreach($withdraws as &$withdraw) {
            $withdraw['createtime_format'] = date('Y-m-d H:i:s', $withdraw['createtime']);
            $withdraw['amount_format'] = number_format($withdraw['amount'], 2, '.', '');
            $withdraw['actual_amount_format'] = number_format($withdraw['actual_amount'], 2, '.', '');
            $withdraw['status_text'] = $status_map[$withdraw['status']] ?? '未知状态';
            $withdraw['user_type_text'] = $withdraw['user_type'] == 1 ? '用户' : '商户';
            
            // 获取用户或商户信息
            if($withdraw['user_type'] == 1 && $withdraw['mid'] > 0) {
                $member = Db::name('member')->where('aid', aid)->where('id', $withdraw['mid'])->field('nickname,mobile,headimg')->find();
                $withdraw['user_info'] = $member ?: ['nickname' => '未知用户', 'mobile' => '', 'headimg' => ''];
            } elseif($withdraw['user_type'] == 2 && $withdraw['bid'] > 0) {
                $business = Db::name('business')->where('aid', aid)->where('id', $withdraw['bid'])->field('name,mobile')->find();
                $withdraw['user_info'] = $business ?: ['name' => '未知商户', 'mobile' => ''];
            }
        }
        
        // 统计信息
        $statistics = [
            'pending_count' => Db::name('wine_coin_withdraw')->where('aid', aid)->where('status', 0)->count(),
            'pending_amount' => Db::name('wine_coin_withdraw')->where('aid', aid)->where('status', 0)->sum('amount'),
            'completed_count' => Db::name('wine_coin_withdraw')->where('aid', aid)->where('status', 3)->count(),
            'completed_amount' => Db::name('wine_coin_withdraw')->where('aid', aid)->where('status', 3)->sum('amount')
        ];
        
        View::assign([
            'withdraws' => $withdraws,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'statistics' => $statistics,
            'search' => [
                'status' => $status,
                'user_type' => $user_type
            ]
        ]);
        
        return View::fetch();
    }
    
    /**
     * 处理提现申请
     */
    public function processWithdraw() {
        if(!request()->isPost()) {
            return $this->error('非法请求');
        }
        
        $id = input('post.id/d');
        $action = input('post.action'); // approve, reject
        $remark = input('post.remark', '');
        
        if(!$id) return $this->error('参数错误');
        
        $withdraw = Db::name('wine_coin_withdraw')->where('aid', aid)->where('id', $id)->find();
        if(!$withdraw) return $this->error('提现申请不存在');
        if($withdraw['status'] != 0) return $this->error('提现申请已处理');
        
        $status = $action == 'approve' ? 1 : 2;
        $result = \app\common\WineCoin::processWithdraw($id, $status, $remark);
        
        if($result['status'] == 1) {
            return $this->success($result['msg']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 充值订单管理
     */
    public function recharges() {
        $page = input('get.page/d', 1);
        $limit = input('get.limit/d', 20);
        $status = input('get.status', '');
        $mid = input('get.mid/d', 0);
        $start_date = input('get.start_date', '');
        $end_date = input('get.end_date', '');

        $where = ['aid' => aid];
        if($status !== '') $where['status'] = $status;
        if($mid) $where['mid'] = $mid;
        if($start_date && $end_date) {
            $start_time = strtotime($start_date . ' 00:00:00');
            $end_time = strtotime($end_date . ' 23:59:59');
            $where['createtime'] = ['between', [$start_time, $end_time]];
        }

        $recharges = Db::name('wine_coin_recharge_order')
            ->where($where)
            ->order('createtime desc')
            ->page($page, $limit)
            ->select()
            ->toArray();

        $total = Db::name('wine_coin_recharge_order')->where($where)->count();

        $status_map = [
            0 => '待支付',
            1 => '已支付',
            2 => '已取消'
        ];

        foreach($recharges as &$recharge) {
            $recharge['createtime_format'] = date('Y-m-d H:i:s', $recharge['createtime']);
            $recharge['pay_time_format'] = $recharge['pay_time'] ? date('Y-m-d H:i:s', $recharge['pay_time']) : '';
            $recharge['amount_format'] = number_format($recharge['amount'], 2, '.', '');
            $recharge['wine_coin_amount_format'] = number_format($recharge['wine_coin_amount'], 2, '.', '');
            $recharge['bonus_amount_format'] = number_format($recharge['bonus_amount'], 2, '.', '');
            $recharge['status_text'] = $status_map[$recharge['status']] ?? '未知状态';

            // 获取用户信息
            if($recharge['mid'] > 0) {
                $member = Db::name('member')->where('aid', aid)->where('id', $recharge['mid'])->field('nickname,mobile')->find();
                $recharge['member'] = $member ?: ['nickname' => '未知用户', 'mobile' => ''];
            }
        }

        // 统计信息
        $statistics = [
            'total_amount' => Db::name('wine_coin_recharge_order')->where('aid', aid)->where('status', 1)->sum('amount'),
            'total_count' => Db::name('wine_coin_recharge_order')->where('aid', aid)->where('status', 1)->count(),
            'today_amount' => Db::name('wine_coin_recharge_order')
                ->where('aid', aid)
                ->where('status', 1)
                ->where('pay_time', '>=', strtotime(date('Y-m-d 00:00:00')))
                ->sum('amount'),
            'today_count' => Db::name('wine_coin_recharge_order')
                ->where('aid', aid)
                ->where('status', 1)
                ->where('pay_time', '>=', strtotime(date('Y-m-d 00:00:00')))
                ->count()
        ];

        View::assign([
            'recharges' => $recharges,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'statistics' => $statistics,
            'search' => [
                'status' => $status,
                'mid' => $mid,
                'start_date' => $start_date,
                'end_date' => $end_date
            ]
        ]);

        return View::fetch();
    }

    /**
     * 转账记录管理
     */
    public function transfers() {
        $page = input('get.page/d', 1);
        $limit = input('get.limit/d', 20);
        $from_mid = input('get.from_mid/d', 0);
        $to_mid = input('get.to_mid/d', 0);
        $start_date = input('get.start_date', '');
        $end_date = input('get.end_date', '');

        $where = ['aid' => aid];
        if($from_mid) $where['from_mid'] = $from_mid;
        if($to_mid) $where['to_mid'] = $to_mid;
        if($start_date && $end_date) {
            $start_time = strtotime($start_date . ' 00:00:00');
            $end_time = strtotime($end_date . ' 23:59:59');
            $where['createtime'] = ['between', [$start_time, $end_time]];
        }

        $transfers = Db::name('wine_coin_transfer')
            ->where($where)
            ->order('createtime desc')
            ->page($page, $limit)
            ->select()
            ->toArray();

        $total = Db::name('wine_coin_transfer')->where($where)->count();

        foreach($transfers as &$transfer) {
            $transfer['createtime_format'] = date('Y-m-d H:i:s', $transfer['createtime']);
            $transfer['amount_format'] = WineCoinHelper::formatAmount($transfer['amount']);
            $transfer['fee_amount_format'] = WineCoinHelper::formatAmount($transfer['fee_amount']);
            $transfer['actual_amount_format'] = WineCoinHelper::formatAmount($transfer['actual_amount']);
            $transfer['transfer_type_text'] = $transfer['transfer_type'] == 1 ? '用户转用户' : '用户转商户';

            // 获取转出用户信息
            if($transfer['from_mid'] > 0) {
                $from_member = Db::name('member')->where('aid', aid)->where('id', $transfer['from_mid'])->field('nickname,mobile')->find();
                $transfer['from_member'] = $from_member ?: ['nickname' => '未知用户', 'mobile' => ''];
            }

            // 获取转入用户信息
            if($transfer['to_mid'] > 0) {
                $to_member = Db::name('member')->where('aid', aid)->where('id', $transfer['to_mid'])->field('nickname,mobile')->find();
                $transfer['to_member'] = $to_member ?: ['nickname' => '未知用户', 'mobile' => ''];
            }
        }

        // 统计信息
        $statistics = [
            'total_amount' => Db::name('wine_coin_transfer')->where('aid', aid)->where('status', 1)->sum('amount'),
            'total_count' => Db::name('wine_coin_transfer')->where('aid', aid)->where('status', 1)->count(),
            'total_fee' => Db::name('wine_coin_transfer')->where('aid', aid)->where('status', 1)->sum('fee_amount'),
            'today_amount' => Db::name('wine_coin_transfer')
                ->where('aid', aid)
                ->where('status', 1)
                ->where('createtime', '>=', strtotime(date('Y-m-d 00:00:00')))
                ->sum('amount'),
            'today_count' => Db::name('wine_coin_transfer')
                ->where('aid', aid)
                ->where('status', 1)
                ->where('createtime', '>=', strtotime(date('Y-m-d 00:00:00')))
                ->count()
        ];

        View::assign([
            'transfers' => $transfers,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'statistics' => $statistics,
            'search' => [
                'from_mid' => $from_mid,
                'to_mid' => $to_mid,
                'start_date' => $start_date,
                'end_date' => $end_date
            ]
        ]);

        return View::fetch();
    }

    /**
     * 数据统计
     */
    public function statistics() {
        $date_range = input('get.date_range', '7'); // 7, 30, 90 天
        $end_date = date('Y-m-d');
        $start_date = date('Y-m-d', strtotime("-{$date_range} days"));

        // 按日统计
        $daily_stats = [];
        for($i = 0; $i < $date_range; $i++) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $start_time = strtotime($date . ' 00:00:00');
            $end_time = strtotime($date . ' 23:59:59');

            $daily_stats[] = [
                'date' => $date,
                'recharge_amount' => Db::name('wine_coin_recharge_order')
                    ->where('aid', aid)
                    ->where('status', 1)
                    ->where('pay_time', 'between', [$start_time, $end_time])
                    ->sum('amount'),
                'transfer_amount' => Db::name('wine_coin_transfer')
                    ->where('aid', aid)
                    ->where('status', 1)
                    ->where('createtime', 'between', [$start_time, $end_time])
                    ->sum('amount'),
                'withdraw_amount' => Db::name('wine_coin_withdraw')
                    ->where('aid', aid)
                    ->where('status', 3)
                    ->where('complete_time', 'between', [$start_time, $end_time])
                    ->sum('amount')
            ];
        }

        $daily_stats = array_reverse($daily_stats);

        // 总体统计
        $total_stats = [
            'total_users' => Db::name('member')->where('aid', aid)->where('wine_coin', '>', 0)->count(),
            'total_balance' => Db::name('member')->where('aid', aid)->sum('wine_coin'),
            'total_recharge' => Db::name('wine_coin_recharge_order')->where('aid', aid)->where('status', 1)->sum('amount'),
            'total_transfer' => Db::name('wine_coin_transfer')->where('aid', aid)->where('status', 1)->sum('amount'),
            'total_withdraw' => Db::name('wine_coin_withdraw')->where('aid', aid)->where('status', 3)->sum('amount'),
            'pending_withdraw' => Db::name('wine_coin_withdraw')->where('aid', aid)->where('status', 0)->sum('amount')
        ];

        View::assign([
            'daily_stats' => $daily_stats,
            'total_stats' => $total_stats,
            'date_range' => $date_range
        ]);

        return View::fetch();
    }
}
