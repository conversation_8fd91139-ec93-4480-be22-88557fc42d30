<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>酒币提现记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    {include file="public/css"/}
</head>
<body>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <h2 class="header-title">酒币提现记录</h2>
        </div>
        
        <!-- 搜索区域 -->
        <div class="layui-card-body">
            <div class="layui-form layui-row" lay-filter="search-form">
                <div class="layui-col-md3">
                    <label class="layui-form-label">用户ID</label>
                    <div class="layui-input-block">
                        <input type="text" name="mid" placeholder="请输入用户ID" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-col-md3">
                    <label class="layui-form-label">提现状态</label>
                    <div class="layui-input-block">
                        <select name="status">
                            <option value="">全部状态</option>
                            <option value="0">待审核</option>
                            <option value="1">审核通过</option>
                            <option value="2">审核拒绝</option>
                            <option value="3">已完成</option>
                        </select>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <label class="layui-form-label">时间范围</label>
                    <div class="layui-input-block">
                        <input type="text" name="ctime" placeholder="选择时间范围" autocomplete="off" class="layui-input" id="test-laydate-range">
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-input-block" style="margin-left: 0;">
                        <button class="layui-btn" lay-submit="" lay-filter="search-submit">
                            <i class="layui-icon layui-icon-search"></i>搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="layui-card-body">
            <table class="layui-hide" id="withdraw-table" lay-filter="withdraw-table"></table>
        </div>
    </div>
</div>

<!-- 用户头像显示模板 -->
<script type="text/html" id="user-tpl">
    <div style="display: flex; align-items: center;">
        {{# if(d.headimg){ }}
            <img src="{{d.headimg}}" style="width: 30px; height: 30px; border-radius: 50%; margin-right: 8px;" />
        {{# } else { }}
            <div style="width: 30px; height: 30px; background: #f0f0f0; border-radius: 50%; margin-right: 8px; display: flex; align-items: center; justify-content: center; font-size: 12px;">无</div>
        {{# } }}
        <div>
            <div style="font-weight: bold;">{{d.nickname || '未知用户'}}</div>
            {{# if(d.realname){ }}
                <div style="font-size: 12px; color: #999;">{{d.realname}}</div>
            {{# } }}
        </div>
    </div>
</script>

<!-- 状态显示模板 -->
<script type="text/html" id="status-tpl">
    <span class="layui-badge layui-bg-{{d.status_color}}">{{d.status_text}}</span>
</script>

<!-- 操作按钮模板 -->
<script type="text/html" id="operate-tpl">
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="detail">详情</a>
    {{# if(d.status == 0){ }}
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="approve">通过</a>
        <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="reject">拒绝</a>
    {{# } }}
    {{# if(d.status == 1){ }}
        <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="complete">完成</a>
    {{# } }}
</script>

{include file="public/js"/}
<script>
layui.use(['table', 'form', 'laydate', 'layer'], function(){
    var $ = layui.$
    ,table = layui.table
    ,form = layui.form
    ,laydate = layui.laydate
    ,layer = layui.layer;

    // 时间范围选择器
    laydate.render({
        elem: '#test-laydate-range'
        ,range: '~'
        ,type: 'datetime'
    });

    // 数据表格
    var tableIns = table.render({
        elem: '#withdraw-table'
        ,url: '{:url("WineCoin/withdrawlog")}'
        ,method: 'get'
        ,cols: [[
            {field: 'id', title: 'ID', width: 80, sort: true}
            ,{field: 'ordernum', title: '提现单号', width: 180}
            ,{field: 'nickname', title: '用户信息', width: 180, templet: '#user-tpl'}
            ,{field: 'amount', title: '提现金额', width: 100, sort: true, templet: function(d){
                return d.amount + ' 酒币';
            }}
            ,{field: 'fee', title: '手续费', width: 80, templet: function(d){
                return d.fee + ' 酒币';
            }}
            ,{field: 'actual_amount', title: '实际金额', width: 100, templet: function(d){
                return d.actual_amount + ' 元';
            }}
            ,{field: 'account_info', title: '提现账户', width: 200}
            ,{field: 'status_text', title: '状态', width: 100, templet: '#status-tpl'}
            ,{field: 'createtime', title: '申请时间', width: 150}
            ,{field: 'audit_time', title: '审核时间', width: 150}
            ,{field: 'complete_time', title: '完成时间', width: 150}
            ,{title: '操作', width: 150, align: 'center', toolbar: '#operate-tpl'}
        ]]
        ,page: true
        ,limit: 20
        ,limits: [10, 20, 50, 100]
        ,request: {
            pageName: 'page'
            ,limitName: 'limit'
        }
        ,parseData: function(res){
            return {
                "code": res.code,
                "msg": res.msg,
                "count": res.count,
                "data": res.data
            };
        }
        ,response: {
            statusCode: 0
        }
        ,loading: true
        ,even: true
        ,done: function(res, curr, count){
            console.log('表格数据加载完成:', res);
        }
    });

    // 搜索
    form.on('submit(search-submit)', function(data){
        var field = data.field;
        tableIns.reload({
            where: field
            ,page: {
                curr: 1
            }
        });
        return false;
    });

    // 表格操作事件
    table.on('tool(withdraw-table)', function(obj){
        var data = obj.data;
        
        if(obj.event === 'detail'){
            // 查看详情
            layer.open({
                type: 1,
                title: '提现详情 - ' + data.ordernum,
                area: ['600px', '500px'],
                content: `
                    <div style="padding: 20px;">
                        <div class="layui-form-item">
                            <label class="layui-form-label">提现单号:</label>
                            <div class="layui-input-block">${data.ordernum}</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">用户信息:</label>
                            <div class="layui-input-block">${data.nickname} (${data.realname || '未实名'})</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">提现金额:</label>
                            <div class="layui-input-block">${data.amount} 酒币</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">手续费:</label>
                            <div class="layui-input-block">${data.fee} 酒币</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">实际到账:</label>
                            <div class="layui-input-block">${data.actual_amount} 元</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">提现账户:</label>
                            <div class="layui-input-block">${data.account_info}</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">状态:</label>
                            <div class="layui-input-block"><span class="layui-badge layui-bg-${data.status_color}">${data.status_text}</span></div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">申请时间:</label>
                            <div class="layui-input-block">${data.createtime}</div>
                        </div>
                        ${data.audit_time ? `<div class="layui-form-item">
                            <label class="layui-form-label">审核时间:</label>
                            <div class="layui-input-block">${data.audit_time}</div>
                        </div>` : ''}
                        ${data.complete_time ? `<div class="layui-form-item">
                            <label class="layui-form-label">完成时间:</label>
                            <div class="layui-input-block">${data.complete_time}</div>
                        </div>` : ''}
                    </div>
                `
            });
        }
        else if(obj.event === 'approve'){
            // 审核通过
            layer.prompt({
                title: '审核通过 - ' + data.ordernum,
                formType: 2,
                area: ['400px', '200px']
            }, function(value, index){
                $.post('/WineCoin/processWithdraw', {
                    id: data.id,
                    status: 1,
                    remark: value
                }, function(res){
                    if(res.status == 1){
                        layer.msg('审核通过成功');
                        tableIns.reload();
                    } else {
                        layer.msg(res.msg);
                    }
                });
                layer.close(index);
            });
        }
        else if(obj.event === 'reject'){
            // 审核拒绝
            layer.prompt({
                title: '审核拒绝 - ' + data.ordernum,
                formType: 2,
                area: ['400px', '200px']
            }, function(value, index){
                if(!value){
                    layer.msg('请输入拒绝原因');
                    return;
                }
                $.post('/WineCoin/processWithdraw', {
                    id: data.id,
                    status: 2,
                    remark: value
                }, function(res){
                    if(res.status == 1){
                        layer.msg('审核拒绝成功');
                        tableIns.reload();
                    } else {
                        layer.msg(res.msg);
                    }
                });
                layer.close(index);
            });
        }
        else if(obj.event === 'complete'){
            // 标记完成
            layer.confirm('确认已完成实际打款操作？', {
                btn: ['确认完成','取消']
            }, function(index){
                layer.prompt({
                    title: '标记完成 - ' + data.ordernum,
                    value: '已完成打款',
                    formType: 2,
                    area: ['400px', '200px']
                }, function(value, index2){
                    $.post('/WineCoin/processWithdraw', {
                        id: data.id,
                        status: 3,
                        remark: value
                    }, function(res){
                        if(res.status == 1){
                            layer.msg('标记完成成功');
                            tableIns.reload();
                        } else {
                            layer.msg(res.msg);
                        }
                    });
                    layer.close(index2);
                });
                layer.close(index);
            });
        }
    });
});
</script>

</body>
</html> 