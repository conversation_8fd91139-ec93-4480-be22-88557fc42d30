<?php
/**
 * 酒币提现配置检查和修复脚本
 * 用于确保酒币提现功能的正确配置
 */

require_once dirname(__DIR__) . '/vendor/autoload.php';

// 检查配置的函数
function checkAndFixWithdrawConfig() {
    try {
        // 连接数据库（这里需要根据实际配置修改）
        $pdo = new PDO("mysql:host=localhost;dbname=your_database", "username", "password");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "🔍 开始检查酒币提现配置...\n\n";
        
        // 1. 检查酒币设置表中的配置
        $stmt = $pdo->prepare("SELECT aid, withdraw_enable, withdraw_auto_audit, min_withdraw_amount, max_withdraw_amount FROM ddwx_wine_coin_setting WHERE aid = 76");
        $stmt->execute();
        $setting = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($setting) {
            echo "✅ 找到平台76的酒币设置:\n";
            echo "   - 提现开关: " . ($setting['withdraw_enable'] ? '开启' : '关闭') . "\n";
            echo "   - 自动审核: " . ($setting['withdraw_auto_audit'] ? '开启' : '关闭') . "\n";
            echo "   - 最小提现: " . $setting['min_withdraw_amount'] . "\n";
            echo "   - 最大提现: " . $setting['max_withdraw_amount'] . "\n\n";
            
            // 如果自动审核开启，则关闭它
            if ($setting['withdraw_auto_audit'] == 1) {
                $updateStmt = $pdo->prepare("UPDATE ddwx_wine_coin_setting SET withdraw_auto_audit = 0 WHERE aid = 76");
                $updateStmt->execute();
                echo "⚠️  已关闭自动审核功能\n\n";
            }
        } else {
            echo "❌ 未找到平台76的酒币设置，可能需要初始化\n\n";
        }
        
        // 2. 检查异常的提现记录
        $stmt = $pdo->prepare("SELECT id, ordernum, status, audit_time, complete_time FROM ddwx_wine_coin_withdraw WHERE aid = 76 AND status = 3 AND audit_time = 0");
        $stmt->execute();
        $abnormalRecords = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($abnormalRecords) > 0) {
            echo "⚠️  发现异常的提现记录（状态为已完成但未经审核）:\n";
            foreach ($abnormalRecords as $record) {
                echo "   - ID: {$record['id']}, 单号: {$record['ordernum']}\n";
            }
            echo "\n";
        }
        
        // 3. 检查缺失流水的提现记录
        $stmt = $pdo->prepare("
            SELECT w.id, w.ordernum, w.status 
            FROM ddwx_wine_coin_withdraw w 
            WHERE w.aid = 76 
            AND w.status IN (1, 3) 
            AND NOT EXISTS (
                SELECT 1 FROM ddwx_wine_coin_log l 
                WHERE l.relation_id = w.id 
                AND l.type IN ('withdraw_approved', 'withdraw_success')
            )
        ");
        $stmt->execute();
        $missingLogs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($missingLogs) > 0) {
            echo "⚠️  发现缺失流水记录的提现:\n";
            foreach ($missingLogs as $record) {
                $statusText = ['0' => '待审核', '1' => '审核通过', '2' => '审核拒绝', '3' => '已完成'];
                echo "   - ID: {$record['id']}, 单号: {$record['ordernum']}, 状态: {$statusText[$record['status']]}\n";
            }
            echo "\n";
        }
        
        // 4. 检查类型映射
        $stmt = $pdo->prepare("SELECT DISTINCT type FROM ddwx_wine_coin_log WHERE aid = 76 AND type LIKE 'withdraw%'");
        $stmt->execute();
        $types = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "📊 现有的提现相关流水类型:\n";
        foreach ($types as $type) {
            $count = $pdo->query("SELECT COUNT(*) FROM ddwx_wine_coin_log WHERE aid = 76 AND type = '$type'")->fetchColumn();
            echo "   - $type: $count 条记录\n";
        }
        echo "\n";
        
        echo "✅ 检查完成！\n";
        
    } catch (Exception $e) {
        echo "❌ 检查过程中出现错误: " . $e->getMessage() . "\n";
    }
}

// 执行检查
checkAndFixWithdrawConfig();

echo "\n📝 修复建议:\n";
echo "1. 确保 withdraw_auto_audit = 0（手动审核）\n";
echo "2. 对于异常记录，建议重置状态或补充审核流水\n";
echo "3. 确保前后端类型映射一致\n";
echo "4. 测试完整的提现流程：申请 → 审核 → 完成\n";
?> 