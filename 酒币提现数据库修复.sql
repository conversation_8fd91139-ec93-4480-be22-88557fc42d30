-- 酒币提现数据库字段修复脚本
-- 解决 fields not exists:[ordernum] 错误

-- 添加缺失的ordernum字段
ALTER TABLE ddwx_wine_coin_withdraw 
ADD COLUMN ordernum varchar(50) NOT NULL DEFAULT '' COMMENT '提现单号' AFTER bid;

-- 为ordernum字段添加索引
ALTER TABLE ddwx_wine_coin_withdraw 
ADD INDEX idx_ordernum (ordernum);

-- 验证表结构
-- DESCRIBE ddwx_wine_coin_withdraw;

-- 验证字段是否添加成功
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = 'dd' AND TABLE_NAME = 'ddwx_wine_coin_withdraw' AND COLUMN_NAME = 'ordernum'; 