<?php
// 酒币提现功能修复验证脚本
echo "=== 酒币提现功能修复验证 ===\n\n";

echo "🔧 问题修复:\n";
echo "错误: fields not exists:[ordernum]\n";
echo "原因: ddwx_wine_coin_withdraw表缺少ordernum字段\n";
echo "解决: 添加ordernum varchar(50)字段和索引\n\n";

// 连接数据库验证
try {
    $pdo = new PDO("mysql:host=localhost;dbname=dd;charset=utf8mb4", 'root', '12345678');
    
    // 检查表结构
    echo "📊 数据库表结构验证:\n";
    $stmt = $pdo->query("DESCRIBE ddwx_wine_coin_withdraw");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasOrdernum = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'ordernum') {
            $hasOrdernum = true;
            echo "   ✅ ordernum字段: {$column['Type']}, 可空: {$column['Null']}, 默认: '{$column['Default']}'\n";
            break;
        }
    }
    
    if (!$hasOrdernum) {
        echo "   ❌ ordernum字段仍然缺失!\n";
    }
    
    // 检查索引
    echo "\n📈 索引验证:\n";
    $stmt = $pdo->query("SHOW INDEX FROM ddwx_wine_coin_withdraw WHERE Column_name = 'ordernum'");
    $indexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($indexes) > 0) {
        echo "   ✅ ordernum索引已创建: {$indexes[0]['Key_name']}\n";
    } else {
        echo "   ⚠️ ordernum索引未找到\n";
    }
    
    // 测试字段兼容性
    echo "\n🧪 字段兼容性测试:\n";
    $testData = [
        'aid' => 1,
        'mid' => 1,
        'bid' => 0,
        'ordernum' => 'TEST_' . time(),
        'user_type' => 1,
        'amount' => 100.00,
        'fee_amount' => 1.00,
        'actual_amount' => 99.00,
        'bank_type' => 'alipay',
        'bank_account' => '<EMAIL>',
        'bank_name' => '',
        'account_name' => '测试用户',
        'status' => 0,
        'audit_time' => 0,
        'audit_remark' => '',
        'complete_time' => 0,
        'createtime' => time()
    ];
    
    try {
        $placeholders = ':' . implode(', :', array_keys($testData));
        $sql = "INSERT INTO ddwx_wine_coin_withdraw (" . implode(', ', array_keys($testData)) . ") VALUES ({$placeholders})";
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($testData);
        
        if ($result) {
            $insertId = $pdo->lastInsertId();
            echo "   ✅ 测试数据插入成功, ID: {$insertId}\n";
            
            // 清理测试数据
            $pdo->exec("DELETE FROM ddwx_wine_coin_withdraw WHERE id = {$insertId}");
            echo "   🧹 测试数据已清理\n";
        } else {
            echo "   ❌ 测试数据插入失败\n";
        }
    } catch (Exception $e) {
        echo "   ❌ 插入测试失败: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
}

echo "\n📝 代码验证:\n";

// 检查代码文件
$files_to_check = [
    'app/common/WineCoin.php' => '提现申请逻辑',
    'app/controller/ApiWineCoin.php' => '提现API接口',
    'uniapp/pagesExt/winecoin/withdraw.vue' => '前端提现页面'
];

foreach ($files_to_check as $file => $desc) {
    if (file_exists($file)) {
        echo "   ✅ {$desc}: {$file}\n";
    } else {
        echo "   ❌ {$desc}: {$file} 不存在\n";
    }
}

echo "\n⚠️ 前端显示单位提醒:\n";
echo "用户修改了显示单位:\n";
echo "- 修改前: {{actualAmount}}酒币\n";
echo "+ 修改后: {{actualAmount}}元\n";
echo "\n请确认:\n";
echo "• 如果酒币提现到账的是酒币 → 应该显示'酒币'\n";
echo "• 如果酒币提现到账的是现金 → 应该显示'元'\n";
echo "• 当前业务逻辑是什么？\n\n";

echo "🚀 测试建议:\n";
echo "1. 测试提现申请是否成功创建\n";
echo "2. 检查ordernum是否正确生成\n";
echo "3. 验证提现记录是否正确保存\n";
echo "4. 确认前端显示单位是否符合业务需求\n\n";

echo "=== 修复验证完成 ===\n";
?> 