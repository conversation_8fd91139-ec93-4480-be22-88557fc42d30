-- ============================================
-- 酒币充值套餐后台管理功能 - 数据库安装脚本
-- 版本：v1.0.0
-- 功能：将硬编码的充值套餐改为后台动态配置
-- ============================================

-- 创建酒币充值套餐配置表
DROP TABLE IF EXISTS `ddwx_winecoin_recharge_package`;
CREATE TABLE `ddwx_winecoin_recharge_package` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) DEFAULT NULL COMMENT '平台ID',
  `givedata` text COMMENT '充值套餐数据(JSON格式)',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:1-启用,0-禁用',
  `can_input` tinyint(1) DEFAULT '1' COMMENT '是否允许自定义金额:1-允许,0-不允许',
  `min_amount` decimal(10,2) DEFAULT '1.00' COMMENT '最小充值金额',
  `max_amount` decimal(10,2) DEFAULT '10000.00' COMMENT '最大充值金额',
  `recharge_desc` text COMMENT '充值说明',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `aid` (`aid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='酒币充值套餐配置表';

-- 创建酒币充值订单表
DROP TABLE IF EXISTS `ddwx_winecoin_recharge_order`;
CREATE TABLE `ddwx_winecoin_recharge_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) DEFAULT NULL COMMENT '平台ID',
  `mid` int(11) DEFAULT NULL COMMENT '用户ID',
  `ordernum` varchar(50) DEFAULT NULL COMMENT '订单号',
  `amount` decimal(10,2) DEFAULT '0.00' COMMENT '支付金额',
  `wine_coin_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '基础酒币数量',
  `bonus_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '赠送酒币数量',
  `bonus_score` int(11) DEFAULT '0' COMMENT '赠送积分数量',
  `total_wine_coin` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '总酒币数量',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态:0-待支付,1-已支付',
  `payorderid` int(11) NOT NULL DEFAULT '0' COMMENT '支付订单ID',
  `pay_time` int(11) NOT NULL DEFAULT '0' COMMENT '支付时间',
  `paytype` varchar(20) NOT NULL DEFAULT '' COMMENT '支付类型',
  `paytypeid` int(11) NOT NULL DEFAULT '0' COMMENT '支付类型ID',
  `paynum` varchar(50) NOT NULL DEFAULT '' COMMENT '支付流水号',
  `platform` varchar(20) NOT NULL DEFAULT 'wx' COMMENT '支付平台',
  `createtime` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_aid_mid` (`aid`,`mid`),
  KEY `idx_ordernum` (`ordernum`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='酒币充值订单表';

-- 插入默认配置
-- 注意：请根据你的实际平台ID修改aid值
INSERT INTO `ddwx_winecoin_recharge_package` (`aid`, `givedata`, `status`, `can_input`, `min_amount`, `max_amount`, `recharge_desc`, `createtime`) VALUES
(1, '[{"money":"10","give":"0","give_score":"0"},{"money":"50","give":"5","give_score":"0"},{"money":"100","give":"15","give_score":"0"},{"money":"200","give":"40","give_score":"0"},{"money":"500","give":"120","give_score":"0"},{"money":"1000","give":"300","give_score":"0"}]', 1, 1, 1.00, 10000.00, '支持微信支付、支付宝支付，实时到账', UNIX_TIMESTAMP());

-- ============================================
-- 安装说明：
-- 1. 请先备份数据库
-- 2. 修改上面的 aid 值为你的实际平台ID
-- 3. 执行此SQL脚本
-- 4. 登录后台：财务管理 → 酒币管理 → 充值套餐设置
-- ============================================

-- 查询语句，验证安装是否成功
SELECT * FROM `ddwx_winecoin_recharge_package` WHERE aid = 1;

-- 如果需要为多个平台创建配置，可以复制以下语句并修改aid值
-- INSERT INTO `ddwx_winecoin_recharge_package` (`aid`, `givedata`, `status`, `can_input`, `min_amount`, `max_amount`, `recharge_desc`, `createtime`) VALUES
-- (2, '[{"money":"10","give":"0","give_score":"0"},{"money":"50","give":"5","give_score":"0"}]', 1, 1, 1.00, 10000.00, '支持微信支付、支付宝支付，实时到账', UNIX_TIMESTAMP()); 