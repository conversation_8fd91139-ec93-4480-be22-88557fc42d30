html{font-family:sans-serif;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%}body{margin:0}article,aside,details,figcaption,figure,footer,header,hgroup,main,menu,nav,section,summary{display:block}audio,canvas,progress,video{display:inline-block;vertical-align:baseline}audio:not([controls]){display:none;height:0}[hidden],template{display:none}a{background-color:transparent}a:active,a:hover{outline:0}abbr[title]{border-bottom:1px dotted}b,strong{font-weight:700}dfn{font-style:italic}h1{margin:.67em 0;font-size:2em}mark{color:#000;background:#ff0}small{font-size:80%}sub,sup{position:relative;font-size:75%;line-height:0;vertical-align:baseline}sup{top:-.5em}sub{bottom:-.25em}img{border:0}svg:not(:root){overflow:hidden}figure{margin:1em 40px}hr{height:0;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box}pre{overflow:auto}code,kbd,pre,samp{font-family:monospace,monospace;font-size:1em}button,input,optgroup,select,textarea{margin:0;font:inherit;color:inherit}button{overflow:visible}button,select{text-transform:none}button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}button[disabled],html input[disabled]{cursor:default}button::-moz-focus-inner,input::-moz-focus-inner{padding:0;border:0}input{line-height:normal}input[type=checkbox],input[type=radio]{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;padding:0}input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button{height:auto}input[type=search]{-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;-webkit-appearance:textfield}input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-decoration{-webkit-appearance:none}fieldset{padding:.35em .625em .75em;margin:0 2px;border:1px solid silver}legend{padding:0;border:0}textarea{overflow:auto}optgroup{font-weight:700}table{border-spacing:0;border-collapse:collapse}td,th{padding:0}
h1, h2, h3, h4, h5, h6,span {
    margin: 0;
    padding: 0;
    font-size: 100%;
    font-weight: normal;
}

.flex {display: -webkit-box;display: -webkit-flex;display: flex;}
.flex1 {flex-grow: 1;flex-shrink: 1;}
.flex-row {display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-orient: horizontal;-webkit-flex-direction: row;flex-direction: row;}
.flex-col{display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-orient: vertical;-webkit-flex-direction: column;flex-direction: column;}
.flex-x-center{display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-pack: center;-webkit-justify-content: center;-ms-flex-pack: center;justify-content: center;}
.flex-y-center{display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-align: center;-webkit-align-items: center;-ms-flex-align: center;-ms-grid-row-align: center;align-items: center;}
.flex-y-bottom{display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-align: end;-webkit-align-items: flex-end;-ms-flex-align: end;-ms-grid-row-align: flex-end;align-items: flex-end;}

*{box-sizing: border-box;}
ul,li,p{list-style: none;margin: 0;padding: 0;}
a:hover, a:visited, a:active, a:focus {text-decoration: none;outline: 0;}
a{background-color: transparent;text-decoration: none;}
.top_content{  width: 100%; border:1px solid rgba(255,255,255,0.1);  position: absolute; z-index: 1000;}
.top_content .content{ width: 1200px; ; margin: auto; height:96px;line-height:96px; display: flex;  align-items: center; }
.top_content .content .logo{ align-items: center; display: flex;  display: flex;  align-items: center; color:#fff; margin-right: 30px; font-size: 28px;}
.top_content .content .logo img{ width:40px ; height: 40px; border-radius: 50%;margin-right: 20px;} 
.top_content .content .item{flex:1;justify-content:flex-end;margin-left:100px}
.top_content .content .item a{color:#fff;padding:0 30px; height: 96px; display: inline-block;}
.top_content .content .item a.on{ border-bottom: 4px solid #FFFFFF; }
.top_content .content .line{margin-left:20px;border-left: 1px solid #ccc;height: 20px;display: inline-block;padding-left: 30px;vertical-align: middle;}
.top_content .content .zixun{cursor:pointer;width:88px;height:32px;border-radius:4px;line-height:32px;text-align:center;font-size:13px;color:#005CFF;background: linear-gradient(90deg, #FFFFFF 0%, #FFFFFF 100%);}
.top_content .content .login{cursor:pointer;margin-left:16px;width:88px;height:32px;border-radius:4px;line-height:32px;text-align:center;font-size:13px;color:#fff;background: none;color:#fff; border: 1px solid #fff;}
/*顶部 结束*/

/*banner 开始*/
.bannerbox{width: 100%; height: 480px;}
.bannerbox .banner1{ width: 100%; height: 480px; background: url('../img/con_banner.png') no-repeat; background-size:100% 100%;padding-top: 190px;box-sizing: border-box;}

.contentbox{width:100%;background:#fff;background: #F5F5F5; padding-top: 30px;}
.contentbox .content{width:1200px;margin:0 auto;display:flex;padding-top:50px;padding-bottom:80px; background: #fff; padding: 54px; position: relative; }
.contentbox .content .left{display:flex;flex-direction: column; width: 350px;}
.contentbox .content .left .f1{display:flex;align-items:center;color:#333;font-weight:bold;font-size:28px}
.contentbox .content .left .f1 img{width:56px;height:56px; border-radius: 50%;}
.contentbox .content .left .f2{color:#494D5A;font-size:14px;margin-top:20px}
.contentbox .content .left .f2 span{ display: flex; line-height: 35px;}
.contentbox .right{display:flex;flex-direction:column;margin-left:10px;width: 600px;height: 494px;background: #FFFFFF;box-shadow: 0px 3px 32px 0px rgba(33, 58, 86, 0.16); margin-left: 100px; position: absolute; right:7% ; top:30px }
.contentbox .right .title{ text-align: center; font-size: 22px; padding-top: 35px;color: #191C3D;}
.contentbox .right .t1{color:#fff;font-size:16px;height:30px;line-height:30px;}
.contentbox .right .t2{color:#fff;opacity: 0.5;font-size:14px;}
.contentbox .right .con { color: #191C3D; font-size: 16px; display: flex; flex-direction: column;  align-items: center; justify-content: center; padding: 20px 60px; } 
.contentbox .right .con .inp1 { height: 63px; line-height: 63px;  color: #333; border: 0;border-bottom: 1px solid rgba(0,0,0,0.2);  margin: 10px;}
.contentbox .right .con .inp2 {  width: 80%; margin-top: 20px;  height: 134px;  line-height: 40px; color: #333;  border: 0; border-bottom: 1px solid rgba(0,0,0,0.2);}
.contentbox .right .con .btn1{ width: 100%; cursor: pointer;  height: 44px;line-height: 44px;  border-radius: 4px; color: #fff; font-size: 16px; margin-top: 30px;}
.contentbox .right .con .btn1 button{ background: none;background: #005CFF; padding:0 34px; border: none; float: right; border-radius: 4px; margin-right: 40px;}


.bottom{ height: 200px;width: 100%; height: 200px; background: url('../img/con_bottom.png') no-repeat; background-size:100% 100%;padding-top: 190px;box-sizing: border-box;}
.bottombox{width:100%;background:#151C29;padding:30px 0}
.bottombox .f1{display:flex;align-items:center;justify-content:center}
.bottombox .f1 a{color:#788BA6;font-size:14px;padding:0 20px;text-align:center;border-right:2px solid #788BA6}
.bottombox .f1 a:last-child{border-right:0}
.bottombox .f2{display:flex;align-items:center;justify-content:center;color:#6B798E;font-size:12px;margin-top:20px}

.layadmin-user-login-codeimg { margin: 10px; }

