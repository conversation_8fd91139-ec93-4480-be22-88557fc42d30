﻿// 纯JS省市区二级联动
var address2 = function(_cmbProvince, _cmbCity, defaultProvince, defaultCity){
	var cmbProvince = document.getElementById(_cmbProvince);
	var cmbCity = document.getElementById(_cmbCity);
	
	function cmbSelect(cmb, str){
		for(var i=0; i<cmb.options.length; i++){
			if(cmb.options[i].value == str){
				cmb.selectedIndex = i;
				break;
			}
		}
		try{
			layui.form.render('select');
		}catch(e){}
	}
	function cmbAddOption(cmb, str, obj){
		var option = document.createElement("OPTION");
		cmb.options.add(option);
		option.innerHTML = str;
		if(str == '省份' || str == '城市'){
			option.value = '';
		}else{
			option.value = str;
		}
		option.obj = obj;
	}
	function changeProvince(){
		cmbCity.options.length = 0;
		cmbCity.onchange = null;
		if(cmbProvince.selectedIndex == -1)return;
		var item = cmbProvince.options[cmbProvince.selectedIndex].obj;
		for(var i=0; i<item.cityList.length; i++){
			cmbAddOption(cmbCity, item.cityList[i]);
		}
		cmbSelect(cmbCity, defaultCity);
	}
	
	for(var i=0; i<provinceList.length; i++){
		cmbAddOption(cmbProvince, provinceList[i].name, provinceList[i]);
	}
	cmbSelect(cmbProvince, defaultProvince);
	changeProvince();
	
	cmbProvince.onchange = changeProvince;
	try{
		layui.form.on('select('+_cmbProvince+')', function(data){
		  changeProvince();
		});
	}catch(e){}
}

var provinceList = [
{name:'省份', cityList:['城市']},
{name:'安徽', cityList:['安庆','蚌埠','亳州','巢湖','滁州','池州','阜阳','淮北','合肥','淮南','黄山','六安','马鞍山','宿州','铜陵','芜湖','宣城']},
{name:'澳门', cityList:['大堂区','氹仔','风顺堂区','花地玛堂区','路环岛','圣安多尼堂区','望德堂区']},
{name:'北京', cityList:['昌平','朝阳','东城','大兴','房山','丰台','海淀','怀柔','门头沟','密云','平谷','石景山','顺义','通州','西城','延庆']},
{name:'重庆', cityList:['北碚','巴南','璧山','城口','长寿','大渡口','垫江','大足','丰都','奉节','涪陵','合川','江北','江津','九龙坡','开县','两江新区','梁平','南岸','南川','彭水','綦江','黔江','荣昌','沙坪坝','双桥','石柱','铜梁','潼南','武隆','巫山','万盛','巫溪','万州','秀山','渝北','永川','酉阳','云阳','渝中','忠县']},
{name:'福建', cityList:['福州','龙岩','宁德','南平','莆田','泉州','三明','厦门','漳州']},
{name:'甘肃', cityList:['白银','定西','甘南','金昌','酒泉','嘉峪关','陇南','临夏','兰州','平凉','庆阳','天水','武威','张掖']},
{name:'广东', cityList:['潮州','东莞','佛山','广州','河源','惠州','江门','揭阳','茂名','梅州','清远','韶关','汕头','汕尾','深圳','云浮','阳江','珠海','湛江','肇庆','中山']},
{name:'广西', cityList:['北海','百色','崇左','防城港','贵港','桂林','河池','贺州','来宾','柳州','南宁','钦州','梧州','玉林']},
{name:'贵州', cityList:['安顺','毕节','贵阳','六盘水','黔东南','黔南','黔西南','铜仁','遵义']},
{name:'海南', cityList:['白沙','保亭','昌江','澄迈','定安','东方','儋州','海口','乐东','临高','陵水','琼海','琼中','三沙','三亚','屯昌','文昌','万宁','五指山']},
{name:'河北', cityList:['保定','承德','沧州','邯郸','衡水','廊坊','秦皇岛','石家庄','唐山','邢台','张家口']},
{name:'黑龙江', cityList:['大庆','大兴安岭','哈尔滨','鹤岗','黑河','佳木斯','鸡西','牡丹江','齐齐哈尔','七台河','绥化','双鸭山','伊春']},
{name:'河南', cityList:['安阳','鹤壁','济源','焦作','开封','漯河','洛阳','南阳','平顶山','濮阳','三门峡','商丘','许昌','新乡','信阳','周口','驻马店','郑州']},
{name:'湖北', cityList:['恩施','鄂州','黄冈','黄石','荆门','荆州','潜江','神农架','十堰','随州','天门','武汉','孝感','咸宁','仙桃','襄阳','宜昌']},
{name:'湖南', cityList:['常德','长沙','郴州','怀化','衡阳','娄底','邵阳','湘潭','湘西','益阳','岳阳','永州','张家界','株洲']},
{name:'江苏', cityList:['常州','淮安','连云港','南京','南通','宿迁','苏州','泰州','无锡','徐州','盐城','扬州','镇江']},
{name:'江西', cityList:['抚州','赣州','吉安','景德镇','九江','南昌','萍乡','上饶','新余','宜春','鹰潭']},
{name:'吉林', cityList:['白城','白山','长春','吉林','辽源','四平','松原','通化','延边']},
{name:'辽宁', cityList:['鞍山','本溪','朝阳','丹东','大连','抚顺','阜新','葫芦岛','锦州','辽阳','盘锦','沈阳','铁岭','营口']},
{name:'内蒙古', cityList:['阿拉善','包头','巴彦淖尔','赤峰','鄂尔多斯','呼和浩特','呼伦贝尔','通辽','乌海','乌兰察布','兴安','锡林郭勒']},
{name:'宁夏', cityList:['固原','石嘴山','吴忠','银川','中卫']},
{name:'青海', cityList:['果洛','海北','海东','海南','黄南','海西','西宁','玉树']},
{name:'陕西', cityList:['安康','宝鸡','汉中','商洛','铜川','渭南','西安','咸阳','延安','榆林']},
{name:'山东', cityList:['滨州','东营','德州','荷泽','济南','济宁','聊城','莱芜','临沂','青岛','日照','泰安','潍坊','威海','烟台','淄博','枣庄']},
{name:'上海', cityList:['宝山','崇明','长宁','奉贤','虹口','黄浦','静安','嘉定','金山','卢湾','闵行','浦东新区','普陀','青浦','松江','徐汇','杨浦','闸北']},
{name:'山西', cityList:['长治','大同','晋城','晋中','临汾','吕梁','朔州','太原','忻州','运城','阳泉']},
{name:'四川', cityList:['阿坝','巴中','成都','德阳','达州','广安','广元','甘孜','乐山','凉山','泸州','眉山','绵阳','南充','内江','攀枝花','遂宁','雅安','宜宾','自贡','资阳']},
{name:'台湾', cityList:['高雄','花莲','基隆','金门','嘉义市','嘉义县','连江','苗栗','南投','屏东','澎湖','台北','台东','台南','桃园','台中','新北','新竹','云林','宜兰','彰化']},
{name:'天津', cityList:['北辰','宝坻','滨海新区','东丽','河北','河东','和平','红桥','河西','静海','津南','蓟县','宁河','南开','武清','西青']},
{name:'香港', cityList:['北区','大埔区','东区','观塘区','黄大仙区','九龙城区','葵青区','离岛区','南区','荃湾区','深水埗区','沙田区','屯门区','湾仔区','西贡区','油尖旺区','元朗区','中西区']},
{name:'新疆', cityList:['阿克苏','阿拉尔','阿勒泰','博尔塔拉','巴音郭楞','昌吉','哈密','和田','克拉玛依','喀什','克孜勒苏','石河子','塔城','吐鲁番','图木舒克','五家渠','乌鲁木齐','伊犁']},
{name:'西藏', cityList:['阿里','昌都','拉萨','林芝','那曲','日喀则','山南']},
{name:'云南', cityList:['思茅','保山','楚雄','德宏','大理','迪庆','红河','昆明','临沧','丽江','怒江','普洱','曲靖','文山','西双版纳','玉溪','昭通']},
{name:'浙江', cityList:['杭州','湖州','金华','嘉兴','丽水','宁波','衢州','绍兴','台州','温州','舟山']},
];