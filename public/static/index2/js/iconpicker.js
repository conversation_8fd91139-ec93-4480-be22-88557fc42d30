var iconpicker = {
	icondatas : [{id:"glass",cat:"Web Application",keywords:"martini,drink,bar,alcohol,liquor,玻璃杯,杯子,高脚杯"},{id:"music",cat:"Web Application",keywords:"note,sound,song,音乐,音符,音频,乐曲,"},{id:"search",cat:"Web Application",keywords:"magnify,zoom,enlarge,bigger,搜索,放大镜"},{id:"envelope-o",cat:"Web Application",keywords:"email,support,e-mail,letter,mail,notification,信封,邮件,信息,消息,通知"},{id:"heart",cat:"Web Application,Medical",keywords:"love,like,favorite,心,喜欢,爱心"},{id:"star",cat:"Web Application",keywords:"award,achievement,night,rating,score,favorite,五角星,星星,收藏,奖章,标星"},{id:"star-o",cat:"Web Application",keywords:"award,achievement,night,rating,score,favorite,五角星,星星,收藏,奖章,标星"},{id:"user",cat:"Web Application",keywords:"person,man,head,profile,用户,人,头像"},{id:"film",cat:"Web Application",keywords:"movie,电影,胶片"},{id:"th-large",cat:"Text Editor",keywords:"blocks,squares,boxes,grid,列表,菜单"},{id:"th",cat:"Text Editor",keywords:"blocks,squares,boxes,grid,列表,菜单"},{id:"th-list",cat:"Text Editor",keywords:"ul,ol,checklist,finished,completed,done,todo,menu,列表,菜单"},{id:"check",cat:"Web Application",keywords:"checkmark,done,todo,agree,accept,confirm,tick,ok,对号,正确,成功"},{id:"times",cat:"Web Application",keywords:"close,exit,x,cross,remove,times,关闭,删除,乘,错,叉,失败",aliases:"remove,close"},{id:"search-plus",cat:"Web Application",keywords:"magnify,zoom,enlarge,bigger,搜索,放大,缩放,放大镜"},{id:"search-minus",cat:"Web Application",keywords:"magnify,minify,zoom,smaller,搜索,缩小,缩放,放大镜"},{id:"power-off",cat:"Web Application",keywords:"on,关机,电源,关闭"},{id:"signal",cat:"Web Application",keywords:"graph,bars,信号,wifi"},{id:"cog",cat:"Web Application,Spinner",keywords:"settings,cog,setting,设置,齿轮",aliases:"gear"},{id:"trash-o",cat:"Web Application",keywords:"garbage,delete,remove,trash,hide,垃圾桶,回收站,删除"},{id:"home",cat:"Web Application",keywords:"main,house,家,房子,主页,首页"},{id:"file-o",cat:"Text Editor,File Type",keywords:"new,page,pdf,document,blank,文件,新建,空白页,文本"},{id:"clock-o",cat:"Web Application",keywords:"watch,timer,late,timestamp,时钟,闹钟,时间,表"},{id:"road",cat:"Web Application",keywords:"street,路,交通"},{id:"download",cat:"Web Application",keywords:"import,下载,缓存"},{id:"arrow-circle-o-down",cat:"Directional",keywords:"download,down,下,向下,箭头,方向"},{id:"arrow-circle-o-up",cat:"Directional",keywords:"up,向上,上,箭头,方向"},{id:"inbox",cat:"Web Application",keywords:"收件箱"},{id:"play-circle-o",cat:"Video Player",keywords:"播放,开始"},{id:"repeat",cat:"Text Editor",keywords:"redo,forward,repeat,右旋转,重做,旋转",aliases:"rotate-right"},{id:"refresh",cat:"Web Application,Spinner",keywords:"reload,sync,刷新,循环"},{id:"list-alt",cat:"Text Editor",keywords:"ul,ol,checklist,finished,completed,done,todo,列表,菜单"},{id:"lock",cat:"Web Application",keywords:"protect,admin,security,锁"},{id:"flag",cat:"Web Application",keywords:"report,notification,notify,旗帜,旗子"},{id:"headphones",cat:"Web Application",keywords:"sound,listen,music,audio,耳机"},{id:"volume-off",cat:"Web Application",keywords:"audio,mute,sound,music,音量,音频,喇叭"},{id:"volume-down",cat:"Web Application",keywords:"audio,lower,quieter,sound,music,音量,喇叭,音频"},{id:"volume-up",cat:"Web Application",keywords:"audio,higher,louder,sound,music,音量,喇叭,音频"},{id:"qrcode",cat:"Web Application",keywords:"scan,二维码"},{id:"barcode",cat:"Web Application",keywords:"scan,条形码"},{id:"tag",cat:"Web Application",keywords:"label,shopping,price,标签,购物,价格"},{id:"tags",cat:"Web Application",keywords:"labels,标签,购物,价格"},{id:"book",cat:"Web Application",keywords:"read,documentation,书,指南"},{id:"bookmark",cat:"Web Application",keywords:"save,mark,书签,标签,标记"},{id:"print",cat:"Web Application",keywords:"打印"},{id:"camera",cat:"Web Application",keywords:"photo,picture,record,镜头,照相机,拍照"},{id:"font",cat:"Text Editor",keywords:"text,字体,文本,编辑"},{id:"bold",cat:"Text Editor",keywords:"粗体,,文本,编辑"},{id:"italic",cat:"Text Editor",keywords:"italics,斜体,文本,编辑"},{id:"text-height",cat:"Text Editor",keywords:"文字高度,文本,编辑"},{id:"text-width",cat:"Text Editor",keywords:"文字宽度,文本,编辑"},{id:"align-left",cat:"Text Editor",keywords:"text,对齐,文本,编辑"},{id:"align-center",cat:"Text Editor",keywords:"middle,text,对齐,文本,编辑,段落"},{id:"align-right",cat:"Text Editor",keywords:"text,对齐,文本,编辑,段落"},{id:"align-justify",cat:"Text Editor",keywords:"text,对齐,文本,编辑,段落"},{id:"list",cat:"Text Editor",keywords:"ul,ol,checklist,finished,completed,done,todo,列表,段落,文本,编辑"},{id:"outdent",cat:"Text Editor",keywords:"outdent,缩进,段落,文本,编辑",aliases:"dedent"},{id:"indent",cat:"Text Editor",keywords:"缩进,段落,文本,编辑"},{id:"video-camera",cat:"Web Application",keywords:"film,movie,record,视频,摄像机,录像"},{id:"picture-o",cat:"Web Application",keywords:"photo,picture-o,图片,图库,相册,照片",aliases:"photo,image"},{id:"pencil",cat:"Web Application",keywords:"write,edit,update,铅笔,编辑,修改"},{id:"map-marker",cat:"Web Application",keywords:"map,pin,location,coordinates,localize,address,travel,where,place,地图,位置,标记"},{id:"adjust",cat:"Web Application",keywords:"contrast,滤镜,对比度,饱和度"},{id:"tint",cat:"Web Application",keywords:"raindrop,waterdrop,drop,droplet,滤镜,模糊,水滴"},{id:"pencil-square-o",cat:"Web Application",keywords:"write,edit,update,pencil-square-o,编辑,铅笔,修改",aliases:"edit"},{id:"share-square-o",cat:"Web Application",keywords:"social,send,arrow,分享"},{id:"check-square-o",cat:"Web Application,Form Control",keywords:"todo,done,agree,accept,confirm,ok,对号,选中,多选,表单"},{id:"arrows",cat:"Web Application,Directional",keywords:"move,reorder,resize,箭头,移动,方向"},{id:"step-backward",cat:"Video Player",keywords:"rewind,previous,beginning,start,first,后退,上一段,箭头"},{id:"fast-backward",cat:"Video Player",keywords:"rewind,previous,beginning,start,first,快退,箭头"},{id:"backward",cat:"Video Player",keywords:"rewind,previous,后退,箭头"},{id:"play",cat:"Video Player",keywords:"start,playing,music,sound,播放,开始"},{id:"pause",cat:"Video Player",keywords:"wait,暂停"},{id:"stop",cat:"Video Player",keywords:"block,box,square,停止"},{id:"forward",cat:"Video Player",keywords:"forward,next,前进,箭头"},{id:"fast-forward",cat:"Video Player",keywords:"next,end,last,快进,箭头"},{id:"step-forward",cat:"Video Player",keywords:"next,end,last,前进,下一段,箭头"},{id:"eject",cat:"Video Player",keywords:"弹出"},{id:"chevron-left",cat:"Directional",keywords:"bracket,previous,back,返回,左箭头,后退,上一步"},{id:"chevron-right",cat:"Directional",keywords:"bracket,next,forward,前进,右箭头,下一步"},{id:"plus-circle",cat:"Web Application",keywords:"add,new,create,expand,加"},{id:"minus-circle",cat:"Web Application",keywords:"delete,remove,trash,hide,减号,横线"},{id:"times-circle",cat:"Web Application",keywords:"close,exit,x,fail,关闭,删除,乘,失败,错误"},{id:"check-circle",cat:"Web Application",keywords:"todo,done,agree,accept,confirm,ok,success,对号,正确,成功"},{id:"question-circle",cat:"Web Application",keywords:"help,information,unknown,support,问号,问题,帮助"},{id:"info-circle",cat:"Web Application",keywords:"help,information,more,details,提示,说明,信息"},{id:"crosshairs",cat:"Web Application",keywords:"picker,map,location,定位,归位,瞄准,选中,地图"},{id:"times-circle-o",cat:"Web Application",keywords:"close,exit,x,fail,关闭,删除,乘,失败,错误"},{id:"check-circle-o",cat:"Web Application",keywords:"todo,done,agree,accept,confirm,ok,success,对号,正确,成功"},{id:"ban",cat:"Web Application",keywords:"delete,remove,trash,hide,block,stop,abort,cancel,禁止"},{id:"arrow-left",cat:"Directional",keywords:"previous,back,左箭头,向左,方向"},{id:"arrow-right",cat:"Directional",keywords:"next,forward,右箭头,向右,方向"},{id:"arrow-up",cat:"Directional",keywords:"up,上箭头,向上,方向"},{id:"arrow-down",cat:"Directional",keywords:"download,down,下箭头,向下,方向"},{id:"share",cat:"Web Application",keywords:"分享",aliases:"mail-forward"},{id:"expand",cat:"Video Player",keywords:"enlarge,bigger,resize,放大,全屏"},{id:"compress",cat:"Video Player",keywords:"collapse,combine,contract,merge,smaller,压缩,缩小,挤压"},{id:"plus",cat:"Web Application",keywords:"add,new,create,expand,加号"},{id:"minus",cat:"Web Application",keywords:"hide,minify,delete,remove,trash,hide,collapse,减号,横线"},{id:"asterisk",cat:"Web Application",keywords:"details,星号,米,密码"},{id:"exclamation-circle",cat:"Web Application",keywords:"warning,error,problem,notification,alert,感叹号,注意,提醒"},{id:"gift",cat:"Web Application",keywords:"present,礼物,奖品"},{id:"leaf",cat:"Web Application",keywords:"eco,nature,plant,树叶"},{id:"fire",cat:"Web Application",keywords:"flame,hot,popular,火,蜡烛"},{id:"eye",cat:"Web Application",keywords:"show,visible,views,预览,眼睛,查看,显示"},{id:"eye-slash",cat:"Web Application",keywords:"toggle,show,hide,visible,visiblity,views,预览,眼睛,隐藏"},{id:"exclamation-triangle",cat:"Web Application",keywords:"warning,error,problem,notification,alert,exclamation-triangle,感叹号,警告,注意",aliases:"warning"},{id:"plane",cat:"Web Application,Transportation",keywords:"travel,trip,location,destination,airplane,fly,mode,飞机"},{id:"calendar",cat:"Web Application",keywords:"date,time,when,event,日历"},{id:"random",cat:"Web Application,Video Player",keywords:"sort,shuffle,随机,播放,音频"},{id:"comment",cat:"Web Application",keywords:"speech,notification,note,chat,bubble,feedback,message,texting,sms,conversation,评论,信息,消息"},{id:"magnet",cat:"Web Application",keywords:"磁铁"},{id:"chevron-up",cat:"Directional",keywords:"up,向上,上箭头"},{id:"chevron-down",cat:"Directional",keywords:"down,向下,下箭头"},{id:"retweet",cat:"Web Application",keywords:"refresh,reload,share,转推,转发"},{id:"shopping-cart",cat:"Web Application",keywords:"checkout,buy,purchase,payment,购物车,购物"},{id:"folder",cat:"Web Application",keywords:"文件夹"},{id:"folder-open",cat:"Web Application",keywords:"文件夹"},{id:"arrows-v",cat:"Web Application,Directional",keywords:"resize,箭头"},{id:"arrows-h",cat:"Web Application,Directional",keywords:"resize,箭头"},{id:"bar-chart",cat:"Web Application,Chart",keywords:"graph,analytics,条形图,图表",aliases:"bar-chart-o"},{id:"twitter-square",cat:"Brand",keywords:"tweet,social network,推特"},{id:"facebook-square",cat:"Brand",keywords:"social network,脸书"},{id:"camera-retro",cat:"Web Application",keywords:"photo,picture,record,镜头,照相机,拍照,拍摄"},{id:"key",cat:"Web Application",keywords:"unlock,password,钥匙,密码,解锁"},{id:"cogs",cat:"Web Application",keywords:"settings,cogs,settings,设置,齿轮",aliases:"gears"},{id:"comments",cat:"Web Application",keywords:"speech,notification,note,chat,bubble,feedback,message,texting,sms,conversation,评论,留言"},{id:"thumbs-o-up",cat:"Web Application,Hand",keywords:"like,approve,favorite,agree,hand,大拇指,赞,顶,支持,认同"},{id:"thumbs-o-down",cat:"Web Application,Hand",keywords:"dislike,disapprove,disagree,hand,大拇指,不支持,踩"},{id:"star-half",cat:"Web Application",keywords:"award,achievement,rating,score,五角星,星星"},{id:"heart-o",cat:"Web Application,Medical",keywords:"love,like,favorite,心,喜欢,爱"},{id:"sign-out",cat:"Web Application",keywords:"log out,logout,leave,exit,arrow,注销,退出"},{id:"linkedin-square",cat:"Brand",keywords:"logo,领英"},{id:"thumb-tack",cat:"Web Application",keywords:"marker,pin,location,coordinates,标记,标签,锁定,大头针,图钉,地图"},{id:"external-link",cat:"Web Application",keywords:"open,new,链接,分享"},{id:"sign-in",cat:"Web Application",keywords:"enter,join,log in,login,sign up,sign in,signin,signup,arrow,登录,进入,加入"},{id:"trophy",cat:"Web Application",keywords:"award,achievement,cup,winner,game,奖品,奖杯,获奖,赢,冠军"},{id:"github-square",cat:"Brand",keywords:"octocat"},{id:"upload",cat:"Web Application",keywords:"import,上传"},{id:"lemon-o",cat:"Web Application",keywords:"food,柠檬,水果"},{id:"phone",cat:"Web Application",keywords:"call,voice,number,support,earphone,telephone,电话,拨打,通话"},{id:"square-o",cat:"Web Application,Form Control",keywords:"block,square,box,方块,矩形,形状,多选"},{id:"bookmark-o",cat:"Web Application",keywords:"save,书签,标签,标记"},{id:"phone-square",cat:"Web Application",keywords:"call,voice,number,support,telephone,电话"},{id:"twitter",cat:"Brand",keywords:"tweet,social network,推特,社交"},{id:"facebook",cat:"Brand",keywords:"social network,脸书,社交",aliases:"facebook-f"},{id:"github",cat:"Brand",keywords:"octocat"},{id:"unlock",cat:"Web Application",keywords:"protect,admin,password,lock,解锁,锁头"},{id:"credit-card",cat:"Web Application,Payment",keywords:"money,buy,debit,checkout,purchase,payment,信用卡,银行卡,付款,购买"},{id:"rss",cat:"Web Application",keywords:"blog",aliases:"feed"},{id:"hdd-o",cat:"Web Application",keywords:"harddrive,hard drive,storage,save,硬盘,盘,存储"},{id:"bullhorn",cat:"Web Application",keywords:"announcement,share,broadcast,louder,megaphone,放大器,扩音器,话筒,喇叭"},{id:"bell",cat:"Web Application",keywords:"alert,reminder,notification,ring,铃铛,铃声,新消息,提醒"},{id:"certificate",cat:"Web Application",keywords:"badge,star,奖章,星星"},{id:"hand-o-right",cat:"Directional,Hand",keywords:"point,right,next,forward,finger,手指,向右,方向"},{id:"hand-o-left",cat:"Directional,Hand",keywords:"point,left,previous,back,finger,手指,向左,方向"},{id:"hand-o-up",cat:"Directional,Hand",keywords:"point,finger,手指,向上,方向"},{id:"hand-o-down",cat:"Directional,Hand",keywords:"point,finger,手指,向下,方向"},{id:"arrow-circle-left",cat:"Directional",keywords:"previous,back,向左,左箭头,方向"},{id:"arrow-circle-right",cat:"Directional",keywords:"next,forward,向右,右箭头,方向"},{id:"arrow-circle-up",cat:"Directional",keywords:"向上,上箭头,方向"},{id:"arrow-circle-down",cat:"Directional",keywords:"download,向下,下箭头,方向"},{id:"globe",cat:"Web Application",keywords:"world,planet,map,place,travel,earth,global,translate,all,language,localize,location,coordinates,country,地球"},{id:"wrench",cat:"Web Application",keywords:"settings,fix,update,spanner,扳手,修改,设置,工具"},{id:"tasks",cat:"Web Application",keywords:"progress,loading,downloading,downloads,settings,todolist,任务,列表"},{id:"filter",cat:"Web Application",keywords:"funnel,options,过滤,漏斗"},{id:"briefcase",cat:"Web Application",keywords:"work,business,office,luggage,bag,box,工具箱,治疗,医疗,箱子"},{id:"arrows-alt",cat:"Video Player,Directional",keywords:"expand,enlarge,fullscreen,bigger,move,reorder,resize,arrow,放大,全屏,移动,箭头,方向"},{id:"users",cat:"Web Application",keywords:"people,profiles,persons,group,用户,讨论组,人,通讯录",aliases:"group"},{id:"link",cat:"Text Editor",keywords:"chain,链接",aliases:"chain"},{id:"cloud",cat:"Web Application",keywords:"save,云"},{id:"flask",cat:"Web Application",keywords:"science,beaker,experimental,labs,chemical,烧瓶,烧杯,化学"},{id:"scissors",cat:"Text Editor",keywords:"scissors,剪切,剪刀,裁切",aliases:"cut"},{id:"files-o",cat:"Text Editor",keywords:"duplicate,clone,copy,复制",aliases:"copy"},{id:"paperclip",cat:"Text Editor",keywords:"attachment,回形针,附件"},{id:"floppy-o",cat:"Text Editor",keywords:"floppy,保存,软盘",aliases:"save"},{id:"square",cat:"Web Application,Form Control",keywords:"block,box,方块,方块,矩形"},{id:"bars",cat:"Web Application",keywords:"menu,drag,reorder,settings,list,ul,ol,checklist,todo,list,hamburger,bars,navicon,排序,列表,菜单,更多",aliases:"navicon,reorder"},{id:"list-ul",cat:"Text Editor",keywords:"ul,ol,checklist,todo,list,列表,菜单,排列,序号"},{id:"list-ol",cat:"Text Editor",keywords:"ul,ol,checklist,list,todo,list,numbers,列表,菜单,排列,序号"},{id:"strikethrough",cat:"Text Editor",keywords:"删除线,文本,编辑"},{id:"underline",cat:"Text Editor",keywords:"下划线,文本,编辑"},{id:"table",cat:"Text Editor",keywords:"data,excel,spreadsheet,表格,日历"},{id:"magic",cat:"Web Application",keywords:"wizard,automatic,autocomplete,魔法,图片,滤镜,美图"},{id:"truck",cat:"Web Application,Transportation",keywords:"shipping,traffic,卡车,汽车,交通,运输"},{id:"pinterest",cat:"Brand",keywords:"logo"},{id:"pinterest-square",cat:"Brand",keywords:"logo"},{id:"google-plus-square",cat:"Brand",keywords:"social network"},{id:"google-plus",cat:"Brand",keywords:"social network"},{id:"money",cat:"Web Application,Currency",keywords:"cash,money,buy,checkout,purchase,payment,钱,钞票,货币,纸币,支付,购买"},{id:"caret-down",cat:"Directional",keywords:"more,dropdown,menu,triangle down,arrow,下箭头,方向,向下"},{id:"caret-up",cat:"Directional",keywords:"triangle up,arrow,上箭头,方向,向上"},{id:"caret-left",cat:"Directional",keywords:"previous,back,triangle left,arrow,左箭头,向左,方向"},{id:"caret-right",cat:"Directional",keywords:"next,forward,triangle right,arrow,右箭头,向右,方向"},{id:"columns",cat:"Text Editor",keywords:"split,panes,列"},{id:"sort",cat:"Web Application",keywords:"order,排序,排列,表单",aliases:"unsorted"},{id:"sort-desc",cat:"Web Application",keywords:"dropdown,more,menu,arrow,排序,排列,下拉",aliases:"sort-down"},{id:"sort-asc",cat:"Web Application",keywords:"arrow,排序,排列",aliases:"sort-up"},{id:"envelope",cat:"Web Application",keywords:"email,e-mail,letter,support,mail,notification,信封,邮件,消息,信息"},{id:"linkedin",cat:"Brand",keywords:"领英"},{id:"undo",cat:"Text Editor",keywords:"back,undo,左旋转,撤销,箭头",aliases:"rotate-left"},{id:"gavel",cat:"Web Application",keywords:"judge,lawyer,opinion,legal,法律,法院,锤子,敲击",aliases:"legal"},{id:"tachometer",cat:"Web Application",keywords:"speedometer,fast,tachometer,仪表盘,汽车,交通",aliases:"dashboard"},{id:"comment-o",cat:"Web Application",keywords:"speech,notification,note,chat,bubble,feedback,message,texting,sms,conversation,评论,消息,评论,留言"},{id:"comments-o",cat:"Web Application",keywords:"speech,notification,note,chat,bubble,feedback,message,texting,sms,conversation,评论,消息,评论,留言"},{id:"bolt",cat:"Web Application",keywords:"lightning,weather,bolt,闪电,闪光灯,天气",aliases:"flash"},{id:"sitemap",cat:"Web Application",keywords:"directory,hierarchy,organization,组织,结构,架构,等级,图表"},{id:"umbrella",cat:"Web Application",keywords:"雨伞,天气,下雨"},{id:"clipboard",cat:"Text Editor",keywords:"copy,clipboard,粘贴",aliases:"paste"},{id:"lightbulb-o",cat:"Web Application",keywords:"idea,inspiration,灯泡,电灯"},{id:"exchange",cat:"Web Application,Directional",keywords:"transfer,arrows,arrow,交换,箭头,转换"},{id:"cloud-download",cat:"Web Application",keywords:"import,云,下载"},{id:"cloud-upload",cat:"Web Application",keywords:"import,云,上传"},{id:"user-md",cat:"Medical",keywords:"doctor,profile,medical,nurse,医生,医疗,人,医院"},{id:"stethoscope",cat:"Medical",keywords:"hospital,听诊器,医生,医疗,医院"},{id:"suitcase",cat:"Web Application",keywords:"trip,luggage,travel,move,baggage,手提箱,箱子,公文包"},{id:"bell-o",cat:"Web Application",keywords:"alert,reminder,notification,铃铛,提醒,响铃"},{id:"coffee",cat:"Web Application",keywords:"morning,mug,breakfast,tea,drink,cafe,咖啡,美食,食物,杯子,饭店,餐厅"},{id:"cutlery",cat:"Web Application",keywords:"food,restaurant,spoon,knife,dinner,eat,餐具,刀叉,美食,食物,餐厅,饭店"},{id:"file-text-o",cat:"Text Editor,File Type",keywords:"new,page,pdf,document,文件"},{id:"building-o",cat:"Web Application",keywords:"work,business,apartment,office,company,建筑,楼,大厦,公司"},{id:"hospital-o",cat:"Medical",keywords:"building,建筑,楼,大厦,公司"},{id:"ambulance",cat:"Medical,Transportation",keywords:"vehicle,support,help,医疗,救护车,救助,医院"},{id:"medkit",cat:"Medical",keywords:"first aid,firstaid,help,support,health,治疗箱,手提包,医院,医疗,治疗,急救包,急救"},{id:"fighter-jet",cat:"Web Application,Transportation",keywords:"fly,plane,airplane,quick,fast,travel,飞机,飞行"},{id:"beer",cat:"Web Application",keywords:"alcohol,stein,drink,mug,bar,liquor,啤酒,酒杯,杯子,食物,餐厅"},{id:"h-square",cat:"Medical",keywords:"hospital,hotel,酒店,旅馆,宾馆"},{id:"plus-square",cat:"Medical,Web Application,Form Control",keywords:"add,new,create,expand,加,加号,医疗,医院"},{id:"angle-double-left",cat:"Directional",keywords:"laquo,quote,previous,back,arrows,方向,箭头,左"},{id:"angle-double-right",cat:"Directional",keywords:"raquo,quote,next,forward,arrows,方向,箭头,右"},{id:"angle-double-up",cat:"Directional",keywords:"arrows,方向,箭头,上"},{id:"angle-double-down",cat:"Directional",keywords:"arrows,方向,箭头,下"},{id:"angle-left",cat:"Directional",keywords:"previous,back,arrow,方向,箭头,左"},{id:"angle-right",cat:"Directional",keywords:"next,forward,arrow,方向,箭头,右"},{id:"angle-up",cat:"Directional",keywords:"arrow,,方向,箭头,上"},{id:"angle-down",cat:"Directional",keywords:"arrow,方向,箭头,下"},{id:"desktop",cat:"Web Application",keywords:"monitor,screen,desktop,computer,demo,device,pc,电脑,桌面"},{id:"laptop",cat:"Web Application",keywords:"demo,computer,device,pc,笔记本,电脑"},{id:"tablet",cat:"Web Application",keywords:"ipad,device,平板,iPad"},{id:"mobile",cat:"Web Application",keywords:"cell phone,cellphone,text,call,iphone,number,telephone,mobile-phone,手机,电话",aliases:"mobile-phone"},{id:"circle-o",cat:"Web Application,Form Control",keywords:"圆圈"},{id:"quote-left",cat:"Web Application",keywords:"引号,引用"},{id:"quote-right",cat:"Web Application",keywords:"引号,引用"},{id:"spinner",cat:"Web Application,Spinner",keywords:"loading,progress,加载"},{id:"circle",cat:"Web Application,Form Control",keywords:"dot,notification,圆圈"},{id:"reply",cat:"Web Application",keywords:"回复",aliases:"mail-reply"},{id:"github-alt",cat:"Brand",keywords:"octocat"},{id:"folder-o",cat:"Web Application",keywords:"文件夹"},{id:"folder-open-o",cat:"Web Application",keywords:"文件夹"},{id:"smile-o",cat:"Web Application",keywords:"face,emoticon,happy,approve,satisfied,rating,微笑,开心,表情"},{id:"frown-o",cat:"Web Application",keywords:"face,emoticon,sad,disapprove,rating,皱眉,难过,表情,伤心"},{id:"meh-o",cat:"Web Application",keywords:"face,emoticon,rating,neutral,无语,表情"},{id:"gamepad",cat:"Web Application",keywords:"controller,游戏机,手柄"},{id:"keyboard-o",cat:"Web Application",keywords:"type,input,键盘,pc,电脑"},{id:"flag-o",cat:"Web Application",keywords:"report,notification,旗帜,旗子"},{id:"flag-checkered",cat:"Web Application",keywords:"report,notification,notify,旗帜,旗子"},{id:"terminal",cat:"Web Application",keywords:"command,prompt,code,终端,代码,pc"},{id:"code",cat:"Web Application",keywords:"html,brackets,代码,pc"},{id:"reply-all",cat:"Web Application",keywords:"回复所有",aliases:"mail-reply-all"},{id:"star-half-o",cat:"Web Application",keywords:"award,achievement,rating,score,五角星,星星",aliases:"star-half-empty,star-half-full"},{id:"location-arrow",cat:"Web Application",keywords:"map,coordinates,location,address,place,where,位置,导航,方向,箭头"},{id:"crop",cat:"Web Application",keywords:"裁剪,裁切"},{id:"code-fork",cat:"Web Application",keywords:"git,fork,vcs,svn,github,rebase,version,merge,代码"},{id:"chain-broken",cat:"Text Editor",keywords:"remove,chain-broken,删除链接",aliases:"unlink"},{id:"question",cat:"Web Application",keywords:"help,information,unknown,support,问号,问题"},{id:"info",cat:"Web Application",keywords:"help,information,more,details,提示,注意"},{id:"exclamation",cat:"Web Application",keywords:"warning,error,problem,notification,notify,alert,感叹号"},{id:"superscript",cat:"Text Editor",keywords:"exponential,上标,数学,公式"},{id:"subscript",cat:"Text Editor",keywords:"下标,脚注,数学,公式"},{id:"eraser",cat:"Text Editor,Web Application",keywords:"remove,delete,橡皮擦,橡皮"},{id:"puzzle-piece",cat:"Web Application",keywords:"addon,add-on,section,拼图"},{id:"microphone",cat:"Web Application",keywords:"record,voice,sound,麦克风,话筒,语音"},{id:"microphone-slash",cat:"Web Application",keywords:"record,voice,sound,mute,麦克风,话筒,语音,静音"},{id:"shield",cat:"Web Application",keywords:"award,achievement,security,winner,盾牌,安全,保护"},{id:"calendar-o",cat:"Web Application",keywords:"date,time,when,event,日历"},{id:"fire-extinguisher",cat:"Web Application",keywords:"灭火器,消防"},{id:"rocket",cat:"Web Application,Transportation",keywords:"app,火箭,运行,飞行"},{id:"maxcdn",cat:"Brand",keywords:""},{id:"chevron-circle-left",cat:"Directional",keywords:"previous,back,arrow,左箭头,方向,后退"},{id:"chevron-circle-right",cat:"Directional",keywords:"next,forward,arrow,右箭头,方向,前进"},{id:"chevron-circle-up",cat:"Directional",keywords:"arrow,向上,方向,箭头"},{id:"chevron-circle-down",cat:"Directional",keywords:"more,dropdown,menu,arrow,向下,方向,箭头"},{id:"html5",cat:"Brand",keywords:"h5,web,网页,开发"},{id:"css3",cat:"Brand",keywords:"code,网页,开发"},{id:"anchor",cat:"Web Application",keywords:"link,锚"},{id:"unlock-alt",cat:"Web Application",keywords:"protect,admin,password,lock,解锁,锁头"},{id:"bullseye",cat:"Web Application",keywords:"target,靶子,靶,圆心"},{id:"ellipsis-h",cat:"Web Application",keywords:"dots,省略号,更多"},{id:"ellipsis-v",cat:"Web Application",keywords:"dots,省略号,更多"},{id:"rss-square",cat:"Web Application",keywords:"feed,blog,订阅,博客"},{id:"play-circle",cat:"Video Player",keywords:"start,playing,播放"},{id:"ticket",cat:"Web Application",keywords:"movie,pass,support,票,电影票"},{id:"minus-square",cat:"Web Application,Form Control",keywords:"hide,minify,delete,remove,trash,hide,collapse,减号,横线,减号"},{id:"minus-square-o",cat:"Web Application,Form Control",keywords:"hide,minify,delete,remove,trash,hide,collapse,减号,横线,减号"},{id:"level-up",cat:"Web Application",keywords:"arrow,向上"},{id:"level-down",cat:"Web Application",keywords:"arrow,向下"},{id:"check-square",cat:"Web Application,Form Control",keywords:"checkmark,done,todo,agree,accept,confirm,ok,对号,对号"},{id:"pencil-square",cat:"Web Application",keywords:"write,edit,update,铅笔"},{id:"external-link-square",cat:"Web Application",keywords:"open,new,链接,分享,打开"},{id:"share-square",cat:"Web Application",keywords:"social,send,分享"},{id:"compass",cat:"Web Application",keywords:"safari,directory,menu,location,指南针,方向,地图,浏览器"},{id:"caret-square-o-down",cat:"Web Application,Directional",keywords:"more,dropdown,menu,三角,caret-square-o-down,下",aliases:"toggle-down"},{id:"caret-square-o-up",cat:"Web Application,Directional",keywords:"三角,caret-square-o-up,上",aliases:"toggle-up"},{id:"caret-square-o-right",cat:"Web Application,Directional",keywords:"next,forward,三角,caret-square-o-right,右",aliases:"toggle-right"},{id:"eur",cat:"Currency",keywords:"eur,欧元,货币,钱",aliases:"euro"},{id:"gbp",cat:"Currency",keywords:"英镑,货币,钱"},{id:"usd",cat:"Currency",keywords:"usd,美元,货币,钱",aliases:"dollar"},{id:"inr",cat:"Currency",keywords:"印度,货币,钱",aliases:"rupee"},{id:"jpy",cat:"Currency",keywords:"rmb,yen,jpy,人民币,日元",aliases:"cny,rmb,yen"},{id:"rub",cat:"Currency",keywords:"卢布,货币,钱",aliases:"ruble,rouble"},{id:"krw",cat:"Currency",keywords:"won,货币,钱",aliases:"won"},{id:"btc",cat:"Currency,Brand",keywords:"btc,货币,钱,比特币",aliases:"bitcoin"},{id:"file",cat:"Text Editor,File Type",keywords:"new,page,pdf,document,文件"},{id:"file-text",cat:"Text Editor,File Type",keywords:"new,page,pdf,document,文件"},{id:"sort-alpha-asc",cat:"Web Application",keywords:"排序,排列"},{id:"sort-alpha-desc",cat:"Web Application",keywords:"排序,排列"},{id:"sort-amount-asc",cat:"Web Application",keywords:"排序,排列"},{id:"sort-amount-desc",cat:"Web Application",keywords:"排序,排列"},{id:"sort-numeric-asc",cat:"Web Application",keywords:"numbers,排序,排列"},{id:"sort-numeric-desc",cat:"Web Application",keywords:"numbers,排序,排列"},{id:"thumbs-up",cat:"Web Application,Hand",keywords:"like,favorite,approve,agree,hand,大拇指,赞,认同"},{id:"thumbs-down",cat:"Web Application,Hand",keywords:"dislike,disapprove,disagree,hand,大拇指,鄙视,踩"},{id:"youtube-square",cat:"Brand",keywords:"video,film,视频,网站"},{id:"youtube",cat:"Brand",keywords:"video,film,视频,网站"},{id:"xing",cat:"Brand",keywords:""},{id:"xing-square",cat:"Brand",keywords:""},{id:"youtube-play",cat:"Brand,Video Player",keywords:"start,playing,视频,网站,播放"},{id:"dropbox",cat:"Brand",keywords:""},{id:"stack-overflow",cat:"Brand",keywords:""},{id:"instagram",cat:"Brand",keywords:""},{id:"flickr",cat:"Brand",keywords:""},{id:"adn",cat:"Brand",keywords:""},{id:"bitbucket",cat:"Brand",keywords:"git"},{id:"bitbucket-square",cat:"Brand",keywords:"git"},{id:"tumblr",cat:"Brand",keywords:""},{id:"tumblr-square",cat:"Brand",keywords:""},{id:"long-arrow-down",cat:"Directional",keywords:"下,方向,箭头"},{id:"long-arrow-up",cat:"Directional",keywords:"上,方向,箭头"},{id:"long-arrow-left",cat:"Directional",keywords:"previous,back,左,方向,箭头"},{id:"long-arrow-right",cat:"Directional",keywords:"右,方向,箭头"},{id:"apple",cat:"Brand",keywords:"osx,food,苹果"},{id:"windows",cat:"Brand",keywords:"microsoft,微软,电脑,系统"},{id:"android",cat:"Brand",keywords:"robot,安卓"},{id:"linux",cat:"Brand",keywords:"tux"},{id:"dribbble",cat:"Brand",keywords:""},{id:"skype",cat:"Brand",keywords:""},{id:"foursquare",cat:"Brand",keywords:""},{id:"trello",cat:"Brand",keywords:""},{id:"female",cat:"Web Application",keywords:"woman,user,person,profile,女性,女子,性别,人"},{id:"male",cat:"Web Application",keywords:"man,user,person,profile,男性,男子,性别,人"},{id:"gratipay",cat:"Brand",keywords:"heart,like,favorite,love,喜欢,收藏",aliases:"gittip"},{id:"sun-o",cat:"Web Application",keywords:"weather,contrast,lighter,brighten,day,太阳,天气"},{id:"moon-o",cat:"Web Application",keywords:"night,darker,contrast,月亮,天气"},{id:"archive",cat:"Web Application",keywords:"box,storage,归档,盒子"},{id:"bug",cat:"Web Application",keywords:"report,insect,虫子,动物,昆虫,自然"},{id:"vk",cat:"Brand",keywords:""},{id:"weibo",cat:"Brand",keywords:"微博,新浪"},{id:"renren",cat:"Brand",keywords:"人人"},{id:"pagelines",cat:"Brand",keywords:"leaf,leaves,tree,plant,eco,nature,叶子,自然,植物"},{id:"stack-exchange",cat:"Brand",keywords:""},{id:"arrow-circle-o-right",cat:"Directional",keywords:"next,forward,右,箭头"},{id:"arrow-circle-o-left",cat:"Directional",keywords:"previous,back,左,箭头"},{id:"caret-square-o-left",cat:"Web Application,Directional",keywords:"previous,back,三角,caret-square-o-left",aliases:"toggle-left"},{id:"dot-circle-o",cat:"Web Application,Form Control",keywords:"target,bullseye,notification,圆圈,圆心"},{id:"wheelchair",cat:"Web Application,Medical,Transportation,Accessibility",keywords:"handicap,person,轮椅,残障,残疾人"},{id:"vimeo-square",cat:"Brand",keywords:""},{id:"try",cat:"Currency",keywords:"turkish-lira",aliases:"turkish-lira"},{id:"plus-square-o",cat:"Web Application,Form Control",keywords:"add,new,create,expand,加,加号"},{id:"space-shuttle",cat:"Web Application,Transportation",keywords:"火箭"},{id:"slack",cat:"Brand",keywords:"hashtag,anchor,hash"},{id:"envelope-square",cat:"Web Application",keywords:"信封,邮件"},{id:"wordpress",cat:"Brand",keywords:""},{id:"openid",cat:"Brand",keywords:""},{id:"university",cat:"Web Application",keywords:"bank,大学,银行",aliases:"institution,bank"},{id:"graduation-cap",cat:"Web Application",keywords:"learning,school,student,学生,硕士,学位",aliases:"mortar-board"},{id:"yahoo",cat:"Brand",keywords:"雅虎"},{id:"google",cat:"Brand",keywords:"谷歌"},{id:"reddit",cat:"Brand",keywords:""},{id:"reddit-square",cat:"Brand",keywords:""},{id:"stumbleupon-circle",cat:"Brand",keywords:""},{id:"stumbleupon",cat:"Brand",keywords:""},{id:"delicious",cat:"Brand",keywords:""},{id:"digg",cat:"Brand",keywords:""},{id:"pied-piper-pp",cat:"Brand",keywords:""},{id:"pied-piper-alt",cat:"Brand",keywords:""},{id:"drupal",cat:"Brand",keywords:""},{id:"joomla",cat:"Brand",keywords:""},{id:"language",cat:"Web Application",keywords:"语言,翻译"},{id:"fax",cat:"Web Application",keywords:"传真,电话"},{id:"building",cat:"Web Application",keywords:"work,business,apartment,office,company,建筑,楼,大厦"},{id:"child",cat:"Web Application",keywords:"小孩,孩子,人"},{id:"paw",cat:"Web Application",keywords:"pet,手掌,熊掌"},{id:"spoon",cat:"Web Application",keywords:"勺子,餐厅"},{id:"cube",cat:"Web Application",keywords:"立方体,方块"},{id:"cubes",cat:"Web Application",keywords:"立方体,方块"},{id:"behance",cat:"Brand",keywords:""},{id:"behance-square",cat:"Brand",keywords:""},{id:"steam",cat:"Brand",keywords:""},{id:"steam-square",cat:"Brand",keywords:""},{id:"recycle",cat:"Web Application",keywords:"回收,循环"},{id:"car",cat:"Web Application,Transportation",keywords:"vehicle,automobile,汽车,交通",aliases:"automobile"},{id:"taxi",cat:"Web Application,Transportation",keywords:"vehicle,cab,出租,汽车,交通",aliases:"cab"},{id:"tree",cat:"Web Application",keywords:"树"},{id:"spotify",cat:"Brand",keywords:""},{id:"deviantart",cat:"Brand",keywords:""},{id:"soundcloud",cat:"Brand",keywords:""},{id:"database",cat:"Web Application",keywords:"数据库"},{id:"file-pdf-o",cat:"Web Application,File Type",keywords:"文件"},{id:"file-word-o",cat:"Web Application,File Type",keywords:"文件"},{id:"file-excel-o",cat:"Web Application,File Type",keywords:"文件"},{id:"file-powerpoint-o",cat:"Web Application,File Type",keywords:"文件"},{id:"file-image-o",cat:"Web Application,File Type",keywords:"图片",aliases:"file-photo-o,file-picture-o"},{id:"file-archive-o",cat:"Web Application,File Type",keywords:"压缩文件",aliases:"file-zip-o"},{id:"file-audio-o",cat:"Web Application,File Type",keywords:"音频文件",aliases:"file-sound-o"},{id:"file-video-o",cat:"Web Application,File Type",keywords:"视频文件",aliases:"file-movie-o"},{id:"file-code-o",cat:"Web Application,File Type",keywords:"代码文件"},{id:"vine",cat:"Brand",keywords:""},{id:"codepen",cat:"Brand",keywords:""},{id:"jsfiddle",cat:"Brand",keywords:""},{id:"life-ring",cat:"Web Application",keywords:"life-saver,support,救生圈,支持",aliases:"life-bouy,life-buoy,life-saver,support"},{id:"circle-o-notch",cat:"Web Application,Spinner",keywords:"圆圈"},{id:"rebel",cat:"Brand",keywords:"rebel",aliases:"ra,resistance"},{id:"empire",cat:"Brand",keywords:"",aliases:"ge"},{id:"git-square",cat:"Brand",keywords:""},{id:"git",cat:"Brand",keywords:""},{id:"hacker-news",cat:"Brand",keywords:"yc,hacker-news",aliases:"y-combinator-square,yc-square"},{id:"tencent-weibo",cat:"Brand",keywords:"腾讯微博"},{id:"qq",cat:"Brand",keywords:"腾讯,企鹅"},{id:"weixin",cat:"Brand",keywords:"wechat,微信",aliases:"wechat"},{id:"paper-plane",cat:"Web Application",keywords:"paper-plane,发送,纸飞机",aliases:"send"},{id:"paper-plane-o",cat:"Web Application",keywords:"paper-plane-o,发送,纸飞机",aliases:"send-o"},{id:"history",cat:"Web Application",keywords:"历史,旋转,时间,记录"},{id:"circle-thin",cat:"Web Application",keywords:"圆圈"},{id:"header",cat:"Text Editor",keywords:"heading,标题,文本,编辑"},{id:"paragraph",cat:"Text Editor",keywords:"段落,文本,编辑"},{id:"sliders",cat:"Web Application",keywords:"settings,设置,调节"},{id:"share-alt",cat:"Web Application,Brand",keywords:"分享"},{id:"share-alt-square",cat:"Web Application,Brand",keywords:"分享"},{id:"bomb",cat:"Web Application",keywords:"炸弹"},{id:"futbol-o",cat:"Web Application",keywords:"足球,运动,football",aliases:"soccer-ball-o"},{id:"tty",cat:"Web Application,Accessibility",keywords:"电话"},{id:"binoculars",cat:"Web Application",keywords:"望远镜"},{id:"plug",cat:"Web Application",keywords:"power,connect,插头"},{id:"slideshare",cat:"Brand",keywords:""},{id:"twitch",cat:"Brand",keywords:""},{id:"yelp",cat:"Brand",keywords:""},{id:"newspaper-o",cat:"Web Application",keywords:"press,报纸"},{id:"wifi",cat:"Web Application",keywords:"无线,wlan"},{id:"calculator",cat:"Web Application",keywords:"计算器"},{id:"paypal",cat:"Brand,Payment",keywords:""},{id:"google-wallet",cat:"Brand,Payment",keywords:""},{id:"cc-visa",cat:"Brand,Payment",keywords:""},{id:"cc-mastercard",cat:"Brand,Payment",keywords:""},{id:"cc-discover",cat:"Brand,Payment",keywords:""},{id:"cc-amex",cat:"Brand,Payment",keywords:"amex"},{id:"cc-paypal",cat:"Brand,Payment",keywords:""},{id:"cc-stripe",cat:"Brand,Payment",keywords:""},{id:"bell-slash",cat:"Web Application",keywords:"铃铛,静音"},{id:"bell-slash-o",cat:"Web Application",keywords:"铃铛,静音"},{id:"trash",cat:"Web Application",keywords:"garbage,delete,remove,hide,垃圾桶,回收站,删除"},{id:"copyright",cat:"Web Application",keywords:"版权"},{id:"at",cat:"Web Application",keywords:"艾特"},{id:"eyedropper",cat:"Web Application",keywords:"拾色器,取色器,吸管"},{id:"paint-brush",cat:"Web Application",keywords:"画笔,笔"},{id:"birthday-cake",cat:"Web Application",keywords:"生日蛋糕"},{id:"area-chart",cat:"Web Application,Chart",keywords:"graph,analytics,图表,折线图"},{id:"pie-chart",cat:"Web Application,Chart",keywords:"graph,analytics,饼状图,图表"},{id:"line-chart",cat:"Web Application,Chart",keywords:"graph,analytics,线条图,图表"},{id:"lastfm",cat:"Brand",keywords:""},{id:"lastfm-square",cat:"Brand",keywords:""},{id:"toggle-off",cat:"Web Application",keywords:"开关"},{id:"toggle-on",cat:"Web Application",keywords:"开关"},{id:"bicycle",cat:"Web Application,Transportation",keywords:"vehicle,bike,自行车,交通"},{id:"bus",cat:"Web Application,Transportation",keywords:"vehicle,公共汽车,公车,公交车,交通"},{id:"ioxhost",cat:"Brand",keywords:""},{id:"angellist",cat:"Brand",keywords:"胜利,剪刀手,手指,手势"},{id:"cc",cat:"Web Application,Accessibility",keywords:"抄送"},{id:"ils",cat:"Currency",keywords:"shekel,sheqel",aliases:"shekel,sheqel"},{id:"meanpath",cat:"Brand",keywords:""},{id:"buysellads",cat:"Brand",keywords:""},{id:"connectdevelop",cat:"Brand",keywords:""},{id:"dashcube",cat:"Brand",keywords:""},{id:"forumbee",cat:"Brand",keywords:""},{id:"leanpub",cat:"Brand",keywords:""},{id:"sellsy",cat:"Brand",keywords:""},{id:"shirtsinbulk",cat:"Brand",keywords:""},{id:"simplybuilt",cat:"Brand",keywords:""},{id:"skyatlas",cat:"Brand",keywords:""},{id:"cart-plus",cat:"Web Application",keywords:"add,shopping,购物车,购物"},{id:"cart-arrow-down",cat:"Web Application",keywords:"shopping,购物车,购物"},{id:"diamond",cat:"Web Application",keywords:"gem,gemstone,钻石,宝石,珠宝"},{id:"ship",cat:"Web Application,Transportation",keywords:"boat,sea,轮船,交通,水运,海洋,航海"},{id:"user-secret",cat:"Web Application",keywords:"whisper,spy,incognito,privacy,用户,人,侦探"},{id:"motorcycle",cat:"Web Application,Transportation",keywords:"vehicle,bike,motobike,摩托车,机车,骑行,交通"},{id:"street-view",cat:"Web Application",keywords:"map,街景,地图,定位,位置"},{id:"heartbeat",cat:"Web Application,Medical",keywords:"ekg,心跳,医疗"},{id:"venus",cat:"Gender",keywords:"female,女性,女子,性别"},{id:"mars",cat:"Gender",keywords:"male,男性,男子,性别"},{id:"mercury",cat:"Gender",keywords:"transgender,性别"},{id:"transgender",cat:"Gender",keywords:"性别",aliases:"intersex"},{id:"transgender-alt",cat:"Gender",keywords:"性别"},{id:"venus-double",cat:"Gender",keywords:"性别"},{id:"mars-double",cat:"Gender",keywords:"男性,男子,性别"},{id:"venus-mars",cat:"Gender",keywords:"两性,性别"},{id:"mars-stroke",cat:"Gender",keywords:"男性,男子,性别"},{id:"mars-stroke-v",cat:"Gender",keywords:"男性,男子,性别"},{id:"mars-stroke-h",cat:"Gender",keywords:"男性,男子,性别"},{id:"neuter",cat:"Gender",keywords:"中性,性别"},{id:"genderless",cat:"Gender",keywords:""},{id:"facebook-official",cat:"Brand",keywords:"脸书"},{id:"pinterest-p",cat:"Brand",keywords:""},{id:"whatsapp",cat:"Brand",keywords:""},{id:"server",cat:"Web Application",keywords:"服务器,pc,运维,服务"},{id:"user-plus",cat:"Web Application",keywords:"sign up,signup,用户,人"},{id:"user-times",cat:"Web Application",keywords:"用户,人"},{id:"bed",cat:"Web Application",keywords:"travel,hotel,床,旅馆,宾馆,床位,住宿,睡眠",aliases:"hotel"},{id:"viacoin",cat:"Brand",keywords:""},{id:"train",cat:"Transportation",keywords:"火车,交通"},{id:"subway",cat:"Transportation",keywords:"地铁,轻轨,捷运,交通"},{id:"medium",cat:"Brand",keywords:""},{id:"y-combinator",cat:"Brand",keywords:"",aliases:"yc"},{id:"optin-monster",cat:"Brand",keywords:""},{id:"opencart",cat:"Brand",keywords:""},{id:"expeditedssl",cat:"Brand",keywords:""},{id:"battery-full",cat:"Web Application",keywords:"power,battery-4,电池,电量",aliases:"battery-4,battery"},{id:"battery-three-quarters",cat:"Web Application",keywords:"power,battery-3,电池,电量",aliases:"battery-3"},{id:"battery-half",cat:"Web Application",keywords:"power,battery-2,电池,电量",aliases:"battery-2"},{id:"battery-quarter",cat:"Web Application",keywords:"power,battery-1,电池,电量",aliases:"battery-1"},{id:"battery-empty",cat:"Web Application",keywords:"power,battery-0,电池,电量",aliases:"battery-0"},{id:"mouse-pointer",cat:"Web Application",keywords:"cursor,鼠标,指针"},{id:"i-cursor",cat:"Web Application",keywords:"光标,鼠标,指针"},{id:"object-group",cat:"Web Application",keywords:"组合,组件,元素,工具,设置"},{id:"object-ungroup",cat:"Web Application",keywords:"组合,打散,组件,元素,工具,设置"},{id:"sticky-note",cat:"Web Application",keywords:"便笺,便签"},{id:"sticky-note-o",cat:"Web Application",keywords:"便笺,便签"},{id:"cc-jcb",cat:"Brand,Payment",keywords:"银行"},{id:"cc-diners-club",cat:"Brand,Payment",keywords:"银行"},{id:"clone",cat:"Web Application",keywords:"copy,克隆,复制"},{id:"balance-scale",cat:"Web Application",keywords:"天平,法律,公平"},{id:"hourglass-o",cat:"Web Application",keywords:"沙漏"},{id:"hourglass-start",cat:"Web Application",keywords:"沙漏,hourglass-start,沙漏",aliases:"hourglass-1"},{id:"hourglass-half",cat:"Web Application",keywords:"hourglass-half,沙漏",aliases:"hourglass-2"},{id:"hourglass-end",cat:"Web Application",keywords:"hourglass-end,沙漏",aliases:"hourglass-3"},{id:"hourglass",cat:"Web Application",keywords:"拳头,手势"},{id:"hand-rock-o",cat:"Web Application,Hand",keywords:"hand-rock,手",aliases:"hand-grab-o"},{id:"hand-paper-o",cat:"Web Application,Hand",keywords:"stop,hand-stop-o,手",aliases:"hand-stop-o"},{id:"hand-scissors-o",cat:"Web Application,Hand",keywords:"手"},{id:"hand-lizard-o",cat:"Web Application,Hand",keywords:"手"},{id:"hand-spock-o",cat:"Web Application,Hand",keywords:"手"},{id:"hand-pointer-o",cat:"Web Application,Hand",keywords:"手"},{id:"hand-peace-o",cat:"Web Application,Hand",keywords:"手"},{id:"trademark",cat:"Web Application",keywords:"tm,商标"},{id:"registered",cat:"Web Application",keywords:"注册,商标"},{id:"creative-commons",cat:"Web Application",keywords:"版权"},{id:"gg",cat:"Currency,Brand",keywords:""},{id:"gg-circle",cat:"Currency,Brand",keywords:""},{id:"tripadvisor",cat:"Brand",keywords:""},{id:"odnoklassniki",cat:"Brand",keywords:""},{id:"odnoklassniki-square",cat:"Brand",keywords:""},{id:"get-pocket",cat:"Brand",keywords:""},{id:"wikipedia-w",cat:"Brand",keywords:"维基"},{id:"safari",cat:"Brand",keywords:"browser,浏览器"},{id:"chrome",cat:"Brand",keywords:"browser,浏览器"},{id:"firefox",cat:"Brand",keywords:"browser,浏览器"},{id:"opera",cat:"Brand",keywords:"欧朋,浏览器"},{id:"internet-explorer",cat:"Brand",keywords:"browser,ie,浏览器"},{id:"television",cat:"Web Application",keywords:"display,computer,monitor,tv,电视,设备,显示器",aliases:"tv"},{id:"contao",cat:"Brand",keywords:""},{id:"500px",cat:"Brand",keywords:""},{id:"amazon",cat:"Brand",keywords:""},{id:"calendar-plus-o",cat:"Web Application",keywords:"日历"},{id:"calendar-minus-o",cat:"Web Application",keywords:"日历"},{id:"calendar-times-o",cat:"Web Application",keywords:"日历"},{id:"calendar-check-o",cat:"Web Application",keywords:"ok,日历"},{id:"industry",cat:"Web Application",keywords:"factory,工业,工厂"},{id:"map-pin",cat:"Web Application",keywords:"地图,地标,位置"},{id:"map-signs",cat:"Web Application",keywords:"地图,地标,位置"},{id:"map-o",cat:"Web Application",keywords:"地图,地标,位置"},{id:"map",cat:"Web Application",keywords:"地图,地标,位置"},{id:"commenting",cat:"Web Application",keywords:"speech,notification,note,chat,bubble,feedback,message,texting,sms,conversation,评论,留言"},{id:"commenting-o",cat:"Web Application",keywords:"speech,notification,note,chat,bubble,feedback,message,texting,sms,conversation,评论,留言"},{id:"houzz",cat:"Brand",keywords:""},{id:"vimeo",cat:"Brand",keywords:""},{id:"black-tie",cat:"Brand",keywords:"领结,领带"},{id:"fonticons",cat:"Brand",keywords:""},{id:"reddit-alien",cat:"Brand",keywords:""},{id:"edge",cat:"Brand",keywords:"browser,ie,浏览器"},{id:"credit-card-alt",cat:"Payment,Web Application",keywords:"money,buy,debit,checkout,purchase,payment,credit card,信用卡,银行卡,卡片,支付,购物,购买"},{id:"codiepie",cat:"Brand",keywords:""},{id:"modx",cat:"Brand",keywords:""},{id:"fort-awesome",cat:"Brand",keywords:""},{id:"usb",cat:"Brand",keywords:""},{id:"product-hunt",cat:"Brand",keywords:""},{id:"mixcloud",cat:"Brand",keywords:""},{id:"scribd",cat:"Brand",keywords:""},{id:"pause-circle",cat:"Video Player",keywords:"暂停"},{id:"pause-circle-o",cat:"Video Player",keywords:"暂停"},{id:"stop-circle",cat:"Video Player",keywords:"停止"},{id:"stop-circle-o",cat:"Video Player",keywords:"停止"},{id:"shopping-bag",cat:"Web Application",keywords:"购物袋,购物,购买,商店,包"},{id:"shopping-basket",cat:"Web Application",keywords:"购物篮,购物,购买,商店,包"},{id:"hashtag",cat:"Web Application",keywords:"话题,#,井号"},{id:"bluetooth",cat:"Web Application,Brand",keywords:"蓝牙"},{id:"bluetooth-b",cat:"Web Application,Brand",keywords:"蓝牙"},{id:"percent",cat:"Web Application",keywords:"百分号,%"},{id:"gitlab",cat:"Brand",keywords:""},{id:"wpbeginner",cat:"Brand",keywords:""},{id:"wpforms",cat:"Brand",keywords:""},{id:"envira",cat:"Brand",keywords:"leaf"},{id:"universal-access",cat:"Web Application,Accessibility",keywords:"人"},{id:"wheelchair-alt",cat:"Web Application,Medical,Transportation,Accessibility",keywords:"handicap,person,残障,残疾,轮椅"},{id:"question-circle-o",cat:"Web Application,Accessibility",keywords:"问,帮助"},{id:"blind",cat:"Web Application,Accessibility",keywords:"盲人,出行,残疾,残障,人"},{id:"audio-description",cat:"Web Application,Accessibility",keywords:""},{id:"volume-control-phone",cat:"Web Application,Accessibility",keywords:"telephone,mobile,电话,手机,拨号"},{id:"braille",cat:"Web Application,Accessibility",keywords:"盲人,残疾,残障,盲文"},{id:"assistive-listening-systems",cat:"Web Application,Accessibility",keywords:"聋,助听,残疾,残障,耳朵"},{id:"american-sign-language-interpreting",cat:"Web Application,Accessibility",keywords:"手语,聋,残疾,残障,手势",aliases:"asl-interpreting"},{id:"deaf",cat:"Web Application,Accessibility",keywords:"聋,助听,残疾,残障,耳朵",aliases:"deafness,hard-of-hearing"},{id:"glide",cat:"Brand",keywords:""},{id:"glide-g",cat:"Brand",keywords:""},{id:"sign-language",cat:"Web Application,Accessibility",keywords:"手语,聋,残疾,残障,手势",aliases:"signing"},{id:"low-vision",cat:"Web Application,Accessibility",keywords:"盲人,残疾,残障,眼睛"},{id:"viadeo",cat:"Brand",keywords:""},{id:"viadeo-square",cat:"Brand",keywords:""},{id:"snapchat",cat:"Brand",keywords:""},{id:"snapchat-ghost",cat:"Brand",keywords:""},{id:"snapchat-square",cat:"Brand",keywords:""},{id:"pied-piper",cat:"Brand",keywords:"树叶,植物"},{id:"first-order",cat:"Brand",keywords:""},{id:"yoast",cat:"Brand",keywords:""},{id:"themeisle",cat:"Brand",keywords:""},{id:"google-plus-official",cat:"Brand",keywords:"谷歌",aliases:"google-plus-circle"},{id:"font-awesome",cat:"Brand",keywords:"",aliases:"fa"},{id:"handshake-o",cat:"Web Application",keywords:"握手,手势"},{id:"envelope-open",cat:"Web Application",keywords:"信封,邮件"},{id:"envelope-open-o",cat:"Web Application",keywords:"信封,邮件"},{id:"linode",cat:"Brand",keywords:""},{id:"address-book",cat:"Web Application",keywords:"地址,地址簿,通讯录"},{id:"address-book-o",cat:"Web Application",keywords:"地址,地址簿,通讯录"},{id:"address-card",cat:"Web Application",keywords:"身份证,证件,卡片",aliases:"vcard"},{id:"address-card-o",cat:"Web Application",keywords:"身份证,证件,卡片",aliases:"vcard-o"},{id:"user-circle",cat:"Web Application",keywords:"人,头像"},{id:"user-circle-o",cat:"Web Application",keywords:"人,头像"},{id:"user-o",cat:"Web Application",keywords:"人,头像"},{id:"id-badge",cat:"Web Application",keywords:"身份证,证件,卡片"},{id:"id-card",cat:"Web Application",keywords:"身份证,证件,卡片",aliases:"drivers-license"},{id:"id-card-o",cat:"Web Application",keywords:"身份证,证件,卡片",aliases:"drivers-license-o"},{id:"quora",cat:"Brand",keywords:""},{id:"free-code-camp",cat:"Brand",keywords:"火"},{id:"telegram",cat:"Brand",keywords:"飞机"},{id:"thermometer-full",cat:"Web Application",keywords:"温度计,天气,气候",aliases:"thermometer-4,thermometer"},{id:"thermometer-three-quarters",cat:"Web Application",keywords:"温度计,天气,气候",aliases:"thermometer-3"},{id:"thermometer-half",cat:"Web Application",keywords:"温度计,天气,气候",aliases:"thermometer-2"},{id:"thermometer-quarter",cat:"Web Application",keywords:"温度计,天气,气候",aliases:"thermometer-1"},{id:"thermometer-empty",cat:"Web Application",keywords:"温度计,天气,气候",aliases:"thermometer-0"},{id:"shower",cat:"Web Application",keywords:"洗澡,淋雨,洗浴,清洁,酒店,宾馆"},{id:"bath",cat:"Web Application",keywords:"洗澡,淋雨,浴缸,洗浴,清洁,酒店,宾馆",aliases:"bathtub,s15"},{id:"podcast",cat:"Web Application",keywords:"广播"},{id:"window-maximize",cat:"Web Application",keywords:"全屏,按钮"},{id:"window-minimize",cat:"Web Application",keywords:"最小化,按钮"},{id:"window-restore",cat:"Web Application",keywords:"屏幕,按钮"},{id:"window-close",cat:"Web Application",keywords:"关闭,窗口,屏幕",aliases:"times-rectangle"},{id:"window-close-o",cat:"Web Application",keywords:"关闭,窗口,屏幕",aliases:"times-rectangle-o"},{id:"bandcamp",cat:"Brand",keywords:""},{id:"grav",cat:"Brand",keywords:""},{id:"etsy",cat:"Brand",keywords:""},{id:"imdb",cat:"Brand",keywords:""},{id:"ravelry",cat:"Brand",keywords:""},{id:"eercast",cat:"Brand",keywords:""},{id:"microchip",cat:"Web Application",keywords:"内存条,存储,电脑"},{id:"snowflake-o",cat:"Web Application",keywords:"冰,冷,雪"},{id:"superpowers",cat:"Brand",keywords:""},{id:"wpexplorer",cat:"Brand",keywords:""},{id:"meetup",cat:"Brand",keywords:""}],
	menu : {All: '全部',"Web Application": '网页',Medical: '医疗',"Text Editor": '文字编辑',Spinner: '旋转',"File Type": '文件类型',Directional: '方向',"Video Player": '播放器',"Form Control": '表单',Transportation: '交通',Chart: '图表',Brand: 'Logo',Hand: '手势',Payment: '支付',Currency: '货币',Accessibility: '无障碍',Gender: '性别'},
	_get : function(keyword,type){
		var icondatas = this.icondatas;
		var menu = this.menu;
		var resultdata = [];
		if(type ==1 || typeof(type)=='undefined'){//关键字查找
			for(var i in icondatas){
				var icondata = icondatas[i];
				var keywords = icondata['keywords'].split(',');
				for(var k in keywords){
					if(keywords[k] == keyword){
						resultdata.push('fa fa-'+icondata.id);
					}
				}
			}
		}else if(type==2){//分类查找
			for(var i in icondatas){
				var icondata = icondatas[i];
				var cats = icondata['cat'].split(',');
				for(var k in cats){
					if(cats[k] == keyword || keyword =='All'){
						resultdata.push('fa fa-'+icondata.id);break;
					}
				}
			}
		}
		//if(keyword == "All") console.log(resultdata);
		return resultdata
	},
	search : function(obj){
		var keyword = $(obj).parents('.iconpicker').eq(0).find('input').val();
		var icons = this._get(keyword,1);
		var iconshtml = '';
		for(var j in icons){
			//console.log(icons[j]);
			iconshtml += '<i class="'+icons[j]+'" style="font-size:20px;padding:3px;margin:2px;cursor:pointer" iconp="1"></i>';
		}
		$(obj).parents('.iconpicker').eq(0).find('.iconpicker-search-data').html(iconshtml);
		layui.element.tabChange('iconpickertab', 100);
	},
	addhistory : function(obj){
		var icon = $(obj).attr('class');
		var iconpickerdata = layui.data('iconpickerdata')
		var	iconpickerhistory = iconpickerdata.history;
		var newdata = [];
		var newdatastr = '';
		if(iconpickerhistory){
			iconpickerhistory = icon + ',' + iconpickerhistory;
			var historyarr = iconpickerhistory.split(',');
			var hasthis = 0;
			for(var i in historyarr){
				if(historyarr[i] != icon || i==0){
					newdata.push(historyarr[i]);
				}
			}
			var newdata2 = [];
			for(var i in newdata){
				if(i<20){
					newdata2.push(newdata[i]);
				}
			}
			newdatastr = newdata2.join(',');
		}else{
			newdatastr = icon
		}
		layui.data('iconpickerdata', {
		  key: 'history'
		  ,value: newdatastr
		});
	},
	show : function(obj){
		var THIS = this;
		var iconpickerdata = layui.data('iconpickerdata')
		var	iconpickerhistory = iconpickerdata.history;
		var hhistoryhtml = '';
		if(iconpickerhistory){
			hhistoryhtml = '最近使用: ';
			var historyarr = iconpickerhistory.split(',');
			for(var i in historyarr){
				if(i<15)
				hhistoryhtml += '<i class="'+historyarr[i]+'" style="font-size:20px;padding:3px;margin:2px;cursor:pointer"  iconp="1"></i>';
			}
		}

		var menu = THIS.menu;
		var html = '<div class="iconpicker" id="iconpicker">';
		html+='<div style="text-align:right;margin:5px 10px;"><div style="float:left;height:38px;line-height:38px">'+hhistoryhtml+'</div><div class="layui-input-inline" style="width: 200px;"><input type="text" autocomplete="off" class="layui-input"></div>';
		html+='	<button class="layui-btn layui-btn-primary" id="iconpickersearch"><i class="layui-icon layui-icon-search"></i></button>';
		html+='</div>'
		html+='<div class="layui-tab layui-tab-card" style="margin:5px 8px" lay-filter="iconpickertab">'
		html+='<ul class="layui-tab-title">'
		for(var i in menu){
			if(i=='All'){
				html+='<li class="layui-this" style="min-width:0;padding:0 7px">'+menu[i]+'</li>'
			}else{
				html+='<li style="min-width:0;padding:0 7px">'+menu[i]+'</li>'
			}
		}
		html+='<li style="min-width:0;padding:0 7px" lay-id="100">&nbsp;</li>'
		html+='</ul>'
		html+='<div class="layui-tab-content" style="height:320px;overflow:auto">'
		for(var i in menu){
			var icons = THIS._get(i,2);
			var iconshtml = '';
			for(var j in icons){
				//console.log(icons[j]);
				iconshtml += '<i class="'+icons[j]+'" style="font-size:20px;padding:3px;margin:2px;cursor:pointer" iconp="1"></i>';
			}
			if(i=='All'){
				html+='<div class="layui-tab-item layui-show">'+iconshtml+'</div>'
			}else{
				html+='<div class="layui-tab-item">'+iconshtml+'</div>'
			}
		}
		html+='<div class="layui-tab-item iconpicker-search-data"></div>'
		html+='</div>'
		html+='</div>';
		html+='</div>';
		var iconpickerdialog = layer.open({
		  type: 1, 
		  title:'选择图标',
		  shadeClose:true,
		  content: html,
		  area: ['810px', '500px']
		});
		$('#iconpickersearch').bind('click',function(){
			var keyword = $(this).parents('.iconpicker').eq(0).find('input').val();
			var icons = THIS._get(keyword,1);
			var iconshtml = '';
			for(var j in icons){
				//console.log(icons[j]);
				iconshtml += '<i class="'+icons[j]+'" style="font-size:20px;padding:3px;margin:2px;cursor:pointer" iconp="1"></i>';
			}
			$(this).parents('.iconpicker').eq(0).find('.iconpicker-search-data').html(iconshtml);
			layui.element.tabChange('iconpickertab', 100);
			THIS.bindiconclick(obj,iconpickerdialog)
		})
		THIS.bindiconclick(obj,iconpickerdialog)
	},
	bindiconclick : function(obj,iconpickerdialog){
		$('i[iconp=1]').bind('click',function(){
			iconpicker.addhistory(this);
			var icon = $(this).attr('class');
			//$(obj).parent().find('i').attr('class',icon);
			//$(obj).parent().find('input').val(icon).trigger('change');
			typeof obj === "function" && obj(icon);
			layer.close(iconpickerdialog)
		})
	}
}
