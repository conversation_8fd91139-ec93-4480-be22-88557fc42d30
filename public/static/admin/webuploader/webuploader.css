.webuploader-container {
	position: relative;
}
.webuploader-element-invisible {
	position: absolute !important;
	clip: rect(1px 1px 1px 1px); /* IE6, IE7 */
    clip: rect(1px,1px,1px,1px);
}
.webuploader-pick {
	position: relative;
	display: inline-block;
	cursor: pointer;
	/*
	background: #00b7ee;
	*/
	padding: 1px 5px;
	color: white;
	text-align: center;
	border-radius: 3px;
	overflow: hidden;
}
.webuploader-pick-hover {
	/*background: #00a2d4;*/
	color: white;
}

.webuploader-pick-disable {
	opacity: 0.6;
	pointer-events:none;
}


/* ----------------Reset Css--------------------- */
/* html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li,
fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, figcaption, figure, footer, header, hgroup, menu, nav, section, summary,
time, mark, audio, video, input  {
	margin: 0;
	padding: 0;
	border: none;
	outline: 0;
	font-size: 100%;
	font: inherit;
	vertical-align: baseline;
}

html, body, form, fieldset, p, div, h1, h2, h3, h4, h5, h6 {
	-webkit-text-size-adjust: none;
}

article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
	display: block;
}

body {
	font-family: arial, sans-serif;
}

ol, ul {
	list-style: none;
}

blockquote, q {
	quotes: none;
}

blockquote:before, blockquote:after, q:before, q:after {
	content: '';
	content: none;
}

ins {
	text-decoration: none;
}

del {
	text-decoration: line-through;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}
*/

/* ------------ */
/* #wrapper {
	width: 980px;
	margin: 0 auto;

	margin: 1em;
	width: auto;
}

#container {
	border: 1px solid #dadada;
	color: #838383;
	font-size: 12px;
	margin-top: 10px;
	background-color: #FFF;
}
*/
.uploader .queueList {
	margin: 10px;min-height:356px
}

.element-invisible {
	position: absolute !important;
	clip: rect(1px 1px 1px 1px); /* IE6, IE7 */
	clip: rect(1px,1px,1px,1px);
}

.uploader .placeholder {
	border: 3px dashed #e6e6e6;
	min-height: 200px;
	padding-top: 150px;
	text-align: center;
	background: url(./image.png) center 80px no-repeat;
	color: #cccccc;
	font-size: 18px;
	position: relative;
}

.uploader .placeholder .webuploader-pick {
	font-size: 18px;
	background: #00b7ee;
	border-radius: 3px;
	line-height: 44px;
	padding: 0 30px;
	color: #fff;
	display: inline-block;
	margin: 35px auto;
	cursor: pointer;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.uploader .placeholder .webuploader-pick-hover {
	background: #00a2d4;
}

.uploader .placeholder .flashTip {
	color: #666666;
	font-size: 12px;
	position: absolute;
	width: 100%;
	text-align: center;
	bottom: 20px;
}
.uploader .placeholder .flashTip a {
	color: #0785d1;
	text-decoration: none;
}
.uploader .placeholder .flashTip a:hover {
	text-decoration: underline;
}

.uploader .placeholder.webuploader-dnd-over {
	border-color: #999999;
}

.uploader .placeholder.webuploader-dnd-over.webuploader-dnd-denied {
	border-color: red;
}

.uploader .filelist {
	list-style: none;
	margin: 0;
	padding: 0;
	max-height: 350px;
	overflow-y : auto;
}

.uploader .filelist:after {
	content: '';
	display: block;
	width: 0;
	height: 0;
	overflow: hidden;
	clear: both;
}

.uploader .filelist li {
	width: 98px;
	height: 98px;
	background: url(./bg.png) no-repeat;
	text-align: center;
	margin: 0 8px 20px 0;
	position: relative;
	display: inline;
	float: left;
	overflow: hidden;
	font-size: 12px;
}

.uploader .filelist li p.log {
	position: relative;
	top: -45px;
}

.uploader .filelist li p.title {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow : ellipsis;
	top: 5px;
	text-indent: 5px;
	text-align: left;
}

.uploader .filelist li p.progress {
	position: absolute;
	width: 100%;
	bottom: 0;
	left: 0;
	height: 8px;
	overflow: hidden;
	z-index: 50;
}
.uploader .filelist li p.progress span {
	display: none;
	overflow: hidden;
	width: 0;
	height: 100%;
	background: #1483d8 url(./progress.png) repeat-x;

	-webit-transition: width 200ms linear;
	-moz-transition: width 200ms linear;
	-o-transition: width 200ms linear;
	-ms-transition: width 200ms linear;
	transition: width 200ms linear;

	-webkit-animation: progressmove 2s linear infinite;
	-moz-animation: progressmove 2s linear infinite;
	-o-animation: progressmove 2s linear infinite;
	-ms-animation: progressmove 2s linear infinite;
	animation: progressmove 2s linear infinite;

	-webkit-transform: translateZ(0);
}

@-webkit-keyframes progressmove {
	0% {
	   background-position: 0 0;
	}
	100% {
	   background-position: 17px 0;
	}
}
@-moz-keyframes progressmove {
	0% {
	   background-position: 0 0;
	}
	100% {
	   background-position: 17px 0;
	}
}
@keyframes progressmove {
	0% {
	   background-position: 0 0;
	}
	100% {
	   background-position: 17px 0;
	}
}

.uploader .filelist li p.imgWrap {
	position: relative;
	z-index: 2;
	line-height: 110px;
	vertical-align: middle;
	overflow: hidden;
	width: 110px;
	height: 110px;

	-webkit-transform-origin: 50% 50%;
	-moz-transform-origin: 50% 50%;
	-o-transform-origin: 50% 50%;
	-ms-transform-origin: 50% 50%;
	transform-origin: 50% 50%;

	-webit-transition: 200ms ease-out;
	-moz-transition: 200ms ease-out;
	-o-transition: 200ms ease-out;
	-ms-transition: 200ms ease-out;
	transition: 200ms ease-out;
}

.uploader .filelist li img {
	width: 100%;
}

.uploader .filelist li p.error {
	background: #f43838;
	color: #fff;
	position: absolute;
	bottom: 0;
	left: 0;
	height: 28px;
	line-height: 28px;
	width: 100%;
	z-index: 100;
}

.uploader .filelist li .success {
	display: block;
	position: absolute;
	left: 0;
	bottom: 0;
	height: 40px;
	width: 100%;
	z-index: 200;
	background: url(./success.png) no-repeat right bottom;
}

.uploader .filelist div.file-panel {
	position: absolute;
	height: 0;
	filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0,startColorstr='#80000000', endColorstr='#80000000')\0;
	background: rgba( 0, 0, 0, 0.5 );
	width: 100%;
	top: 0;
	left: 0;
	overflow: hidden;
	z-index: 300;
}

.uploader .filelist div.file-panel span {
	width: 24px;
	height: 24px;
	display: inline;
	float: right;
	text-indent: -9999px;
	overflow: hidden;
	background: url(./icons.png) no-repeat;
	margin: 5px 1px 1px;
	cursor: pointer;
}

.uploader .filelist div.file-panel span.rotateLeft {
	background-position: 0 -24px;
}
.uploader .filelist div.file-panel span.rotateLeft:hover {
	background-position: 0 0;
}

.uploader .filelist div.file-panel span.rotateRight {
	background-position: -24px -24px;
}
.uploader .filelist div.file-panel span.rotateRight:hover {
	background-position: -24px 0;
}

.uploader .filelist div.file-panel span.cancel {
	background-position: -48px -24px;
}
.uploader .filelist div.file-panel span.cancel:hover {
	background-position: -48px 0;
}

.uploader .statusBar {
	height: 63px;
	border-top: 1px solid #dadada;
	padding: 0 20px;
	line-height: 63px;
	vertical-align: middle;
	position: relative;
}

.uploader .statusBar .progress {
	border: 1px solid #1483d8;
	width: 198px;
	background: #fff;
	height: 18px;
	position: relative;
	display: inline-block;
	text-align: center;
	line-height: 20px;
	color: #6dbfff;
	position: relative;
	margin-right: 10px;
}
.uploader .statusBar .progress span.percentage {
	width: 0;
	height: 100%;
	left: 0;
	top: 0;
	background: #1483d8;
	position: absolute;
}
.uploader .statusBar .progress span.text {
	position: relative;
	z-index: 10;
}

.uploader .statusBar .info {
	display: inline-block;
	font-size: 14px;
	color: #666666;
}

.uploader .statusBar .btns {
	position: absolute;
	top: 10px;
	right: 40px;
	line-height: 40px;
}

#filePicker2, #wechat-filePicker2 {
	display: inline-block;
	float: left;
}

.uploader .statusBar .btns .webuploader-pick,
.uploader .statusBar .btns .uploadBtn,
.uploader .statusBar .btns .uploadBtn.state-uploading,
.uploader .statusBar .btns .uploadBtn.state-paused {
	/*
	background: #ffffff;
	border: 1px solid #cfcfcf;
	padding: 0 18px;
	color: #565656;
	*/
	color: white;
	display: inline-block;
	border-radius: 3px;
	margin-left: 10px;
	cursor: pointer;
	font-size: 14px;
	float: left;
}
.uploader .statusBar .btns .webuploader-pick-hover,
.uploader .statusBar .btns .uploadBtn:hover,
.uploader .statusBar .btns .uploadBtn.state-uploading:hover,
.uploader .statusBar .btns .uploadBtn.state-paused:hover {
	/*background: #f0f0f0;*/
}

.uploader .statusBar .btns .uploadBtn {
	/*background: #00b7ee;*/
	color: #fff;
	border-color: transparent;
}
.uploader .statusBar .btns .uploadBtn:hover {
	background: #00a2d4;
}

.uploader .statusBar .btns .uploadBtn.disabled {
	pointer-events: none;
	opacity: 0.6;
}


/*上传文件*/
#modal-fileUploader .browser{color:#428bca;padding:10px 0 0 0}
#modal-fileUploader .browser option{height:30px;line-height:30px}
#modal-fileUploader .browser .browser_group{width:150px;height:505px;border-right:1px solid #e8e9eb;color:#666}
#modal-fileUploader .browser .browser_group ul{height:460px;overflow-x:hidden;overflow-y:auto;}
#modal-fileUploader .browser .browser_group li{height:40px;line-height:40px;padding:0 10px 0 15px;cursor:pointer;overflow:hidden}
#modal-fileUploader .browser .browser_group .active{background:#F1F2F6;color:#428bca}
#modal-fileUploader .browser .browser-group-op{height:40px;line-height:40px;padding:0 10px;color:#3296fa;font-size:12px}
#modal-fileUploader .browser .browser-group-op div{cursor:pointer}
#modal-fileUploader .browser .browser-top{display:flex;margin-bottom:2px}
#modal-fileUploader .browser .browser-top-item{display:flex;height:38px;line-height:38px}
#modal-fileUploader .browser .file-browser-bottomop{display:flex;height:40px;line-height:40px;padding-right:20px}
#modal-fileUploader .browser .file-browser-filelist{display:flex;flex-wrap:wrap;padding-top:10px;overflow:hidden}
#modal-fileUploader .browser .img-item {cursor: pointer; position:relative;width:98px; height:98px; }
#modal-fileUploader .browser .img-item .img-container{position: relative;width:98px; height:98px; text-align:center; background-color:#eee; background-size: contain; background-repeat: no-repeat; background-position: 50% 50%;}
#modal-fileUploader .browser .img-item .img-container:hover .img-meta{display:none;}
#modal-fileUploader .browser .img-item .img-container .img-meta { width:100%; height:20px; position: absolute; bottom: 0; z-index:2; background:rgba(0,0,0,0.5); color:#eee; overflow:hidden;}
#modal-fileUploader .browser .img-item .img-container .select-status {display:inline-block; width:50px; height:50px; position: absolute; bottom:0; right:0; z-index:2;}
#modal-fileUploader .browser .img-item-selected .img-container .select-status {display:inline-block; width:50px; height:50px; position: absolute; bottom:0; right:0; background: url(images/success-small.png) no-repeat right bottom; z-index:2;}
#modal-fileUploader .browser .img-item-selected .img-container .img-meta {display:none;}
#modal-fileUploader .browser .file-browser .img-item{margin:0 5px 5px 0;}
#modal-fileUploader .browser .file-browser{width:740px;height:500px; overflow-y:auto; padding-top:0;padding-left:10px}
#modal-fileUploader .browser span{display:block;height:20px;overflow:hidden; text-align:center;}
#modal-fileUploader .browser img{width:98px;}
#modal-fileUploader .browser i{display:block;font-size:40px;padding:5px 13px 0 13px;}
#modal-fileUploader .btnClose{text-align:right; position:absolute; top:-10px; right:-5px; display:none; z-index:10;}
#modal-fileUploader .img-item:hover .btnClose{display:block;}
#modal-fileUploader .btnClose a{display:inline-block; width:20px; height:20px; text-align:center; line-height:20px;color:#fff; background:rgba(0,0,0,.3); border-radius:50%;}
#modal-fileUploader .btnClose a i{font-size:14px; padding:3px;}
#modal-fileUploader .bthClose a:hover{background:rgba(0,0,0,1);}
#modal-fileUploader .crawler .crawler-img{width:250px; height:150px; position:relative; text-align:center; overflow:hidden; margin:10px 0; background-color:#efefef; background-size: contain; background-repeat: no-repeat; background-position: 50% 50%;}
#modal-fileUploader .crawler .crawler-img span{position:absolute; left:0; bottom:0; display:inline-block; width:100%; height:30px; line-height:30px; background:rgba(0,0,0,0.2); color:#eee;}
#modal-fileUploader .form-group{margin-left:0;; margin-right:0;}
#modal-fileUploader .row{margin-top: 5px; height: 250px; }

.breadcrumb{padding:8px 15px;margin-bottom:20px;list-style:none;background-color:#f5f5f5;border-radius:4px}.breadcrumb>li{display:inline-block}.breadcrumb>li+li:before{padding:0 5px;color:#ccc;content:"/\00a0"}.breadcrumb>.active{color:#777}

.iconsvgpicker_content .icon{padding:2px;font-size:30px}

.svgeditdiv{width:96%;margin:0 2%}
.tbackground{text-align: center;border-radius: 2px;width:100%;height:230px;line-height:230px;overflow: hidden;}
.tbackground .icon{font-size:100px;background-position:0 0,5px 5px!important;background-size:10px 10px!important;background-image:linear-gradient(45deg,#eee 25%,transparent 25%,transparent 75%,#eee 75%,#eee 100%),linear-gradient(45deg,#eee 25%,#fff 25%,#fff 75%,#eee 75%,#eee 100%)!important}
.svgeditdiv .color-block-lists{width:100%;display:flex;margin-top:15px;cursor:pointer}
.svgeditdiv .iconsvgsetdiv{width:100%;display:flex;margin-top:8px;align-items:center}
.svgeditdiv .color-block{flex:1;width:20px;height: 20px;overflow: hidden;}
/*
.svgeditdiv svg path.selected {stroke: #666;stroke-dasharray: 20;stroke-width: 5;}
.svgeditdiv svg path{cursor: pointer;}
*/