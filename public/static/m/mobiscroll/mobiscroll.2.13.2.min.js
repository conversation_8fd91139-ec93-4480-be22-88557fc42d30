/*!
 * Mobiscroll v2.13.2
 * http://mobiscroll.com
 *
 * Copyright 2010-2014, Acid Media
 * Licensed under the MIT license.
 *
 */
(function(g,e){function j(p){var o;for(o in p){if(m[p[o]]!==e){return true}}return false}function f(){var o=["Webkit","Moz","O","ms"],q;for(q in o){if(j([o[q]+"Transform"])){return"-"+o[q].toLowerCase()+"-"}}return""}function n(r,q,p){var o=r;if(typeof q==="object"){return r.each(function(){if(!this.id){this.id="mobiscroll"+(++d)}if(a[this.id]){a[this.id].destroy()}new g.mobiscroll.classes[q.component||"Scroller"](this,q)})}if(typeof q==="string"){r.each(function(){var s,t=a[this.id];if(t&&t[q]){s=t[q].apply(this,Array.prototype.slice.call(p,1));if(s!==e){o=s;return false}}})}return o}var d=+new Date(),h={},a={},l=g.extend,m=document.createElement("modernizr").style,k=j(["perspectiveProperty","WebkitPerspective","MozPerspective","OPerspective","msPerspective"]),c=j(["flex","msFlex","WebkitBoxDirection"]),i=f(),b=i.replace(/^\-/,"").replace(/\-$/,"").replace("moz","Moz");g.fn.mobiscroll=function(o){l(this,g.mobiscroll.components);return n(this,o,arguments)};g.mobiscroll=g.mobiscroll||{version:"2.13.2",util:{prefix:i,jsPrefix:b,has3d:k,hasFlex:c,testTouch:function(o){if(o.type=="touchstart"){h[o.target]=true}else{if(h[o.target]){delete h[o.target];return false}}return true},isNumeric:function(o){return o-parseFloat(o)>=0},getCoord:function(p,q){var o=p.originalEvent||p;return o.changedTouches?o.changedTouches[0]["page"+q]:p["page"+q]},constrain:function(q,p,o){return Math.max(p,Math.min(q,o))}},tapped:false,presets:{scroller:{},numpad:{}},themes:{listview:{}},i18n:{},instances:a,classes:{},components:{},defaults:{theme:"mobiscroll",context:"body"},userdef:{},setDefaults:function(p){l(this.userdef,p)},presetShort:function(o,r,q){this.components[o]=function(p){return n(this,l(p,{component:r,preset:q===false?e:o}),arguments)}}};g.scroller=g.scroller||g.mobiscroll;g.fn.scroller=g.fn.scroller||g.fn.mobiscroll})(jQuery);(function(a){a.mobiscroll.i18n.zh=a.extend(a.mobiscroll.i18n.zh,{setText:"确定",cancelText:"取消",clearText:"明确",selectedText:"选",dateFormat:"yy/mm/dd",dateOrder:"yymmdd",dayNames:["星期日","周一","周二","周三","周四","周五","周六"],dayNamesShort:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayNamesMin:["日","一","二","三","四","五","六"],dayText:"日",hourText:"时",minuteText:"分",monthNames:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],monthNamesShort:["一","二","三","四","五","六","七","八","九","十","十一","十二"],monthText:"月",secText:"秒",timeFormat:"HH:ii",timeWheels:"HHii",yearText:"年",nowText:"当前",pmText:"下午",amText:"上午",dateText:"日",timeText:"时间",calendarText:"日历",closeText:"关闭",fromText:"开始时间",toText:"结束时间",wholeText:"合计",fractionText:"分数",unitText:"单位",labels:["年","月","日","小时","分钟","秒",""],labelsShort:["年","月","日","点","分","秒",""],startText:"开始",stopText:"停止",resetText:"重置",lapText:"圈",hideText:"隐藏"})})(jQuery);(function(a){a.mobiscroll.themes.android={dateOrder:"Mddyy",mode:"clickpick",height:50,showLabel:false,btnStartClass:"mbsc-ic mbsc-ic-play3",btnStopClass:"mbsc-ic mbsc-ic-pause2",btnResetClass:"mbsc-ic mbsc-ic-stop2",btnLapClass:"mbsc-ic mbsc-ic-loop2"}})(jQuery);(function(b){var a=b.mobiscroll.themes,c={dateOrder:"Mddyy",rows:5,minWidth:76,height:36,showLabel:false,selectedLineHeight:true,selectedLineBorder:2,useShortLabels:true,icon:{filled:"star3",empty:"star"},btnPlusClass:"mbsc-ic mbsc-ic-arrow-down6",btnMinusClass:"mbsc-ic mbsc-ic-arrow-up6",onThemeLoad:function(e,d){if(d.theme){d.theme=d.theme.replace("android-ics","android-holo").replace(" light","-light")}},onMarkupReady:function(d){d.addClass("mbsc-android-holo")}};a["android-holo"]=c;a["android-holo-light"]=c;a["android-ics"]=c;a["android-ics light"]=c;a["android-holo light"]=c})(jQuery);(function(a){a.mobiscroll.themes.ios={display:"bottom",dateOrder:"MMdyy",rows:5,height:30,minWidth:60,headerText:false,showLabel:false,btnWidth:false,selectedLineHeight:true,selectedLineBorder:2,useShortLabels:true}})(jQuery);(function(a){a.mobiscroll.themes.ios7={display:"bottom",dateOrder:"MMdyy",rows:5,height:34,minWidth:55,headerText:false,showLabel:false,btnWidth:false,selectedLineHeight:true,selectedLineBorder:1,useShortLabels:true,deleteIcon:"backspace3",checkIcon:"ion-ios7-checkmark-empty",btnCalPrevClass:"mbsc-ic mbsc-ic-arrow-left5",btnCalNextClass:"mbsc-ic mbsc-ic-arrow-right5",btnPlusClass:"mbsc-ic mbsc-ic-arrow-down5",btnMinusClass:"mbsc-ic mbsc-ic-arrow-up5"}})(jQuery);(function(b){var a=b.mobile&&b.mobile.version.match(/1\.4/);b.mobiscroll.themes.jqm={jqmBorder:"a",jqmBody:a?"a":"c",jqmHeader:"b",jqmWheel:"d",jqmLine:"b",jqmClickPick:"c",jqmSet:"b",jqmCancel:"c",disabledClass:"ui-disabled",activeClass:"ui-btn-active",activeTabInnerClass:"ui-btn-active",btnCalPrevClass:"",btnCalNextClass:"",selectedLineHeight:true,selectedLineBorder:1,onThemeLoad:function(g,e){var f=e.jqmBody||"c",c=e.jqmEventText||"b",d=e.jqmEventBubble||"a";e.dayClass="ui-body-a ui-body-"+f;e.innerDayClass="ui-state-default ui-btn ui-btn-up-"+f;e.calendarClass="ui-body-a ui-body-"+f;e.weekNrClass="ui-body-a ui-body-"+f;e.eventTextClass="ui-btn-up-"+c;e.eventBubbleClass="ui-body-"+d},onEventBubbleShow:function(c,d){b(".dw-cal-event-list",d).attr("data-role","listview");d.page().trigger("create")},onMarkupInserted:function(e,d){var c=d.settings;if(a){e.addClass("mbsc-jqm14");b(".mbsc-np-btn, .dwwb, .dw-cal-sc-m-cell .dw-i",e).addClass("ui-btn");b(".dwbc div.dwb, .dw-dr",e).addClass("ui-btn ui-mini ui-corner-all");b(".dw-cal-prev .dw-cal-btn-txt",e).addClass("ui-btn ui-icon-arrow-l ui-btn-icon-notext ui-shadow ui-corner-all");b(".dw-cal-next .dw-cal-btn-txt",e).addClass("ui-btn ui-icon-arrow-r ui-btn-icon-notext ui-shadow ui-corner-all")}b(".dw",e).removeClass("dwbg").addClass("ui-selectmenu ui-overlay-shadow ui-corner-all ui-body-"+c.jqmBorder);b(".dwbc .dwb",e).attr("data-role","button").attr("data-mini","true").attr("data-theme",c.jqmCancel);b(".dwb-s .dwb",e).addClass("ui-btn-"+c.jqmSet).attr("data-theme",c.jqmSet);b(".dwwb",e).attr("data-role","button").attr("data-theme",c.jqmClickPick);b(".dwv",e).addClass("ui-header ui-bar-"+c.jqmHeader);b(".dwwr",e).addClass("ui-corner-all ui-body-"+c.jqmBody);b(".dwwl",e).addClass("ui-body-"+c.jqmWheel);b(".dwwol",e).addClass("ui-body-"+c.jqmLine);b(".dwl",e).addClass("ui-body-"+c.jqmBody);b(".dw-cal-tabs",e).attr("data-role","navbar");b(".dw-cal-prev .dw-cal-btn-txt",e).attr("data-role","button").attr("data-icon","arrow-l").attr("data-iconpos","notext");b(".dw-cal-next .dw-cal-btn-txt",e).attr("data-role","button").attr("data-icon","arrow-r").attr("data-iconpos","notext");b(".dw-cal-events",e).attr("data-role","page");b(".dw-dr",e).attr("data-role","button").attr("data-mini","true");b(".mbsc-np-btn",e).attr("data-role","button").attr("data-corners","false");e.trigger("create")}}})(jQuery);(function(a){a.mobiscroll.themes["sense-ui"]={btnStartClass:"mbsc-ic mbsc-ic-play3",btnStopClass:"mbsc-ic mbsc-ic-pause2",btnResetClass:"mbsc-ic mbsc-ic-stop2",btnLapClass:"mbsc-ic mbsc-ic-loop2"}})(jQuery);(function(b){var a=b.mobiscroll.themes,c={minWidth:76,height:76,accent:"none",dateOrder:"mmMMddDDyy",headerText:false,showLabel:false,deleteIcon:"backspace4",icon:{filled:"star3",empty:"star"},btnWidth:false,btnStartClass:"mbsc-ic mbsc-ic-play3",btnStopClass:"mbsc-ic mbsc-ic-pause2",btnResetClass:"mbsc-ic mbsc-ic-stop2",btnLapClass:"mbsc-ic mbsc-ic-loop2",btnHideClass:"mbsc-ic mbsc-ic-close",btnCalPrevClass:"mbsc-ic mbsc-ic-arrow-left2",btnCalNextClass:"mbsc-ic mbsc-ic-arrow-right2",btnPlusClass:"mbsc-ic mbsc-ic-plus",btnMinusClass:"mbsc-ic mbsc-ic-minus",onMarkupInserted:function(h,e){var d,g,f;h.addClass("mbsc-wp");b(".dw",h).addClass("mbsc-wp-"+e.settings.accent);b(".dwb-s .dwb",h).addClass("mbsc-ic mbsc-ic-checkmark");b(".dwb-c .dwb",h).addClass("mbsc-ic mbsc-ic-close");b(".dwb-cl .dwb",h).addClass("mbsc-ic mbsc-ic-close");b(".dwb-n .dwb",h).addClass("mbsc-ic mbsc-ic-loop2");b(".dwwl",h).on("touchstart mousedown DOMMouseScroll mousewheel",function(i){if(i.type==="mousedown"&&g){return}g=i.type==="touchstart";d=true;f=b(this).hasClass("wpa");b(".dwwl",h).removeClass("wpa");b(this).addClass("wpa")}).on("touchmove mousemove",function(){d=false}).on("touchend mouseup",function(i){if(d&&f&&b(i.target).closest(".dw-li").hasClass("dw-sel")){b(this).removeClass("wpa")}if(i.type==="mouseup"){g=false}d=false})},onThemeLoad:function(f,e){if(f&&f.dateOrder&&!e.dateOrder){var d=f.dateOrder;d=d.match(/mm/i)?d.replace(/mmMM|mm|MM/,"mmMM"):d.replace(/mM|m|M/,"mM");d=d.match(/dd/i)?d.replace(/ddDD|dd|DD/,"ddDD"):d.replace(/dD|d|D/,"dD");e.dateOrder=d}if(e.theme){e.theme=e.theme.replace(" light","-light")}}};a.wp=c;a["wp-light"]=c;a["wp light"]=c})(jQuery);(function(b,c){var a=b.mobiscroll;a.datetime={defaults:{shortYearCutoff:"+10",monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayNamesShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],dayNamesMin:["S","M","T","W","T","F","S"],monthText:"Month",amText:"am",pmText:"pm",getYear:function(e){return e.getFullYear()},getMonth:function(e){return e.getMonth()},getDay:function(e){return e.getDate()},getDate:function(l,e,k,j,f,g){return new Date(l,e,k,j||0,f||0,g||0)},getMaxDayOfMonth:function(e,d){return 32-new Date(e,d,32).getDate()},getWeekNumber:function(f){f=new Date(f);f.setHours(0,0,0);f.setDate(f.getDate()+4-(f.getDay()||7));var e=new Date(f.getFullYear(),0,1);return Math.ceil((((f-e)/86400000)+1)/7)}},formatDate:function(p,e,f){if(!e){return null}var q=b.extend({},a.datetime.defaults,f),n=function(h){var i=0;while(k+1<p.length&&p.charAt(k+1)==h){i++;k++}return i},j=function(i,r,h){var s=""+r;if(n(i)){while(s.length<h){s="0"+s}}return s},g=function(h,t,r,i){return(n(h)?i[t]:r[t])},k,m,d="",o=false;for(k=0;k<p.length;k++){if(o){if(p.charAt(k)=="'"&&!n("'")){o=false}else{d+=p.charAt(k)}}else{switch(p.charAt(k)){case"d":d+=j("d",q.getDay(e),2);break;case"D":d+=g("D",e.getDay(),q.dayNamesShort,q.dayNames);break;case"o":d+=j("o",(e.getTime()-new Date(e.getFullYear(),0,0).getTime())/86400000,3);break;case"m":d+=j("m",q.getMonth(e)+1,2);break;case"M":d+=g("M",q.getMonth(e),q.monthNamesShort,q.monthNames);break;case"y":m=q.getYear(e);d+=(n("y")?m:(m%100<10?"0":"")+m%100);break;case"h":var l=e.getHours();d+=j("h",(l>12?(l-12):(l===0?12:l)),2);break;case"H":d+=j("H",e.getHours(),2);break;case"i":d+=j("i",e.getMinutes(),2);break;case"s":d+=j("s",e.getSeconds(),2);break;case"a":d+=e.getHours()>11?q.pmText:q.amText;break;case"A":d+=e.getHours()>11?q.pmText.toUpperCase():q.amText.toUpperCase();break;case"'":if(n("'")){d+="'"}else{o=true}break;default:d+=p.charAt(k)}}}return d},parseDate:function(v,n,x){var k=b.extend({},a.datetime.defaults,x),j=k.defaultValue||new Date();if(!v||!n){return j}if(n.getTime){return n}n=(typeof n=="object"?n.toString():n+"");var d=k.shortYearCutoff,f=k.getYear(j),z=k.getMonth(j)+1,t=k.getDay(j),i=-1,w=j.getHours(),o=j.getMinutes(),g=0,l=-1,r=false,m=function(s){var B=(e+1<v.length&&v.charAt(e+1)==s);if(B){e++}return B},A=function(B){m(B);var C=(B=="@"?14:(B=="!"?20:(B=="y"?4:(B=="o"?3:2)))),D=new RegExp("^\\d{1,"+C+"}"),s=n.substr(u).match(D);if(!s){return 0}u+=s[0].length;return parseInt(s[0],10)},h=function(C,E,B){var F=(m(C)?B:E),D;for(D=0;D<F.length;D++){if(n.substr(u,F[D].length).toLowerCase()==F[D].toLowerCase()){u+=F[D].length;return D+1}}return 0},q=function(){u++},u=0,e;for(e=0;e<v.length;e++){if(r){if(v.charAt(e)=="'"&&!m("'")){r=false}else{q()}}else{switch(v.charAt(e)){case"d":t=A("d");break;case"D":h("D",k.dayNamesShort,k.dayNames);break;case"o":i=A("o");break;case"m":z=A("m");break;case"M":z=h("M",k.monthNamesShort,k.monthNames);break;case"y":f=A("y");break;case"H":w=A("H");break;case"h":w=A("h");break;case"i":o=A("i");break;case"s":g=A("s");break;case"a":l=h("a",[k.amText,k.pmText],[k.amText,k.pmText])-1;break;case"A":l=h("A",[k.amText,k.pmText],[k.amText,k.pmText])-1;break;case"'":if(m("'")){q()}else{r=true}break;default:q()}}}if(f<100){f+=new Date().getFullYear()-new Date().getFullYear()%100+(f<=(typeof d!="string"?d:new Date().getFullYear()%100+parseInt(d,10))?0:-100)}if(i>-1){z=1;t=i;do{var p=32-new Date(f,z-1,32).getDate();if(t<=p){break}z++;t-=p}while(true)}w=(l==-1)?w:((l&&w<12)?(w+12):(!l&&w==12?0:w));var y=k.getDate(f,z-1,t,w,o,g);if(k.getYear(y)!=f||k.getMonth(y)+1!=z||k.getDay(y)!=t){return j}return y}};a.formatDate=a.datetime.formatDate;a.parseDate=a.datetime.parseDate})(jQuery);(function(f,i,m,h){var c,r,s=f.extend,n=f.mobiscroll,d=n.instances,k=n.userdef,a=n.util,p=a.jsPrefix,l=a.has3d,b=a.getCoord,o=a.constrain,g=/android [1-3]/i.test(navigator.userAgent),q="webkitAnimationEnd animationend",j=function(){},e=function(t){t.preventDefault()};n.classes.Widget=function(ac,ad,C){var M,z,A,aa,V,H,y,t,B,K,P,Y,E,W,v,J,O,S,L,ae,Z,D,x,N,R,af,u,U=this,T=f(ac),G=[],Q={};function F(ah){if(P){P.removeClass("dwb-a")}P=f(this);if(!P.hasClass("dwb-d")&&!P.hasClass("dwb-nhl")){P.addClass("dwb-a")}if(ah.type==="mousedown"){f(m).on("mouseup",I)}}function I(ah){if(P){P.removeClass("dwb-a");P=null}if(ah.type==="mouseup"){f(m).off("mouseup",I)}}function X(ah){if(!ah){y.focus()}U.ariaMessage(Z.ariaMessage)}function ab(ai){var al,ak,aj,ah=Z.focusOnClose;aa.remove();if(c&&!ai){setTimeout(function(){if(ah===h){r=true;al=c[0];aj=al.type;ak=al.value;try{al.type="button"}catch(am){}c.focus();al.type=aj;al.value=ak}else{if(ah){if(d[f(ah).attr("id")]){n.tapped=false}f(ah).focus()}}},200)}U._isVisible=false;ag("onHide",[])}function w(ah){clearTimeout(Q[ah.type]);Q[ah.type]=setTimeout(function(){var ai=ah.type=="scroll";if(ai&&!D){return}U.position(!ai)},200)}function ag(aj,ai){var ah;ai.push(U);f.each([k,N,L,ad],function(al,ak){if(ak&&ak[aj]){ah=ak[aj].apply(ac,ai)}});return ah}U.position=function(aG){var ar,aA,av,ao,au,aD,ay,ax,aB,aj,aC,ai,aE,ak,aF,az,aI=0,an=0,aq={},aH=Math.min(t[0].innerWidth||t.innerWidth(),H.width()),am=t[0].innerHeight||t.innerHeight();if((af===aH&&u===am&&aG)||ae){return}if(W&&U._isLiquid&&Z.display!=="bubble"){y.width(aH)}if(ag("onPosition",[aa,aH,am])===false||!W){return}aF=t.scrollLeft();az=t.scrollTop();ao=Z.anchor===h?T:f(Z.anchor);if(U._isLiquid&&Z.layout!=="liquid"){if(aH<400){aa.addClass("dw-liq")}else{aa.removeClass("dw-liq")}}if(/modal|bubble/.test(Z.display)){B.width("");f(".mbsc-w-p",aa).each(function(){ar=f(this).outerWidth(true);aI+=ar;an=(ar>an)?ar:an});ar=aI>aH?an:aI;B.width(ar).css("white-space",aI>aH?"":"nowrap")}J=y.outerWidth();O=y.outerHeight(true);D=O<=am&&J<=aH;U.scrollLock=D;if(Z.display=="modal"){aA=Math.max(0,aF+(aH-J)/2);av=az+(am-O)/2}else{if(Z.display=="bubble"){ak=true;aj=f(".dw-arrw-i",aa);ay=ao.offset();ax=Math.abs(z.offset().top-ay.top);aB=Math.abs(z.offset().left-ay.left);au=ao.outerWidth();aD=ao.outerHeight();aA=o(aB-(y.outerWidth(true)-au)/2,aF+3,aF+aH-J-3);av=ax-O;if((av<az)||(ax>az+am)){y.removeClass("dw-bubble-top").addClass("dw-bubble-bottom");av=ax+aD}else{y.removeClass("dw-bubble-bottom").addClass("dw-bubble-top")}aC=aj.outerWidth();ai=o(aB+au/2-(aA+(J-aC)/2),0,aC);f(".dw-arr",aa).css({left:ai})}else{aA=aF;if(Z.display=="top"){av=az}else{if(Z.display=="bottom"){av=az+am-O}}}}av=av<0?0:av;aq.top=av;aq.left=aA;y.css(aq);H.height(0);aE=Math.max(av+O,Z.context=="body"?f(m).height():z[0].scrollHeight);H.css({height:aE});if(ak&&((av+O>az+am)||(ax>az+am))){ae=true;setTimeout(function(){ae=false},300);t.scrollTop(Math.min(av+O-am,aE-am))}af=aH;u=am};U.attachShow=function(ah,ai){G.push(ah);if(Z.display!=="inline"){ah.on("mousedown.dw",function(aj){if(x){aj.preventDefault()}}).on((Z.showOnFocus?"focus.dw":"")+(Z.showOnTap?" click.dw":""),function(aj){if((aj.type!=="focus"||(aj.type==="focus"&&!r))&&!n.tapped){if(ai){ai()}if(f(m.activeElement).is("input,textarea")){f(m.activeElement).blur()}c=ah;U.show()}setTimeout(function(){r=false},300)})}};U.select=function(){if(!W||U.hide(false,"set")!==false){U._fillValue();ag("onSelect",[U.val])}};U.cancel=function(){if(!W||U.hide(false,"cancel")!==false){ag("onCancel",[U.val])}};U.clear=function(){ag("onClear",[aa]);if(W&&!U.live){U.hide(false,"clear")}U.setValue(null,true)};U.enable=function(){Z.disabled=false;if(U._isInput){T.prop("disabled",false)}};U.disable=function(){Z.disabled=true;if(U._isInput){T.prop("disabled",true)}};U.show=function(ai,ah){var aj;if(Z.disabled||U._isVisible){return}if(Y!==false){if(Z.display=="top"){Y="slidedown"}if(Z.display=="bottom"){Y="slideup"}}U._readValue();ag("onBeforeShow",[]);aj='<div lang="'+Z.lang+'" class="mbsc-'+Z.theme+" dw-"+Z.display+" "+(Z.cssClass||"")+(U._isLiquid?" dw-liq":"")+(g?" mbsc-old":"")+(E?"":" dw-nobtn")+'"><div class="dw-persp">'+(W?'<div class="dwo"></div>':"")+"<div"+(W?' role="dialog" tabindex="-1"':"")+' class="dw'+(Z.rtl?" dw-rtl":" dw-ltr")+'">'+(Z.display==="bubble"?'<div class="dw-arrw"><div class="dw-arrw-i"><div class="dw-arr"></div></div></div>':"")+'<div class="dwwr"><div aria-live="assertive" class="dw-aria dw-hidden"></div>'+(Z.headerText?'<div class="dwv">'+Z.headerText+"</div>":"")+'<div class="dwcc">';aj+=U._generateContent();aj+="</div>";if(E){aj+='<div class="dwbc">';f.each(K,function(al,ak){ak=(typeof ak==="string")?U.buttons[ak]:ak;aj+="<div"+(Z.btnWidth?' style="width:'+(100/K.length)+'%"':"")+' class="dwbw '+ak.css+'"><div tabindex="0" role="button" class="dwb dwb'+al+' dwb-e">'+ak.text+"</div></div>"});aj+="</div>"}aj+="</div></div></div></div>";aa=f(aj);H=f(".dw-persp",aa);V=f(".dwo",aa);B=f(".dwwr",aa);A=f(".dwv",aa);y=f(".dw",aa);M=f(".dw-aria",aa);U._markup=aa;U._header=A;U._isVisible=true;S="orientationchange resize";U._markupReady();ag("onMarkupReady",[aa]);if(W){f(i).on("keydown.dw",function(ak){if(ak.keyCode==13){U.select()}else{if(ak.keyCode==27){U.cancel()}}});if(Z.scrollLock){aa.on("touchstart touchmove",function(ak){if(D){ak.preventDefault()}})}if(p!=="Moz"){f("input,select,button",z).each(function(){if(!this.disabled){f(this).addClass("dwtd").prop("disabled",true)}})}S+=" scroll";n.activeInstance=U;aa.appendTo(z);if(l&&Y&&!ai){aa.addClass("dw-in dw-trans").on(q,function(){aa.removeClass("dw-in dw-trans").find(".dw").removeClass("dw-"+Y);X(ah)}).find(".dw").addClass("dw-"+Y)}}else{if(T.is("div")){T.html(aa)}else{aa.insertAfter(T)}}ag("onMarkupInserted",[aa]);U.position();t.on(S,w);aa.on("selectstart mousedown",e).on("click",".dwb-e",e).on("keydown",".dwb-e",function(ak){if(ak.keyCode==32){ak.preventDefault();ak.stopPropagation();f(this).click()}});setTimeout(function(){f.each(K,function(al,ak){U.tap(f(".dwb"+al,aa),function(am){ak=(typeof ak==="string")?U.buttons[ak]:ak;ak.handler.call(this,am,U)},true)});if(Z.closeOnOverlay){U.tap(V,function(){U.cancel()})}if(W&&!Y){X(ah)}aa.on("touchstart mousedown",".dwb-e",F).on("touchend",".dwb-e",I);U._attachEvents(aa)},300);ag("onShow",[aa,U._valueText]);if(f(".dwc").length==1){f(".dwc").css("width","100%")}};U.hide=function(ah,ai,aj){if(!U._isVisible||(!aj&&!U._isValid&&ai=="set")||(!aj&&ag("onClose",[U._valueText,ai])===false)){return false}if(aa){if(p!=="Moz"){f(".dwtd",z).each(function(){f(this).prop("disabled",false).removeClass("dwtd")})}if(l&&W&&Y&&!ah&&!aa.hasClass("dw-trans")){aa.addClass("dw-out dw-trans").find(".dw").addClass("dw-"+Y).on(q,function(){ab(ah)})}else{ab(ah)}t.off(S,w)}delete n.activeInstance};U.ariaMessage=function(ah){M.html("");setTimeout(function(){M.html(ah)},100)};U.isVisible=function(){return U._isVisible};U.setValue=j;U._generateContent=j;U._attachEvents=j;U._readValue=j;U._fillValue=j;U._markupReady=j;U._processSettings=j;U.tap=function(am,al,aj){var ai,ah,ak;if(Z.tap){am.on("touchstart.dw",function(an){if(aj){an.preventDefault()}ai=b(an,"X");ah=b(an,"Y");ak=false}).on("touchmove.dw",function(an){if(Math.abs(b(an,"X")-ai)>20||Math.abs(b(an,"Y")-ah)>20){ak=true}}).on("touchend.dw",function(ao){var an=this;if(!ak){ao.preventDefault();setTimeout(function(){al.call(an,ao)},g?400:10)}n.tapped=true;setTimeout(function(){n.tapped=false},500)})}am.on("click.dw",function(an){if(!n.tapped){al.call(this,an)}an.preventDefault()})};U.option=function(ah,ai){var aj={};if(typeof ah==="object"){aj=ah}else{aj[ah]=ai}U.init(aj)};U.destroy=function(){U.hide(true,false,true);f.each(G,function(ai,ah){ah.off(".dw")});if(U._isInput&&x){ac.readOnly=R}ag("onDestroy",[]);delete d[ac.id]};U.getInst=function(){return U};U.trigger=ag;U.init=function(ah){U.settings=Z={};s(ad,ah);s(Z,n.defaults,U._defaults,k,ad);N=n.themes[Z.theme]||n.themes.mobiscroll;v=n.i18n[Z.lang];ag("onThemeLoad",[v,ad]);s(Z,N,v,k,ad);L=n.presets[U._class][Z.preset];Z.buttons=Z.buttons||(Z.display!=="inline"?["set","cancel"]:[]);Z.headerText=Z.headerText===h?(Z.display!=="inline"?"{value}":false):Z.headerText;if(L){L=L.call(ac,U);s(Z,L,ad)}if(!n.themes[Z.theme]){Z.theme="mobiscroll"}U._isLiquid=(Z.layout||(/top|bottom/.test(Z.display)?"liquid":""))==="liquid";U._processSettings();T.off(".dw");Y=g?false:Z.animate;K=Z.buttons;W=Z.display!=="inline";x=Z.showOnFocus||Z.showOnTap;t=f(Z.context=="body"?i:Z.context);z=f(Z.context);if(!Z.setText){K.splice(f.inArray("set",K),1)}if(!Z.cancelText){K.splice(f.inArray("cancel",K),1)}if(Z.button3){K.splice(f.inArray("set",K)+1,0,{text:Z.button3Text,handler:Z.button3})}U.context=t;U.live=f.inArray("set",K)==-1;U.buttons.set={text:Z.setText,css:"dwb-s",handler:U.select};U.buttons.cancel={text:(U.live)?Z.closeText:Z.cancelText,css:"dwb-c",handler:U.cancel};U.buttons.clear={text:Z.clearText,css:"dwb-cl",handler:U.clear};U._isInput=T.is("input");E=K.length>0;if(U._isVisible){U.hide(true,false,true)}if(W){U._readValue();if(U._isInput&&x){if(R===h){R=ac.readOnly}ac.readOnly=true}U.attachShow(T)}else{U.show()}if(U._isInput){T.on("change.dw",function(){if(!U._preventChange){U.setValue(T.val(),false)}U._preventChange=false})}};U.val=null;U.buttons={};U._isValid=true;if(!C){d[ac.id]=U;U.init(ad)}};n.classes.Widget.prototype._defaults={lang:"zh",setText:"Set",selectedText:"Selected",closeText:"Close",cancelText:"Cancel",clearText:"Clear",disabled:false,closeOnOverlay:true,showOnFocus:true,showOnTap:true,display:"modal",scrollLock:true,tap:true,btnWidth:true,focusOnClose:false};n.themes.mobiscroll={rows:5,showLabel:false,headerText:false,btnWidth:false,selectedLineHeight:true,selectedLineBorder:1,dateOrder:"MMddyy",weekDays:"min",checkIcon:"ion-ios7-checkmark-empty",btnPlusClass:"mbsc-ic mbsc-ic-arrow-down5",btnMinusClass:"mbsc-ic mbsc-ic-arrow-up5",btnCalPrevClass:"mbsc-ic mbsc-ic-arrow-left5",btnCalNextClass:"mbsc-ic mbsc-ic-arrow-right5"};f(i).on("focus",function(){if(c){r=true}});f(m).on("mouseover mouseup mousedown click",function(t){if(n.tapped){t.stopPropagation();t.preventDefault();return false}})})(jQuery,window,document);(function(j,m,o,g){var h,d=j.mobiscroll,i=d.classes,a=d.instances,k=d.util,c=k.jsPrefix,l=k.has3d,b=k.hasFlex,e=k.getCoord,p=k.constrain,f=k.testTouch;function n(q){var r={values:[],keys:[]};j.each(q,function(t,s){r.keys.push(t);r.values.push(s)});return r}i.Scroller=function(ak,am,H){var aj,Y,ae,an,af,T,F,ab,t,Q,x,K,ah,ao,O,r,V,Z,S,ad=this,ac=j(ak),R={},U={},B={},E=[];function G(s){if(f(s)&&!h&&!ab&&!Y&&!I(this)){s.preventDefault();s.stopPropagation();h=true;ae=af.mode!="clickpick";r=j(".dw-ul",this);v(r);t=R[V]!==g;ah=t?N(r):U[V];Q=e(s,"Y");x=new Date();K=Q;ag(r,V,ah,0.001);if(ae){r.closest(".dwwl").addClass("dwa")}if(s.type==="mousedown"){j(o).on("mousemove",J).on("mouseup",ai)}}}function J(s){if(h){if(ae){s.preventDefault();s.stopPropagation();K=e(s,"Y");if(Math.abs(K-Q)>3||t){ag(r,V,p(ah+(Q-K)/an,ao-1,O+1));t=true}}}}function ai(ax){if(h){var at=new Date()-x,ar=p(ah+(Q-K)/an,ao-1,O+1),au,aw,aq,av=r.offset().top;ax.stopPropagation();if(l&&at<300){au=(K-Q)/at;aw=(au*au)/af.speedUnit;if(K-Q<0){aw=-aw}}else{aw=K-Q}aq=Math.round(ah-aw/an);if(!t){var ay=Math.floor((K-av)/an),az=j(j(".dw-li",r)[ay]),s=az.hasClass("dw-v"),ap=ae;if(T("onValueTap",[az])!==false&&s){aq=ay}else{ap=true}if(ap&&s){az.addClass("dw-hl");setTimeout(function(){az.removeClass("dw-hl")},100)}}if(ae){y(r,aq,0,true,Math.round(ar))}if(ax.type==="mouseup"){j(o).off("mousemove",J).off("mouseup",ai)}h=false}}function M(s){Y=j(this);if(Y.hasClass("dwwb")){if(f(s)){X(s,Y.closest(".dwwl"),Y.hasClass("dwwbp")?w:A)}}if(s.type==="mousedown"){j(o).on("mouseup",P)}}function P(s){Y=null;if(ab){clearInterval(S);ab=false}if(s.type==="mouseup"){j(o).off("mouseup",P)}}function C(s){if(s.keyCode==38){X(s,j(this),A)}else{if(s.keyCode==40){X(s,j(this),w)}}}function u(){if(ab){clearInterval(S);ab=false}}function D(ap){if(!I(this)){ap.preventDefault();ap=ap.originalEvent||ap;var aq=ap.wheelDelta?(ap.wheelDelta/120):(ap.detail?(-ap.detail/3):0),s=j(".dw-ul",this);v(s);y(s,Math.round(U[V]-aq),aq<0?1:2)}}function X(ar,s,aq){ar.stopPropagation();ar.preventDefault();if(!ab&&!I(s)&&!s.hasClass("dwa")){ab=true;var ap=s.find(".dw-ul");v(ap);clearInterval(S);S=setInterval(function(){aq(ap)},af.delay);aq(ap)}}function I(s){if(j.isArray(af.readonly)){var ap=j(".dwwl",aj).index(s);return af.readonly[ap]}return af.readonly}function L(at){var ar='<div class="dw-bf">',av=E[at],ap=av.values?av:n(av),s=1,aw=ap.labels||[],aq=ap.values,au=ap.keys||aq;j.each(aq,function(ay,ax){if(s%20===0){ar+='</div><div class="dw-bf">'}ar+='<div role="option" aria-selected="false" class="dw-li dw-v" data-val="'+au[ay]+'"'+(aw[ay]?' aria-label="'+aw[ay]+'"':"")+' style="height:'+an+"px;line-height:"+an+'px;"><div class="dw-i"'+(Z>1?' style="line-height:'+Math.round(an/Z)+"px;font-size:"+Math.round(an/Z*0.8)+'px;"':"")+">"+ax+"</div></div>";s++});ar+="</div>";return ar}function v(ap){var s=ap.closest(".dwwl").hasClass("dwwms");ao=j(".dw-li",ap).index(j(s?".dw-li":".dw-v",ap).eq(0));O=Math.max(ao,j(".dw-li",ap).index(j(s?".dw-li":".dw-v",ap).eq(-1))-(s?af.rows-1:0));V=j(".dw-ul",aj).index(ap)}function q(s){var ap=af.headerText;return ap?(typeof ap==="function"?ap.call(ak,s):ap.replace(/\{value\}/i,s)):""}function N(aq){var ar=m.getComputedStyle?getComputedStyle(aq[0]):aq[0].style,s,ap;if(l){j.each(["t","webkitT","MozT","OT","msT"],function(au,at){if(ar[at+"ransform"]!==g){s=ar[at+"ransform"];return false}});s=s.split(")")[0].split(", ");ap=s[13]||s[5]}else{ap=ar.top.replace("px","")}return Math.round(-ap/an)}function z(ap,s){clearTimeout(R[s]);delete R[s];ap.closest(".dwwl").removeClass("dwa")}function ag(aq,s,av,au,at){var ap=-av*an,ar=aq[0].style;if(ap==B[s]&&R[s]){return}B[s]=ap;ar[c+"Transition"]="all "+(au?au.toFixed(3):0)+"s ease-out";if(l){ar[c+"Transform"]="translate3d(0,"+ap+"px,0)"}else{ar.top=ap+"px"}if(R[s]){z(aq,s)}if(au&&at){aq.closest(".dwwl").addClass("dwa");R[s]=setTimeout(function(){z(aq,s)},au*1000)}U[s]=av}function W(s,ay,ap,aA){var ax=j('.dw-li[data-val="'+s+'"]',ay),az=j(".dw-li",ay),aw=az.index(ax),aq=az.length;if(aA){v(ay)}else{if(!ax.hasClass("dw-v")){var av=ax,au=ax,at=0,ar=0;while(aw-at>=0&&!av.hasClass("dw-v")){at++;av=az.eq(aw-at)}while(aw+ar<aq&&!au.hasClass("dw-v")){ar++;au=az.eq(aw+ar)}if(((ar<at&&ar&&ap!==2)||!at||(aw-at<0)||ap==1)&&au.hasClass("dw-v")){ax=au;aw=aw+ar}else{ax=av;aw=aw-at}}}return{cell:ax,v:aA?p(aw,ao,O):aw,val:ax.hasClass("dw-v")?ax.attr("data-val"):null}}function al(at,ap,aq,s,ar){if(T("validate",[aj,ap,at,s])!==false){j(".dw-ul",aj).each(function(ay){var ax=j(this),av=ax.closest(".dwwl").hasClass("dwwms"),az=ay==ap||ap===g,aw=W(ad.temp[ay],ax,s,av),au=aw.cell;if(!(au.hasClass("dw-sel"))||az){ad.temp[ay]=aw.val;if(!av){j(".dw-sel",ax).removeAttr("aria-selected");au.attr("aria-selected","true")}j(".dw-sel",ax).removeClass("dw-sel");au.addClass("dw-sel");ag(ax,ay,aw.v,az?at:0.1,az?ar:false)}});ad._valueText=F=af.formatResult(ad.temp);if(ad.live){ad._hasValue=aq||ad._hasValue;aa(aq,aq,0,true)}ad._header.html(q(F));if(aq){T("onChange",[F])}T("onValidated",[])}}function y(az,ap,ar,au,ax){ap=p(ap,ao,O);var ay=j(".dw-li",az).eq(ap),s=ax===g?ap:ax,at=ax!==g,aw=V,av=Math.abs(ap-s),aq=au?(ap==s?0.1:av*af.timeUnit*Math.max(0.5,(100-av)/100)):0;ad.temp[aw]=ay.attr("data-val");ag(az,aw,ap,aq,at);setTimeout(function(){al(aq,aw,true,ar,at)},10)}function w(s){var ap=U[V]+1;y(s,ap>O?ao:ap,1,true)}function A(s){var ap=U[V]-1;y(s,ap<ao?O:ap,2,true)}function aa(ar,at,aq,ap,s){if(ad._isVisible&&!ap){al(aq)}ad._valueText=F=af.formatResult(ad.temp);if(!s){ad.values=ad.temp.slice(0);ad.val=ad._hasValue?F:null}if(ar){T("onValueFill",[ad._hasValue?F:"",at]);if(ad._isInput){ac.val(ad._hasValue?F:"");if(at){ad._preventChange=true;ac.change()}}}}i.Widget.call(this,ak,am,true);ad.setValue=function(ap,ar,aq,s,at){ad._hasValue=ap!==null&&ap!==g;ad.temp=j.isArray(ap)?ap.slice(0):af.parseValue.call(ak,ap,ad);aa(ar,at===g?ar:at,aq,false,s)};ad.getValue=function(){return ad._hasValue?ad.values:null};ad.getValues=function(){var s=[],ap;for(ap in ad._selectedValues){s.push(ad._selectedValues[ap])}return s};ad.changeWheel=function(s,at,aq){if(aj){var ap=0,ar=s.length;j.each(af.wheels,function(av,au){j.each(au,function(ax,aw){if(j.inArray(ap,s)>-1){E[ap]=aw;j(".dw-ul",aj).eq(ap).html(L(ap));ar--;if(!ar){ad.position();al(at,g,aq);return false}}ap++});if(!ar){return false}})}};ad.getValidCell=W;ad._generateContent=function(){var aq,ap="",s=0;j.each(af.wheels,function(at,ar){ap+='<div class="mbsc-w-p dwc'+(af.mode!="scroller"?" dwpm":" dwsc")+(af.showLabel?"":" dwhl")+'"><div class="dwwc"'+(af.maxWidth?"":' style="max-width:600px;"')+">"+(b?"":'<table class="dw-tbl" cellpadding="0" cellspacing="0"><tr>');j.each(ar,function(av,au){E[s]=au;aq=au.label!==g?au.label:av;ap+="<"+(b?"div":"td")+' class="dwfl" style="'+(af.fixedWidth?("width:"+(af.fixedWidth[s]||af.fixedWidth)+"px;"):""+(af.maxWidth?("max-width:"+(af.maxWidth[s]||af.maxWidth)+"px;"):""))+'"><div class="dwwl dwwl'+s+(au.multiple?" dwwms":"")+'">'+(af.mode!="scroller"?'<div class="dwb-e dwwb dwwbp '+(af.btnPlusClass||"")+'" style="height:'+an+"px;line-height:"+an+'px;"><span>+</span></div><div class="dwb-e dwwb dwwbm '+(af.btnMinusClass||"")+'" style="height:'+an+"px;line-height:"+an+'px;"><span>&ndash;</span></div>':"")+'<div class="dwl">'+aq+'</div><div tabindex="0" aria-live="off" aria-label="'+aq+'" role="listbox" class="dwww"><div class="dww" style="height:'+(af.rows*an)+'px;"><div class="dw-ul" style="margin-top:'+(au.multiple?0:af.rows/2*an-an/2)+'px;">';ap+=L(s)+'</div></div><div class="dwwo"></div></div><div class="dwwol"'+(af.selectedLineHeight?' style="height:'+an+"px;margin-top:-"+(an/2+(af.selectedLineBorder||0))+'px;"':"")+"></div></div>"+(b?"</div>":"</td>");s++});ap+=(b?"":"</tr></table>")+"</div></div>"});return ap};ad._attachEvents=function(s){s.on("DOMMouseScroll mousewheel",".dwwl",D).on("keydown",".dwwl",C).on("keyup",".dwwl",u).on("touchstart mousedown",".dwwl",G).on("touchmove",".dwwl",J).on("touchend",".dwwl",ai).on("touchstart mousedown",".dwb-e",M).on("touchend",".dwb-e",P)};ad._markupReady=function(){aj=ad._markup;al()};ad._fillValue=function(){ad._hasValue=true;aa(true,true,0,true)};ad._readValue=function(){var s=ac.val()||"";ad._hasValue=s!=="";ad.temp=ad.values?ad.values.slice(0):af.parseValue(s,ad);aa()};ad._processSettings=function(){af=ad.settings;T=ad.trigger;an=af.height;Z=af.multiline;ad._isLiquid=(af.layout||(/top|bottom/.test(af.display)&&af.wheels.length==1?"liquid":""))==="liquid";ad.values=null;ad.temp=null;if(Z>1){af.cssClass=(af.cssClass||"")+" dw-ml"}};ad._selectedValues={};if(!H){a[ak.id]=ad;ad.init(am)}};i.Scroller.prototype._class="scroller";i.Scroller.prototype._defaults=j.extend({},i.Widget.prototype._defaults,{minWidth:80,height:40,rows:3,multiline:1,delay:300,readonly:false,showLabel:true,wheels:[],mode:"scroller",preset:"",speedUnit:0.0012,timeUnit:0.08,formatResult:function(q){return q.join(" ")},parseValue:function(u,t){var v=u.split(" "),q=[],r=0,s;j.each(t.settings.wheels,function(x,w){j.each(w,function(z,y){y=y.values?y:n(y);s=y.keys||y.values;if(j.inArray(v[r],s)!==-1){q.push(v[r])}else{q.push(s[0])}r++})});return q}})})(jQuery,window,document);(function(d,f){var b=d.mobiscroll,g=b.datetime,a=new Date(),e={startYear:a.getFullYear()-100,endYear:a.getFullYear()+1,showNow:false,stepHour:1,stepMinute:1,stepSecond:1,separator:" ",dateFormat:"mm/dd/yy",dateOrder:"mmddy",timeWheels:"hhiiA",timeFormat:"hh:ii A",dayText:"Day",yearText:"Year",hourText:"Hours",minuteText:"Minutes",ampmText:"&nbsp;",secText:"Seconds",nowText:"Now"},c=function(Y){var ad=d(this),A={},U;if(ad.is("input")){switch(ad.attr("type")){case"date":U="yy-mm-dd";break;case"datetime":U="yy-mm-ddTHH:ii:ssZ";break;case"datetime-local":U="yy-mm-ddTHH:ii:ss";break;case"month":U="yy-mm";A.dateOrder="mmyy";break;case"time":U="HH:ii:ss";break}var aA=ad.attr("min"),I=ad.attr("max");if(aA){A.minDate=g.parseDate(U,aA)}if(I){A.maxDate=g.parseDate(U,I)}}var at,ap,P,aB,r,L,F,T,K,x,j=d.extend({},Y.settings),ah=d.extend(Y.settings,b.datetime.defaults,e,A,j),af=0,X=[],B=[],au=[],am={},av={y:W,m:aC,d:E,h:ay,i:ag,s:u,a:H},ae=ah.invalid,ac=ah.valid,ak=ah.preset,az=ah.dateOrder,N=ah.timeWheels,z=az.match(/D/),M=N.match(/a/i),m=N.match(/h/),an=ak=="datetime"?ah.dateFormat+ah.separator+ah.timeFormat:ak=="time"?ah.timeFormat:ah.dateFormat,h=new Date(),w=ah.stepHour,t=ah.stepMinute,l=ah.stepSecond,ab=ah.minDate||new Date(ah.startYear,0,1),D=ah.maxDate||new Date(ah.endYear,11,31,23,59,59),aw=ab.getHours()%w,aq=ab.getMinutes()%t,aj=ab.getSeconds()%l,V=C(w,aw,(m?11:23)),S=C(t,aq,59),J=C(t,aq,59);U=U||an;if(ak.match(/date/i)){d.each(["y","m","d"],function(k,i){at=az.search(new RegExp(i,"i"));if(at>-1){au.push({o:at,v:i})}});au.sort(function(k,i){return k.o>i.o?1:-1});d.each(au,function(o,k){am[k.v]=o});r=[];for(ap=0;ap<3;ap++){if(ap==am.y){af++;aB=[];P=[];L=ah.getYear(ab);F=ah.getYear(D);for(at=L;at<=F;at++){P.push(at);aB.push((az.match(/yy/i)?at:(at+"").substr(2,2))+(ah.yearSuffix||""))}R(r,P,aB,ah.yearText)}else{if(ap==am.m){af++;aB=[];P=[];for(at=0;at<12;at++){var Z=az.replace(/[dy]/gi,"").replace(/mm/,(at<9?"0"+(at+1):at+1)+(ah.monthSuffix||"")).replace(/m/,at+1+(ah.monthSuffix||""));P.push(at);aB.push(Z.match(/MM/)?Z.replace(/MM/,'<span class="dw-mon">'+ah.monthNames[at]+"</span>"):Z.replace(/M/,'<span class="dw-mon">'+ah.monthNamesShort[at]+"</span>"))}R(r,P,aB,ah.monthText)}else{if(ap==am.d){af++;aB=[];P=[];for(at=1;at<32;at++){P.push(at);aB.push((az.match(/dd/i)&&at<10?"0"+at:at)+(ah.daySuffix||""))}R(r,P,aB,ah.dayText)}}}}B.push(r)}if(ak.match(/time/i)){T=true;au=[];d.each(["h","i","s","a"],function(o,k){o=N.search(new RegExp(k,"i"));if(o>-1){au.push({o:o,v:k})}});au.sort(function(k,i){return k.o>i.o?1:-1});d.each(au,function(o,k){am[k.v]=af+o});r=[];for(ap=af;ap<af+4;ap++){if(ap==am.h){af++;aB=[];P=[];for(at=aw;at<(m?12:24);at+=w){P.push(at);aB.push(m&&at===0?12:N.match(/hh/i)&&at<10?"0"+at:at)}R(r,P,aB,ah.hourText)}else{if(ap==am.i){af++;aB=[];P=[];for(at=aq;at<60;at+=t){P.push(at);aB.push(N.match(/ii/)&&at<10?"0"+at:at)}R(r,P,aB,ah.minuteText)}else{if(ap==am.s){af++;aB=[];P=[];for(at=aj;at<60;at+=l){P.push(at);aB.push(N.match(/ss/)&&at<10?"0"+at:at)}R(r,P,aB,ah.secText)}else{if(ap==am.a){af++;var al=N.match(/A/);R(r,[0,1],al?[ah.amText.toUpperCase(),ah.pmText.toUpperCase()]:[ah.amText,ah.pmText],ah.ampmText)}}}}}B.push(r)}function ar(p,k,o){if(am[k]!==f){return +p[am[k]]}if(o!==f){return o}return av[k](h)}function R(p,o,i,s){p.push({values:i,keys:o,label:s})}function aa(k,o,p,i){return Math.min(i,Math.floor(k/o)*o+p)}function W(i){return ah.getYear(i)}function aC(i){return ah.getMonth(i)}function E(i){return ah.getDay(i)}function ay(k){var i=k.getHours();i=m&&i>=12?i-12:i;return aa(i,w,aw,V)}function ag(i){return aa(i.getMinutes(),t,aq,S)}function u(i){return aa(i.getSeconds(),l,aj,J)}function H(i){return M&&i.getHours()>11?1:0}function G(k){if(k===null){return k}var i=ar(k,"h",0);return ah.getDate(ar(k,"y"),ar(k,"m"),ar(k,"d"),ar(k,"a",0)?i+12:i,ar(k,"i",0),ar(k,"s",0))}function C(o,k,i){return Math.floor((i-k)/o)*o+k}function ax(aE,o){var p,s,k=false,aD=false,i=0,aF=0;if(q(aE)){return aE}if(aE<ab){aE=ab}if(aE>D){aE=D}p=aE;s=aE;if(o!==2){k=q(p);while(!k&&p<D){p=new Date(p.getTime()+1000*60*60*24);k=q(p);i++}}if(o!==1){aD=q(s);while(!aD&&s>ab){s=new Date(s.getTime()-1000*60*60*24);aD=q(s);aF++}}if(o===1&&k){return p}if(o===2&&aD){return s}return aF<i&&aD?s:p}function q(i){if(i<ab){return false}if(i>D){return false}if(O(i,ac)){return true}if(O(i,ae)){return false}return true}function O(s,p){var o,k,i;if(p){for(k=0;k<p.length;k++){o=p[k];i=o+"";if(!o.start){if(o.getTime){if(s.getFullYear()==o.getFullYear()&&s.getMonth()==o.getMonth()&&s.getDate()==o.getDate()){return true}}else{if(!i.match(/w/i)){i=i.split("/");if(i[1]){if((i[0]-1)==s.getMonth()&&i[1]==s.getDate()){return true}}else{if(i[0]==s.getDate()){return true}}}else{i=+i.replace("w","");if(i==s.getDay()){return true}}}}}}return false}function y(s,aF,k,aD,o,aG,i){var p,aE,aH;if(s){for(p=0;p<s.length;p++){aE=s[p];aH=aE+"";if(!aE.start){if(aE.getTime){if(ah.getYear(aE)==aF&&ah.getMonth(aE)==k){aG[ah.getDay(aE)-1]=i}}else{if(!aH.match(/w/i)){aH=aH.split("/");if(aH[1]){if(aH[0]-1==k){aG[aH[1]-1]=i}}else{aG[aH[0]-1]=i}}else{aH=+aH.replace("w","");for(ap=aH-aD;ap<o;ap+=7){if(ap>=0){aG[ap]=i}}}}}}}}function ao(k,aT,aG,aZ,aE,aN,a0,a3,aK){var a1,aP,aM,aY,aX,aJ,aH,s,o,aQ,aO,aL,aI,a2,p,aW,aV,aS,aD={},aU={h:w,i:t,s:l,a:1},aR=ah.getDate(aE,aN,a0),aF=["a","h","i","s"];if(k){d.each(k,function(a4,a5){if(a5.start){a5.apply=false;a1=a5.d;aP=a1+"";aM=aP.split("/");if(a1&&((a1.getTime&&aE==ah.getYear(a1)&&aN==ah.getMonth(a1)&&a0==ah.getDay(a1))||(!aP.match(/w/i)&&((aM[1]&&a0==aM[1]&&aN==aM[0]-1)||(!aM[1]&&a0==aM[0])))||(aP.match(/w/i)&&aR.getDay()==+aP.replace("w","")))){a5.apply=true;aD[aR]=true}}});d.each(k,function(i,a4){aI=0;a2=0;aO=0;aL=f;aJ=true;aH=true;p=false;if(a4.start&&(a4.apply||(!a4.d&&!aD[aR]))){aY=a4.start.split(":");aX=a4.end.split(":");for(aQ=0;aQ<3;aQ++){if(aY[aQ]===f){aY[aQ]=0}if(aX[aQ]===f){aX[aQ]=59}aY[aQ]=+aY[aQ];aX[aQ]=+aX[aQ]}aY.unshift(aY[0]>11?1:0);aX.unshift(aX[0]>11?1:0);if(m){if(aY[1]>=12){aY[1]=aY[1]-12}if(aX[1]>=12){aX[1]=aX[1]-12}}for(aQ=0;aQ<aT;aQ++){if(X[aQ]!==f){s=aa(aY[aQ],aU[aF[aQ]],K[aF[aQ]],x[aF[aQ]]);o=aa(aX[aQ],aU[aF[aQ]],K[aF[aQ]],x[aF[aQ]]);aW=0;aV=0;aS=0;if(m&&aQ==1){aW=aY[0]?12:0;aV=aX[0]?12:0;aS=X[0]?12:0}if(!aJ){s=0}if(!aH){o=x[aF[aQ]]}if((aJ||aH)&&(s+aW<X[aQ]+aS&&X[aQ]+aS<o+aV)){p=true}if(X[aQ]!=s){aJ=false}if(X[aQ]!=o){aH=false}}}if(!aK){for(aQ=aT+1;aQ<4;aQ++){if(aY[aQ]>0){aI=aU[aG]}if(aX[aQ]<x[aF[aQ]]){a2=aU[aG]}}}if(!p){s=aa(aY[aT],aU[aG],K[aG],x[aG])+aI;o=aa(aX[aT],aU[aG],K[aG],x[aG])-a2;if(aJ){aO=v(a3,s,x[aG],0)}if(aH){aL=v(a3,o,x[aG],1)}}if(aJ||aH||p){if(aK){d(".dw-li",a3).slice(aO,aL).addClass("dw-v")}else{d(".dw-li",a3).slice(aO,aL).removeClass("dw-v")}}}})}}function Q(k,i){return d(".dw-li",k).index(d('.dw-li[data-val="'+i+'"]',k))}function v(o,k,i,p){if(k<0){return 0}if(k>i){return d(".dw-li",o).length}return Q(o,k)+p}function n(p){var o,k=[];if(p===null||p===f){return p}for(o in am){k[am[o]]=av[o](p)}return k}function ai(k){var s,o,aD,p=[];if(k){for(s=0;s<k.length;s++){o=k[s];if(o.start&&o.start.getTime){aD=new Date(o.start);while(aD<=o.end){p.push(new Date(aD.getFullYear(),aD.getMonth(),aD.getDate()));aD.setDate(aD.getDate()+1)}}else{p.push(o)}}return p}return k}Y.setDate=function(p,o,k,i,s){Y.temp=n(p);Y.setValue(Y.temp,o,k,i,s)};Y.getDate=function(i){return G(i?Y.temp:Y.values)};Y.convert=function(k){var i=k;if(!d.isArray(k)){i=[];d.each(k,function(p,s){d.each(s,function(aD,aE){if(p==="daysOfWeek"){if(aE.d){aE.d="w"+aE.d}else{aE="w"+aE}}i.push(aE)})})}return i};Y.format=an;Y.order=am;Y.buttons.now={text:ah.nowText,css:"dwb-n",handler:function(){Y.setDate(new Date(),false,0.3,true,true)}};if(ah.showNow){ah.buttons.splice(d.inArray("set",ah.buttons)+1,0,"now")}ae=ae?Y.convert(ae):false;ae=ai(ae);ac=ai(ac);ab=G(n(ab));D=G(n(D));K={y:ab.getFullYear(),m:0,d:1,h:aw,i:aq,s:aj,a:0};x={y:D.getFullYear(),m:11,d:31,h:V,i:S,s:J,a:1};return{wheels:B,headerText:ah.headerText?function(){return g.formatDate(an,G(Y.temp),ah)}:false,formatResult:function(i){return g.formatDate(U,G(i),ah)},parseValue:function(i){return n(i?g.parseDate(U,i,ah):(ah.defaultValue||new Date()))},validate:function(k,aE,p,s){var o=ax(G(Y.temp),s),aI=n(o),aG=ar(aI,"y"),aD=ar(aI,"m"),aH=true,aF=true;d.each(["y","m","d","a","h","i","s"],function(aR,aO){if(am[aO]!==f){var aN=K[aO],aQ=x[aO],aM=31,aJ=ar(aI,aO),aT=d(".dw-ul",k).eq(am[aO]);if(aO=="d"){aM=ah.getMaxDayOfMonth(aG,aD);aQ=aM;if(z){d(".dw-li",aT).each(function(){var aU=d(this),aW=aU.data("val"),i=ah.getDate(aG,aD,aW).getDay(),aV=az.replace(/[my]/gi,"").replace(/dd/,(aW<10?"0"+aW:aW)+(ah.daySuffix||"")).replace(/d/,aW+(ah.daySuffix||""));d(".dw-i",aU).html(aV.match(/DD/)?aV.replace(/DD/,'<span class="dw-day">'+ah.dayNames[i]+"</span>"):aV.replace(/D/,'<span class="dw-day">'+ah.dayNamesShort[i]+"</span>"))})}}if(aH&&ab){aN=av[aO](ab)}if(aF&&D){aQ=av[aO](D)}if(aO!="y"){var aL=Q(aT,aN),aK=Q(aT,aQ);d(".dw-li",aT).removeClass("dw-v").slice(aL,aK+1).addClass("dw-v");if(aO=="d"){d(".dw-li",aT).removeClass("dw-h").slice(aM).addClass("dw-h")}}if(aJ<aN){aJ=aN}if(aJ>aQ){aJ=aQ}if(aH){aH=aJ==aN}if(aF){aF=aJ==aQ}if(aO=="d"){var aP=ah.getDate(aG,aD,1).getDay(),aS={};y(ae,aG,aD,aP,aM,aS,1);y(ac,aG,aD,aP,aM,aS,0);d.each(aS,function(aV,aU){if(aU){d(".dw-li",aT).eq(aV).removeClass("dw-v")}})}}});if(T){d.each(["a","h","i","s"],function(aL,aJ){var aN=ar(aI,aJ),aM=ar(aI,"d"),aK=d(".dw-ul",k).eq(am[aJ]);if(am[aJ]!==f){ao(ae,aL,aJ,aI,aG,aD,aM,aK,0);ao(ac,aL,aJ,aI,aG,aD,aM,aK,1);X[aL]=+Y.getValidCell(aN,aK,s).val}})}Y.temp=aI}}};d.each(["date","time","datetime"],function(j,h){b.presets.scroller[h]=c;b.presetShort(h)})})(jQuery);(function(a,c){var b={inputClass:"",invalid:[],rtl:false,showInput:true,group:false,groupLabel:"Groups",checkIcon:"checkmark"};a.mobiscroll.presetShort("select");a.mobiscroll.presets.scroller.select=function(g){var q,n,C,m,u,J,y,B,x,r,p,f=a.extend({},g.settings),z=a.extend(g.settings,b,f),K=z.layout||(/top|bottom/.test(z.display)?"liquid":""),D=K=="liquid",j=a(this),L=j.prop("multiple"),A=this.id+"_dummy",G=a('label[for="'+this.id+'"]').attr("for",A),l=z.label!==c?z.label:(G.length?G.text():j.attr("name")),k="dw-msel mbsc-ic mbsc-ic-"+z.checkIcon,O=a("optgroup",j).length&&!z.group,H=[],e=[],h={},F=z.readonly;function M(i,v,s){a("option",i).each(function(){s.push(this.text);v.push(this.value);if(this.disabled){H.push(this.value)}})}function I(){var i,Q,P=0,v=[],R=[],s=[[]];if(z.group){a("optgroup",j).each(function(w){v.push(this.label);R.push(w)});Q={values:v,keys:R,label:z.groupLabel};if(D){s[0][P]=Q}else{s[P]=[Q]}i=m;P++}else{i=j}v=[];R=[];if(O){a("optgroup",j).each(function(w){v.push(this.label);R.push("__group"+w);H.push("__group"+w);M(this,R,v)})}else{M(i,R,v)}Q={multiple:L,values:v,keys:R,label:l};if(D){s[0][P]=Q}else{s[P]=[Q]}return s}function o(i){var s=a("option",j).attr("value");y=L?(i?i[0]:s):(i===c||i===null?s:i);if(z.group){m=j.find('option[value="'+y+'"]').parent();C=m.index()}}function d(s,R,S){var Q=[];if(L){var P=[],w=0;for(w in g._selectedValues){P.push(h[w]);Q.push(w)}u.val(P.join(", "))}else{u.val(s);Q=R?g.temp[J]:null}if(R){j.val(Q);if(S){x=true;j.change()}}}function N(i){var v=i.attr("data-val"),s=i.hasClass("dw-msel");if(L&&i.closest(".dwwl").hasClass("dwwms")){if(i.hasClass("dw-v")){if(s){i.removeClass(k).removeAttr("aria-selected");delete g._selectedValues[v]}else{i.addClass(k).attr("aria-selected","true");g._selectedValues[v]=v}if(g.live){d(v,true,true)}}return false}}if(z.group&&!a("optgroup",j).length){z.group=false}if(!z.invalid.length){z.invalid=H}if(z.group){n=0;J=1}else{n=-1;J=0}a("option",j).each(function(){h[this.value]=this.text});o(j.val());a("#"+A).remove();u=a('<input type="text" id="'+A+'" class="'+z.inputClass+'" placeholder="'+(z.placeholder||"")+'" readonly />');if(z.showInput){u.insertBefore(j)}g.attachShow(u);var t=j.val()||[],E=0;for(E;E<t.length;E++){g._selectedValues[t[E]]=t[E]}d(h[y]);j.off(".dwsel").on("change.dwsel",function(){if(!x){g.setValue(L?j.val()||[]:[j.val()],true)}x=false}).addClass("dw-hsel").attr("tabindex",-1).closest(".ui-field-contain").trigger("create");if(!g._setValue){g._setValue=g.setValue}g.setValue=function(Q,V,s,U,R){var P,S,T=a.isArray(Q)?Q[0]:Q;y=T!==c&&T!==null?T:a("option",j).attr("value");if(L){g._selectedValues={};if(Q){for(P=0;P<Q.length;P++){g._selectedValues[Q[P]]=Q[P]}}}if(T===null){S=null}else{if(z.group){m=j.find('option[value="'+y+'"]').parent();C=m.index();S=[C,y]}else{S=[y]}}g._setValue(S,V,s,U,R);if(V){var w=L?true:y!==j.val();d(h[y],w,R===c?V:R)}};g.getValue=function(i,s){var v=i?g.temp:(g._hasValue?g.values:null);return v?(z.group&&s?v:v[J]):null};return{width:50,wheels:p,layout:K,headerText:false,anchor:u,formatResult:function(i){return h[i[J]]},parseValue:function(P){var s=j.val()||[],w=0;if(L){g._selectedValues={};for(w;w<s.length;w++){g._selectedValues[s[w]]=s[w]}}o(P===c?j.val():P);return z.group?[C,y]:[y]},onBeforeShow:function(){if(L&&z.counter){z.headerText=function(){var i=0;a.each(g._selectedValues,function(){i++});return i+" "+z.selectedText}}o(j.val());if(z.group){B=C;g.temp=[C,y]}z.wheels=I()},onMarkupReady:function(i){i.addClass("dw-select");a(".dwwl"+n,i).on("mousedown touchstart",function(){clearTimeout(r)});if(O){a(".dw",i).addClass("dw-select-gr");a('.dw-li[data-val^="__group"]',i).addClass("dw-w-gr")}if(L){i.addClass("dwms");a(".dwwl",i).on("keydown",function(s){if(s.keyCode==32){s.preventDefault();s.stopPropagation();N(a(".dw-sel",this))}}).eq(J).addClass("dwwms").attr("aria-multiselectable","true");e=a.extend({},g._selectedValues)}},validate:function(P,R,S){var w,s,Q=a(".dw-ul",P).eq(J);if(R===c&&L){s=g._selectedValues;w=0;a(".dwwl"+J+" .dw-li",P).removeClass(k).removeAttr("aria-selected");for(w in s){a(".dwwl"+J+' .dw-li[data-val="'+s[w]+'"]',P).addClass(k).attr("aria-selected","true")}}if(z.group&&(R===c||R===n)){C=+g.temp[n];if(C!==B){m=j.find("optgroup").eq(C);y=m.find("option").not("[disabled]").eq(0).val();y=y||j.val();z.wheels=I();if(!q){g.temp=[C,y];z.readonly=[false,true];clearTimeout(r);r=setTimeout(function(){q=true;B=C;g.changeWheel([J],c,true);z.readonly=F},S*1000);return false}}else{z.readonly=F}}else{y=g.temp[J]}a.each(z.invalid,function(U,T){a('.dw-li[data-val="'+T+'"]',Q).removeClass("dw-v")});q=false},onClear:function(i){g._selectedValues={};u.val("");a(".dwwl"+J+" .dw-li",i).removeClass(k).removeAttr("aria-selected")},onValueTap:N,onSelect:function(i){d(i,true,true)},onCancel:function(){if(!g.live&&L){g._selectedValues=a.extend({},e)}},onChange:function(i){if(g.live&&!L){u.val(i);x=true;j.val(g.temp[J]).change()}},onDestroy:function(){u.remove();j.removeClass("dw-hsel").removeAttr("tabindex")}}}})(jQuery);opt={preset:"datetime",theme:"android-holo light",display:"bottom",mode:"scroller",dateFormat:"yy-mm-dd",setText:"确定",cancelText:"取消",dateOrder:"yymmd D",showNow:true,group:true,nowText:"今天",stepMinute:5,dayText:"日",monthText:"月份",yearText:"年份",endYear:2050};
