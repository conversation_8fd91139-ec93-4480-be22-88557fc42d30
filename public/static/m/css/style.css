.flex {display: -webkit-box;display: -webkit-flex;display: flex;}
.flex1 {flex-grow: 1;flex-shrink: 1;}
.flex0 {flex-grow: 0;flex-shrink: 0;}
.flex-row {display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-orient: horizontal;-webkit-flex-direction: row;flex-direction: row;}
.flex-col{display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-orient: vertical;-webkit-flex-direction: column;flex-direction: column;}
.flex-x-center{display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-pack: center;-webkit-justify-content: center;-ms-flex-pack: center;justify-content: center;}
.flex-y-center{display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-align: center;-webkit-align-items: center;-ms-flex-align: center;-ms-grid-row-align: center;align-items: center;}
.flex-y-bottom{display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-align: end;-webkit-align-items: flex-end;-ms-flex-align: end;-ms-grid-row-align: flex-end;align-items: flex-end;}
*{padding: 0px;margin: 0px;font-family: "微软雅黑";}
*{box-sizing: border-box;}

.data-empty {width: 100%; text-align: center; padding-top:50px;padding-bottom:50px}
.data-empty-img{ width: 150px; height: 150px; display: inline-block; }
.data-empty-text{ display: block; text-align: center; color: #999999; font-size: 17px; width: 100%; margin-top:30px; }

.main{width:100%;padding:6px 8px;background:#f7f7f8;font-size:14px}
.header{width:100%;height:40px}
.logo{width:25%}
.logo img{width:100%}
.search{width:75%;padding-left:15px}
.search-inner{width:100%;padding:0 5px;background:#fff;border:1px solid #e0e0e0;border-radius:8px;height:35px;line-height:35px}
.search-inner input{border:0;outline:none;height:33px}
.search_ico{width:22px;height:22px}

.bannerbox{width:100%;height:200px;overflow: hidden;position: relative;margin-top:5px}
.bannerbox img{width:100%;height:200px;}
.bannerhd{position: absolute;bottom:5px;margin:0 auto;text-align:center;width: 100%;}
.bannerhd span{ width:10px;height:10px;background:rgba(0,0,0,0.2);border-radius:5px;margin:0 4px;display:inline-block}
.bannerhd ul{width: 100%;text-align: center;}
.bannerhd li{width: 34px;height: 8px;display: inline-block;background-color: rgba(255,255,255,0.2);border-radius: 6px;margin-right:4px;cursor:pointer}
.bannerhd .on{background-color: rgba(255,255,255,0.8)}

.category{width:100%;background:#fff;border-radius:6px;margin-top:15px;padding:10px}
.category .item{color:#666666;font-size:15px}
.category .item img{width:70%}
.category .item span{height:30px;line-height:30px}

.prolist{margin-top:6px;} 
.prodata{width:100%;background:#fff;border-radius:0;margin-top:5px;padding:8px 10px}
.prolist .item{position:relative;margin-bottom:0}
.prolist .item .f1{width:35%;border-radius:8px;background:#f7f7f8}
.prolist .item .f1 img{width:100%}
.prolist .item .f2{width:65%;padding-left:10px}
.prolist .item .f2 .t1{font-size:16px;color:#404040;font-weight:bold;height:46px;line-height:23px}
.prolist .item .f2 .t2{height:30px;line-height:30px;font-size:12px}
.prolist .item .f2 .t2 span{border:1px solid #f54029;border-radius:6px;color:#f54029;padding:1px 5px}
.prolist .item .f2 .t3{height:20px;line-height:20px;padding-top:0;font-size:12px;color:#cccccc}
.prolist .item .f2 .t4{color:#f54029;font-size:16px;height:25px;line-height:25px;}
.prolist .item .f2 .fx{position:absolute;right:0px;bottom:0;color:#e9353e;background:#ffebe8;border-radius:15px;padding:6px 14px;font-size:16px}

.tabbar{width:100%;background:#fff;position:fixed;bottom:0;height:60px;}
.tabbar .item{width:25%;color:#333;font-size:14px}
.tabbar .item img{width:25px;height:25px}
.tabbar .item span{height:20px;line-height:20px}

.pay-main{width:100%;padding:6px 0px;background:#f7f7f8;font-size:14px}
.pay-pro{width:100%;background:#fff;border-radius:6px;padding:0}
.pay-pro .f1{width:20%;border-radius:8px;background:#f7f7f8}
.pay-pro .f1 img{width:100%}
.pay-pro .f2{width:80%;padding-left:10px;}
.pay-pro .f2 .t1{font-size:16px;color:#404040;font-weight:bold;height:30px;line-height:30px}
.pay-pro .f2 .t2{height:30px;line-height:30px;font-size:16px;color:red}
.pay-form-ti{height:40px;line-height:40px;font-size:16px;color:#666;margin-top:5px;padding-left:20px}
.pay-form{width:100%;background:#fff}
.pay-form .item{width:100%;padding:10px 20px;color:#555;border-bottom:1px solid #f2f2f2}
.pay-form .item:last-child{border-bottom:0}
.pay-form .item .f1{width:80px;text-align:right;padding-right:10px}
.pay-form .item .f2 input[type=text]{width:100%;height:35px;padding:2px 5px;border:1px solid #ddd;border-radius:2px}
.pay-form .item .f2 textarea{width:100%;height:60px;padding:2px 5px;border:1px solid #ddd;border-radius:2px}
.pay-form .item .f2 select{width:100%;height:35px;padding:2px 5px;border:1px solid #ddd;border-radius:2px}
.pay-form .item .f2 label{height:35px;line-height:35px;}
/*.pay-form .item .f2 input[type=checkbox]{margin-top:2px;}
.pay-form .item .f2 input[type=radio]{margin-top:2px;}*/
.pay-btn{width:80%;margin:20px 10%;border:1px solid #f80;background:#f50;color:#fff;height:40px;line-height:40px;border-radius:4px;font-size:16px}

.order-main{width:100%;padding:6px 0px;background:#f7f7f8;font-size:14px}
.order-tab{position:fixed;top:0;z-index:99;display:flex;width:100%; height:45px;border-bottom: 1px #e5e5e5 solid;background: #fff; margin-bottom: 5px;}
.order-tab .item{flex:1;font-size:15px; text-align: center; color:#666; height: 45px; line-height:45px; overflow: hidden;}
.order-tab .on{ border-bottom: 2px #ff4a03 solid;}
.order-container{ width:100%;margin-top:53px}
.order-box{ width: 100%;padding:3px 3%; background: #fff; margin-bottom:6px;}
.order-box .head{ display:flex;width:100%; border-bottom: 1px #e5e5e5 solid; height:35px; line-height:35px; overflow: hidden; color: #999;}
.order-box .head .st0{ width:70px; color: #ff8758; text-align: right; }
.order-box .head .st1{ width:70px; color: #ffc702; text-align: right; }
.order-box .head .st2{ width:70px; color: #ff4246; text-align: right; }
.order-box .head .st3{ width:70px; color: #999; text-align: right; }
.order-box .head .st4{ width:70px; color: #bbb; text-align: right; }
.order-box .content{display:flex;width: 100%; padding:8px 0px;border-bottom: 0px #e5e5e5 dashed;position:relative}
.order-box .content:last-child{ border-bottom: 0; }
.order-box .content img{ width:70px; height:70px;}
.order-box .content .detail{display:flex;flex-direction:column;margin-left:7px;flex:1}
.order-box .content .detail .t1{margin-top:10px;height:30px;line-height:30px;color: #000;}
.order-box .content .detail .t2{height:23px;line-height:23px;color: #999;overflow: hidden;font-size:13px;}
.order-box .content .detail .t3{display:flex;height:15px;line-height:15px;color: #ff4246;}
.order-box .content .detail .x1{ flex:1}
.order-box .content .detail .x2{ width:100rpx;font-size:16px;text-align:right;margin-right:4px}
.order-box .bottom{ width:100%; padding:5px 0px; border-top: 1px #e5e5e5 solid; color: #555;}
.order-box .op{ display:flex;justify-content:flex-end;align-items:center;width:100%; padding:5px 0px; border-top: 1px #e5e5e5 solid; color: #555;font-size:13px}
.order-box .op .btn{ border-radius:5px; padding:3px 5px;margin-left: 10px;}
.order-box .op .pay{ border: 1px #06aa53 solid; color: #06aa53;}
.order-box .op .del{ border: 1px #aaa solid;color: #aaa;}
.order-box .op .coll{ border: 1px #ff4246 solid;color: #ff4246;}
.order-box .op .wul{ border: 1px #556699 solid; color: #556699; }
.order-box .op .ref{ border: 1px #999 solid;color: #999;}
.order-box .op .det{ border: 1px #555 solid;color: #555;}


.orderdetail-op{ display:flex;align-items:center;width:100%; margin-top:10px;padding:5px 10px;color: #555;font-size:13px}
.orderdetail-op .btn{ border-radius:5px; padding:3px 5px;margin-left: 10px;}
.orderdetail-op .pay{ border: 1px #06aa53 solid; color: #06aa53;}
.orderdetail-op .del{ border: 1px #aaa solid;color: #aaa;}
.orderdetail-op .coll{ border: 1px #ff4246 solid;color: #ff4246;}
.orderdetail-op .wul{ border: 1px #556699 solid; color: #556699; }
.orderdetail-op .ref{ border: 1px #999 solid;color: #999;}
.orderdetail-op .det{ border: 1px #555 solid;color: #555;}


.usercenter .banner2{ padding:15px 10px;color:#fff;position:relative;background:#fc8}
.usercenter .banner2 img{ width:70px;height:70px;border-radius:50%;}
.usercenter .banner2 .info{margin-top:5px}
.usercenter .banner2 .info .nickname{font-size:20px;}

.usercenter .list{ width: 100%;background: #fff;margin-top:10px;padding:0 10px;margin-bottom:10px;font-size:16px}
.usercenter .list .item{ height:50px;border-bottom:1px solid #eee}
.usercenter .list .item:last-child{border-bottom:0;margin-bottom:10px}
.usercenter .list .f1{width:25px;height:25px;line-height:25px;}
.usercenter .list .f1 img{ width:20px;height:20px;}
.usercenter .list .f1 span{ width:20px;height:20px;font-size:20px}
.usercenter .list .f3{ color: #666;text-align:right;flex:1}
.usercenter .list .f4{ width: 20px; height: 20px;}


.hongbao-main{width:100%;padding:6px 0px;background:#f7f7f8;font-size:14px}
.hongbao-main .toplabel{width: 100%;background: #f9f9f9;padding: 10px 10px;border-bottom: 1px #e3e3e3 solid;display:flex;}
.hongbao-main .toplabel .t1{color: #666;font-size:15px;flex:1}
.hongbao-main .toplabel .t2{color: #666;font-size:15px;text-align:right}
.hongbao-main .tab{width: 100%;background: #fff;display:flex}
.hongbao-main .tab .item{flex:1;border-right: 1px #f2f2f2 solid;height:40px;line-height:40px;text-align: center;color: #666;}
.hongbao-main .tab .on{color: #ff771b;border-bottom: 2px #ff771b solid;}
.hongbao-main .content{ width:100%;margin-top:10px}
.hongbao-main .content .item{width: 100%;background: #fff;margin-bottom: 5px;}
.hongbao-main .content .item .f1{width:100%;padding: 8px 10px;color: #666;border-bottom: 1px #e3e3e3 dashed;}
.hongbao-main .content .item .f2{display:flex;padding:10px;align-items:center}
.hongbao-main .content .item .f2 .t1{display:flex;flex-direction:column;flex:auto}
.hongbao-main .content .item .f2 .t1 .x2{ color:#999}
.hongbao-main .content .item .f2 .t2{ width:100px;text-align:right;display:flex;flex-direction:column;}
.hongbao-main .content .item .f2 .t2 .x1{color: #000;height:22px;line-height: 22px;overflow: hidden;font-size:18px;}
.hongbao-main .content .item .f2 .t2 .x2{height:22px;line-height: 22px;overflow: hidden;}
.hongbao-main .dfk{color: #ff9900;}
.hongbao-main .yfk{color: red;}
.hongbao-main .ywc{color: #ff6600;}

.logistics .head { width:100%;background: #fff; padding: 10px 10px;display:flex;align-items:center}
.logistics .head .f1{ width:30px;font-size:15px;}
.logistics .head .f2{display:flex;flex-direction:column;flex:auto}
.logistics .content{ width: 100%;  background: #fff; padding: 0 10px;display:flex;flex-direction:column;color: #979797;}
.logistics .content .on{color: #23aa5e;}
.logistics .content .item{display:flex;width: 96%;  margin: 0 2%;border-left: 1px #dadada solid;padding:5px 0}
.logistics .content .item .f1{ width:30px;position:relative}
.logistics .content img{width: 15px; height: 15px; position: absolute; left: -8px; top: 10px;}
/*.content .on img{ top:-1rpx}*/
.logistics .content .item .f1 img{ width: 15px; height: 15px;}

.logistics .content .item .f2{display:flex;flex-direction:column;flex:auto}
.logistics .content .item .f2 .t1{font-size: 15px;}
.logistics .content .item .f2 .t1{font-size: 13px;}