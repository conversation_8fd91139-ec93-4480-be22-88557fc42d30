<?php
// 临时查看酒币提现记录页面（绕过框架限制）
try {
    $pdo = new PDO("mysql:host=localhost;dbname=dd", "root", "12345678");
    
    echo "<h2>🍷 酒币提现记录（临时页面）</h2>";
    echo "<p style='color: orange;'>⚠️ 这是临时调试页面，显示所有平台的提现记录</p>";
    
    // 获取筛选参数
    $aid_filter = $_GET['aid'] ?? '';
    $status_filter = $_GET['status'] ?? '';
    
    // 构建查询条件
    $where_conditions = [];
    $params = [];
    
    if ($aid_filter !== '') {
        $where_conditions[] = "w.aid = ?";
        $params[] = $aid_filter;
    }
    
    if ($status_filter !== '') {
        $where_conditions[] = "w.status = ?";
        $params[] = $status_filter;
    }
    
    $where_sql = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";
    
    // 显示筛选表单
    echo "<form method='GET' style='margin-bottom: 20px; padding: 10px; border: 1px solid #ddd;'>";
    echo "<label>平台ID: <input type='text' name='aid' value='{$aid_filter}' placeholder='如：76'></label> ";
    echo "<label>状态: <select name='status'>";
    echo "<option value=''>全部</option>";
    echo "<option value='0'" . ($status_filter === '0' ? ' selected' : '') . ">待审核</option>";
    echo "<option value='1'" . ($status_filter === '1' ? ' selected' : '') . ">审核通过</option>";
    echo "<option value='2'" . ($status_filter === '2' ? ' selected' : '') . ">审核拒绝</option>";
    echo "<option value='3'" . ($status_filter === '3' ? ' selected' : '') . ">已完成</option>";
    echo "</select></label> ";
    echo "<input type='submit' value='筛选'>";
    echo "</form>";
    
    // 查询数据
    $sql = "
        SELECT w.*, m.nickname, m.tel, m.realname
        FROM ddwx_wine_coin_withdraw w 
        LEFT JOIN ddwx_member m ON w.mid = m.id 
        {$where_sql}
        ORDER BY w.id DESC
        LIMIT 50
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    
    $count = 0;
    $records = [];
    while($row = $stmt->fetch()) {
        $records[] = $row;
        $count++;
    }
    
    echo "<p><strong>查询结果：</strong>共 {$count} 条记录</p>";
    
    if ($count > 0) {
        echo "<div style='overflow-x: auto;'>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>ID</th><th>平台</th><th>用户信息</th><th>提现金额</th><th>手续费</th><th>实际金额</th>";
        echo "<th>提现方式</th><th>账户信息</th><th>状态</th><th>申请时间</th><th>操作</th>";
        echo "</tr>";
        
        foreach($records as $row) {
            $status_colors = ['orange', 'blue', 'red', 'green'];
            $status_texts = ['待审核','审核通过','审核拒绝','已完成'];
            $status_text = $status_texts[$row['status']] ?? '未知';
            $status_color = $status_colors[$row['status']] ?? 'gray';
            
            $createtime = date('Y-m-d H:i:s', $row['createtime']);
            $audit_time = $row['audit_time'] ? date('Y-m-d H:i:s', $row['audit_time']) : '-';
            $complete_time = $row['complete_time'] ? date('Y-m-d H:i:s', $row['complete_time']) : '-';
            
            // 构建账户信息
            $account_info = '';
            if($row['bank_type'] == 'alipay') {
                $account_info = '支付宝：' . $row['bank_account'];
            } elseif($row['bank_type'] == 'bank') {
                $account_info = $row['bank_name'] . '：' . substr($row['bank_account'], 0, 4) . '****' . substr($row['bank_account'], -4);
            }
            if($row['account_name']) {
                $account_info .= '（' . $row['account_name'] . '）';
            }
            
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['aid']}</td>";
            echo "<td>";
            echo "<strong>{$row['nickname']}</strong><br>";
            echo "手机：{$row['tel']}<br>";
            echo "ID：{$row['mid']}";
            if($row['realname']) echo "<br>实名：{$row['realname']}";
            echo "</td>";
            echo "<td style='text-align: right;'>" . number_format($row['amount'], 2) . "</td>";
            echo "<td style='text-align: right;'>" . number_format($row['fee_amount'], 2) . "</td>";
            echo "<td style='text-align: right;'>" . number_format($row['actual_amount'], 2) . "</td>";
            echo "<td>" . ($row['bank_type'] == 'alipay' ? '支付宝' : '银行卡') . "</td>";
            echo "<td style='font-size: 11px;'>{$account_info}</td>";
            echo "<td><span style='color: {$status_color}; font-weight: bold;'>{$status_text}</span></td>";
            echo "<td style='font-size: 11px;'>";
            echo "申请：{$createtime}";
            if($row['audit_time']) echo "<br>审核：{$audit_time}";
            if($row['complete_time']) echo "<br>完成：{$complete_time}";
            echo "</td>";
            echo "<td style='font-size: 11px;'>";
            
            if($row['status'] == 0) {
                echo "<button onclick=\"processWithdraw({$row['id']}, 1, '审核通过')\">通过</button><br>";
                echo "<button onclick=\"processWithdraw({$row['id']}, 2, '审核拒绝')\">拒绝</button>";
            } elseif($row['status'] == 1) {
                echo "<button onclick=\"processWithdraw({$row['id']}, 3, '已完成打款')\">完成</button>";
            }
            
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        echo "</div>";
        
        // 添加简单的操作功能
        echo "<script>
        function processWithdraw(id, status, remark) {
            if(confirm('确认执行操作：' + remark + '？')) {
                fetch('/WineCoin/processWithdraw', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'id=' + id + '&status=' + status + '&remark=' + encodeURIComponent(remark)
                })
                .then(response => response.json())
                .then(data => {
                    alert(data.msg);
                    if(data.status == 1) {
                        location.reload();
                    }
                })
                .catch(error => {
                    alert('操作失败：' + error);
                });
            }
        }
        </script>";
        
    } else {
        echo "<p style='color: orange;'>没有找到符合条件的提现记录。</p>";
        
        // 显示一些统计信息
        echo "<h3>📊 数据库统计</h3>";
        $statsQuery = $pdo->query("
            SELECT 
                aid,
                COUNT(*) as total,
                SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as approved,
                SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as rejected,
                SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed
            FROM ddwx_wine_coin_withdraw 
            GROUP BY aid 
            ORDER BY aid
        ");
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>平台ID</th><th>总数</th><th>待审核</th><th>已通过</th><th>已拒绝</th><th>已完成</th></tr>";
        
        while($stat = $statsQuery->fetch()) {
            echo "<tr>";
            echo "<td><a href='?aid={$stat['aid']}'>{$stat['aid']}</a></td>";
            echo "<td>{$stat['total']}</td>";
            echo "<td style='color: orange;'>{$stat['pending']}</td>";
            echo "<td style='color: blue;'>{$stat['approved']}</td>";
            echo "<td style='color: red;'>{$stat['rejected']}</td>";
            echo "<td style='color: green;'>{$stat['completed']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 发生错误: " . $e->getMessage() . "</p>";
}

echo "<br><hr>";
echo "<p>";
echo "<a href='debug_aid.php'>🔍 查看Session调试信息</a> | ";
echo "<a href='javascript:history.back()'>← 返回上一页</a>";
echo "</p>";
?> 