<?php
session_start();
echo "<h2>🔍 酒币提现记录调试信息</h2>";

echo "<h3>📋 当前Session信息</h3>";
echo "<pre>";
echo "ADMIN_AID: " . ($_SESSION['ADMIN_AID'] ?? '未设置') . "\n";
echo "ADMIN_UID: " . ($_SESSION['ADMIN_UID'] ?? '未设置') . "\n";
echo "ADMIN_LOGIN: " . ($_SESSION['ADMIN_LOGIN'] ?? '未设置') . "\n";
echo "ADMIN_BID: " . ($_SESSION['ADMIN_BID'] ?? '未设置') . "\n";
echo "</pre>";

if (isset($_SESSION['ADMIN_AID'])) {
    $aid = $_SESSION['ADMIN_AID'];
    
    try {
        // 直接查询数据库
        $pdo = new PDO("mysql:host=localhost;dbname=dd", "root", "12345678");
        
        echo "<h3>📊 数据库查询结果</h3>";
        
        // 查询当前aid的提现记录
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM ddwx_wine_coin_withdraw WHERE aid = ?");
        $stmt->execute([$aid]);
        $result = $stmt->fetch();
        
        echo "<p><strong>当前aid ($aid) 的提现记录数量:</strong> {$result['count']}</p>";
        
        // 查询平台76的记录
        $stmt76 = $pdo->prepare("SELECT COUNT(*) as count FROM ddwx_wine_coin_withdraw WHERE aid = 76");
        $stmt76->execute();
        $result76 = $stmt76->fetch();
        
        echo "<p><strong>平台76的提现记录数量:</strong> {$result76['count']}</p>";
        
        // 显示所有平台的记录统计
        echo "<h3>🌍 所有平台统计</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>平台ID</th><th>记录数量</th></tr>";
        
        $stmtAll = $pdo->query("SELECT aid, COUNT(*) as count FROM ddwx_wine_coin_withdraw GROUP BY aid ORDER BY aid");
        while($row = $stmtAll->fetch()) {
            $highlight = ($row['aid'] == $aid) ? " style='background-color: yellow;'" : "";
            echo "<tr{$highlight}><td>{$row['aid']}</td><td>{$row['count']}</td></tr>";
        }
        echo "</table>";
        
        // 显示当前aid的最新记录
        if ($result['count'] > 0) {
            echo "<h3>📝 当前aid ({$aid}) 的最新记录</h3>";
            $stmtRecent = $pdo->prepare("
                SELECT w.id, w.ordernum, w.amount, w.status, w.createtime, m.nickname 
                FROM ddwx_wine_coin_withdraw w 
                LEFT JOIN ddwx_member m ON w.mid = m.id 
                WHERE w.aid = ? 
                ORDER BY w.id DESC 
                LIMIT 5
            ");
            $stmtRecent->execute([$aid]);
            
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>订单号</th><th>用户</th><th>金额</th><th>状态</th><th>时间</th></tr>";
            
            while($row = $stmtRecent->fetch()) {
                $status_text = ['待审核','审核通过','审核拒绝','已完成'][$row['status']] ?? '未知';
                $createtime = date('Y-m-d H:i:s', $row['createtime']);
                
                echo "<tr>";
                echo "<td>{$row['id']}</td>";
                echo "<td>{$row['ordernum']}</td>";
                echo "<td>{$row['nickname']}</td>";
                echo "<td>{$row['amount']}</td>";
                echo "<td>{$status_text}</td>";
                echo "<td>{$createtime}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // 如果当前aid不是76，显示平台76的记录
        if ($aid != 76 && $result76['count'] > 0) {
            echo "<h3>🎯 平台76的记录（这些记录在当前账号下看不到）</h3>";
            $stmt76Records = $pdo->query("
                SELECT w.id, w.ordernum, w.amount, w.status, w.createtime, m.nickname 
                FROM ddwx_wine_coin_withdraw w 
                LEFT JOIN ddwx_member m ON w.mid = m.id 
                WHERE w.aid = 76 
                ORDER BY w.id DESC 
                LIMIT 5
            ");
            
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>订单号</th><th>用户</th><th>金额</th><th>状态</th><th>时间</th></tr>";
            
            while($row = $stmt76Records->fetch()) {
                $status_text = ['待审核','审核通过','审核拒绝','已完成'][$row['status']] ?? '未知';
                $createtime = date('Y-m-d H:i:s', $row['createtime']);
                
                echo "<tr>";
                echo "<td>{$row['id']}</td>";
                echo "<td>{$row['ordernum']}</td>";
                echo "<td>{$row['nickname']}</td>";
                echo "<td>{$row['amount']}</td>";
                echo "<td>{$status_text}</td>";
                echo "<td>{$createtime}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // 查询管理员信息
        echo "<h3>👤 管理员信息</h3>";
        $stmtUser = $pdo->prepare("SELECT id, username, aid, auth_type FROM ddwx_admin_user WHERE aid = ?");
        $stmtUser->execute([$aid]);
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>用户名</th><th>平台ID</th><th>权限类型</th></tr>";
        
        while($row = $stmtUser->fetch()) {
            $highlight = ($row['id'] == $_SESSION['ADMIN_UID']) ? " style='background-color: lightblue;'" : "";
            echo "<tr{$highlight}><td>{$row['id']}</td><td>{$row['username']}</td><td>{$row['aid']}</td><td>{$row['auth_type']}</td></tr>";
        }
        echo "</table>";
        
        echo "<h3>💡 诊断结论</h3>";
        if ($aid == 76) {
            if ($result['count'] > 0) {
                echo "<p style='color: green;'>✅ 当前账号 (aid: $aid) 有提现记录，应该能正常显示数据。</p>";
                echo "<p>如果前端仍不显示，请检查：</p>";
                echo "<ul>";
                echo "<li>浏览器开发者工具的Network面板</li>";
                echo "<li>是否有JavaScript错误</li>";
                echo "<li>访问URL是否正确：/WineCoin/withdrawlog</li>";
                echo "</ul>";
            } else {
                echo "<p style='color: orange;'>⚠️ 当前账号 (aid: $aid) 没有提现记录。</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ 当前账号 (aid: $aid) 不是平台76，看不到平台76的提现记录。</p>";
            echo "<p><strong>解决方案：</strong></p>";
            echo "<ul>";
            echo "<li>登出当前账号</li>";
            echo "<li>使用平台76的管理员账号登录</li>";
            echo "<li>或者联系系统管理员分配正确的权限</li>";
            echo "</ul>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ 未登录或session过期，请重新登录后台管理系统。</p>";
}

echo "<br><hr>";
echo "<p><a href='javascript:history.back()'>← 返回上一页</a></p>";
?> 