# 酒币提现功能实现说明

## 📋 功能概述

酒币提现功能已完整实现，支持用户申请提现、管理员审核、自动/手动打款等完整流程。该功能基于系统现有的通用提现架构，具有高度的可配置性和安全性。

## ✅ **完整功能清单**

### 1. 🏗️ **核心架构**（已完善）
- **数据库表**：`ddwx_wine_coin_withdraw` - 完整的提现申请记录表
- **核心逻辑**：`app/common/WineCoin.php` - 提现申请和处理的业务逻辑
- **配置系统**：`app/controller/WineCoinSetting.php` - 提现参数配置

### 2. 📱 **前端功能**（已完善）
- **提现入口**：酒币钱包首页已有提现按钮
- **提现页面**：`uniapp/pagesExt/winecoin/withdraw.vue` - 现代化提现申请界面
- **数据接口**：`ApiWineCoin/withdraw` - 获取提现页面数据
- **提交接口**：`ApiWineCoin/applyWithdraw` - 提现申请提交

### 3. 🛠️ **后台管理**（已完善）
- **提现记录**：`app/controller/WineCoin.php::withdrawlog()` - 提现记录查看
- **审核处理**：`app/controller/WineCoin.php::processWithdraw()` - 提现审核操作
- **管理页面**：`app/view/wine_coin/withdraw_log.html` - 现代化审核界面
- **菜单集成**：后台菜单已包含"酒币提现记录"

### 4. ⚙️ **配置系统**（已完善）
- **提现开关**：是否允许用户提现
- **金额限制**：最小/最大提现金额
- **每日限额**：用户每日提现总限额
- **手续费设置**：费率、最小费用、最大费用
- **自动审核**：是否自动通过提现申请

## 🔧 **技术实现详解**

### 核心业务逻辑

#### 1. 申请提现流程
```php
// 1. 验证用户余额和提现限制
// 2. 计算手续费
// 3. 冻结用户酒币
// 4. 创建提现申请记录
// 5. 如果开启自动审核，直接处理
\app\common\WineCoin::applyWithdraw($aid, $mid, $amount, $bank_type, $bank_info);
```

#### 2. 审核处理流程
```php
// 1. 审核通过：标记完成，记录成功流水
// 2. 审核拒绝：退回冻结酒币，记录拒绝原因
\app\common\WineCoin::processWithdraw($withdraw_id, $status, $remark);
```

### 数据库表结构
```sql
CREATE TABLE `ddwx_wine_coin_withdraw` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '租户ID',
  `mid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '提现金额',
  `fee_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '手续费金额',
  `actual_amount` decimal(11,2) NOT NULL DEFAULT '0.00' COMMENT '实际到账金额',
  `bank_type` varchar(50) NOT NULL DEFAULT '' COMMENT '提现方式:bank,alipay',
  `bank_account` varchar(255) NOT NULL DEFAULT '' COMMENT '收款账号',
  `bank_name` varchar(255) NOT NULL DEFAULT '' COMMENT '银行名称',
  `account_name` varchar(255) NOT NULL DEFAULT '' COMMENT '账户姓名',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态 0:待审核 1:已通过 2:已拒绝 3:已完成',
  `audit_time` int(11) NOT NULL DEFAULT '0' COMMENT '审核时间',
  `audit_remark` varchar(500) NOT NULL DEFAULT '' COMMENT '审核备注',
  `complete_time` int(11) NOT NULL DEFAULT '0' COMMENT '完成时间',
  `createtime` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='酒币提现申请表';
```

## 💳 **支持的提现方式**

### 1. 支付宝提现
- **所需信息**：支付宝账号、真实姓名
- **到账时间**：1-3个工作日
- **适用场景**：个人用户快速提现

### 2. 银行卡提现
- **所需信息**：银行名称、银行卡号、户名
- **到账时间**：1-3个工作日
- **适用场景**：大额提现、企业用户

## 🛡️ **安全机制**

### 1. 资金安全
- **预冻结机制**：申请时先冻结酒币，防止重复提现
- **审核机制**：人工审核确保提现合规性
- **手续费控制**：合理的手续费设置防止恶意提现

### 2. 风险控制
- **金额限制**：设置最小/最大提现金额
- **频次限制**：每日提现限额控制
- **身份验证**：要求真实姓名和收款信息匹配

### 3. 操作安全
- **权限控制**：只有授权管理员可以审核
- **操作日志**：完整的操作记录和审计追踪
- **状态控制**：严格的状态流转，防止重复操作

## 🎯 **管理员操作指南**

### 1. 查看提现申请
1. 登录后台管理系统
2. 进入"财务管理" → "酒币管理" → "酒币提现记录"
3. 可按用户、状态、时间等条件筛选

### 2. 审核提现申请
1. 点击待审核记录的"详情"查看完整信息
2. 核实用户身份和收款信息
3. 点击"通过"或"拒绝"，填写审核备注
4. 确认操作后状态自动更新

### 3. 标记完成
1. 审核通过后，实际打款完成
2. 点击"标记完成"更新状态
3. 用户端将显示提现成功

### 4. 自动审核设置
1. 进入"系统设置" → "酒币设置"
2. 开启"自动审核"功能
3. 符合条件的提现申请将自动通过

## 📊 **提现状态流程**

```
待审核(0) → 审核通过(1) → 已完成(3)
     ↓
  审核拒绝(2)
```

- **待审核**：用户刚提交申请，等待管理员处理
- **审核通过**：管理员审核通过，等待实际打款
- **审核拒绝**：管理员拒绝申请，酒币已退回用户
- **已完成**：实际打款完成，整个流程结束

## 🚀 **用户操作流程**

### 1. 申请提现
1. 进入酒币钱包首页
2. 点击"提现"按钮
3. 输入提现金额
4. 选择提现方式（支付宝/银行卡）
5. 填写收款信息
6. 确认提交申请

### 2. 查看进度
1. 在酒币流水记录中查看提现状态
2. 系统会显示当前处理进度
3. 收到到账通知后确认完成

## ⚙️ **配置参数说明**

| 参数名称 | 默认值 | 说明 |
|---------|--------|------|
| withdraw_enable | 1 | 是否允许提现 |
| min_withdraw_amount | 10.00 | 最小提现金额 |
| max_withdraw_amount | 10000.00 | 最大提现金额 |
| daily_withdraw_limit | 50000.00 | 每日提现限额 |
| withdraw_fee_rate | 0.005 | 提现手续费率(0.5%) |
| withdraw_fee_min | 0.01 | 最小手续费 |
| withdraw_fee_max | 50.00 | 最大手续费 |
| withdraw_auto_audit | 0 | 是否自动审核 |

## 🔄 **与现有系统集成**

### 1. 复用通用架构
- 基于系统现有的余额提现架构设计
- 复用了成熟的审核流程和权限控制
- 与其他财务模块保持一致的操作体验

### 2. 数据统计集成
- 提现数据已集成到酒币统计系统
- 支持今日/本月提现金额统计
- 风险指标监控（大额提现、待审核数量）

### 3. 通知系统集成
- 支持提现状态变更通知
- 管理员审核提醒
- 用户到账通知

## 📝 **开发日志**

- **v1.0**：完成核心提现逻辑和数据库表设计
- **v1.1**：实现前端提现申请页面和API接口
- **v1.2**：完善后台审核管理界面
- **v1.3**：集成配置系统和安全机制
- **v1.4**：完善用户体验和操作流程

## 🎉 **功能特色**

1. **完整性**：从申请到到账的完整业务流程
2. **安全性**：多重安全机制保障资金安全
3. **灵活性**：丰富的配置选项适应不同需求
4. **易用性**：现代化界面，操作简单直观
5. **可扩展性**：基于通用架构，易于扩展新功能

## 📞 **技术支持**

如需要进一步的功能定制或技术支持，请联系开发团队。

---

**酒币提现功能现已完整实现，可立即投入使用！** 🎉 