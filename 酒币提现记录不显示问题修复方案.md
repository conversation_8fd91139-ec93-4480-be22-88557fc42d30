# 酒币提现记录不显示问题修复方案

## 🔍 问题现状

- **表现**：后台酒币提现记录页面不显示数据
- **数据库状态**：数据库中确实存在数据（平台76有3条提现记录）
- **错误信息**：没有错误日志

## ✅ 已验证的正常项

1. ✅ **数据库表结构完整** - `ddwx_wine_coin_withdraw` 表结构正确
2. ✅ **数据库中有数据** - 平台76有3条提现记录（ID: 4,5,6）
3. ✅ **控制器语法正确** - `app/controller/WineCoin.php` 没有语法错误
4. ✅ **前端页面存在** - `app/view/wine_coin/withdrawlog.html` 页面完整

## 🚨 可能的问题原因

### 1. **管理员账号问题**（最可能）
- 当前登录的管理员账号对应的 `aid` 不是 76
- 数据查询时使用的是 `session('ADMIN_AID')`

### 2. **权限问题**
- 当前管理员没有访问 `WineCoin` 控制器的权限

### 3. **前端访问问题**
- 页面访问的URL不正确
- AJAX请求失败

## 🔧 修复步骤

### 步骤1：检查当前登录管理员的aid

```sql
-- 查询当前可能的管理员账号
SELECT id, aid, username, status FROM ddwx_admin_user WHERE status = 1;

-- 查询平台76的管理员账号  
SELECT id, aid, username, status FROM ddwx_admin_user WHERE aid = 76;
```

### 步骤2：创建临时调试页面

创建 `public/debug_aid.php` 文件来检查当前session中的aid：

```php
<?php
session_start();
echo "当前Session信息：\n";
echo "ADMIN_AID: " . ($_SESSION['ADMIN_AID'] ?? '未设置') . "\n";
echo "ADMIN_UID: " . ($_SESSION['ADMIN_UID'] ?? '未设置') . "\n";
echo "ADMIN_LOGIN: " . ($_SESSION['ADMIN_LOGIN'] ?? '未设置') . "\n";

if (isset($_SESSION['ADMIN_AID'])) {
    $aid = $_SESSION['ADMIN_AID'];
    
    // 直接查询数据库
    $pdo = new PDO("mysql:host=localhost;dbname=dd", "root", "12345678");
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM ddwx_wine_coin_withdraw WHERE aid = ?");
    $stmt->execute([$aid]);
    $result = $stmt->fetch();
    
    echo "当前aid ($aid) 的提现记录数量: " . $result['count'] . "\n";
    
    // 查询平台76的记录
    $stmt76 = $pdo->prepare("SELECT COUNT(*) as count FROM ddwx_wine_coin_withdraw WHERE aid = 76");
    $stmt76->execute();
    $result76 = $stmt76->fetch();
    
    echo "平台76的提现记录数量: " . $result76['count'] . "\n";
}
?>
```

### 步骤3：修复WineCoin控制器（添加调试）

临时修改 `app/controller/WineCoin.php` 的 `withdrawlog()` 方法，添加调试信息：

```php
public function withdrawlog(){
    if(request()->isAjax()){
        // 添加调试信息
        \think\facade\Log::info('WineCoin withdrawlog - 当前aid: ' . aid);
        
        $page = input('param.page');
        $limit = input('param.limit');
        $order = 'id desc';
        
        $where = [];
        $where[] = ['aid','=',aid];
        
        // 记录查询条件
        \think\facade\Log::info('WineCoin withdrawlog - 查询条件: ' . json_encode($where));
        
        if(input('param.mid')) $where[] = ['mid','=',input('param.mid')];
        if(input('param.status') !== '') $where[] = ['status','=',input('param.status')];
        if(input('param.ctime')){
            $ctime = explode(' ~ ',input('param.ctime'));
            $where[] = ['createtime','>=',strtotime($ctime[0])];
            $where[] = ['createtime','<',strtotime($ctime[1]) + 86400];
        }
        
        $count = Db::name('wine_coin_withdraw')->where($where)->count();
        $data = Db::name('wine_coin_withdraw')->where($where)->page($page,$limit)->order($order)->select()->toArray();
        
        // 记录查询结果
        \think\facade\Log::info('WineCoin withdrawlog - 查询结果数量: ' . $count);
        \think\facade\Log::info('WineCoin withdrawlog - 返回数据条数: ' . count($data));
        
        // ... 其余代码保持不变
        
        return json(['code'=>0,'msg'=>'查询成功','count'=>$count,'data'=>$data]);
    }
    
    return View::fetch();
}
```

### 步骤4：检查前端页面访问

在浏览器中：

1. **访问页面**：`http://your-domain/WineCoin/withdrawlog`
2. **检查开发者工具**：
   - Network面板查看AJAX请求
   - Console面板查看JavaScript错误
3. **检查请求URL**：确认AJAX请求的URL是 `/WineCoin/withdrawlog`

### 步骤5：检查管理员权限

查询当前管理员是否有WineCoin控制器的访问权限：

```sql
-- 查询管理员权限
SELECT u.id, u.username, u.aid, u.auth_type, u.auth_data, g.auth_data as group_auth_data
FROM ddwx_admin_user u 
LEFT JOIN ddwx_admin_user_group g ON u.groupid = g.id 
WHERE u.aid = 76 AND u.status = 1;
```

## 🎯 快速修复方案

### 方案1：切换到正确的管理员账号

如果当前登录的管理员aid不是76，需要：

1. 退出当前管理员账号
2. 使用平台76的管理员账号登录
3. 重新访问酒币提现记录页面

### 方案2：创建临时数据展示页面

创建 `public/temp_withdraw_list.php`：

```php
<?php
// 临时查看酒币提现记录页面（绕过框架）
$pdo = new PDO("mysql:host=localhost;dbname=dd", "root", "12345678");

echo "<h2>酒币提现记录（临时页面）</h2>";

// 查询所有平台的提现记录
$stmt = $pdo->query("
    SELECT w.*, m.nickname, m.tel 
    FROM ddwx_wine_coin_withdraw w 
    LEFT JOIN ddwx_member m ON w.mid = m.id 
    ORDER BY w.id DESC
");

echo "<table border='1'>";
echo "<tr><th>ID</th><th>平台</th><th>用户</th><th>金额</th><th>状态</th><th>时间</th></tr>";

while($row = $stmt->fetch()) {
    $status_text = ['待审核','审核通过','审核拒绝','已完成'][$row['status']] ?? '未知';
    $createtime = date('Y-m-d H:i:s', $row['createtime']);
    
    echo "<tr>";
    echo "<td>{$row['id']}</td>";
    echo "<td>{$row['aid']}</td>";
    echo "<td>{$row['nickname']} ({$row['tel']})</td>";
    echo "<td>{$row['amount']}</td>";
    echo "<td>{$status_text}</td>";
    echo "<td>{$createtime}</td>";
    echo "</tr>";
}

echo "</table>";
?>
```

### 方案3：修改控制器查询条件（临时）

临时修改控制器，显示所有平台的数据：

```php
// 临时注释掉aid限制
// $where[] = ['aid','=',aid];

// 或者硬编码查询平台76
$where[] = ['aid','=',76];
```

## 📋 验证清单

完成修复后，请验证：

- [ ] 能够正常访问 `/WineCoin/withdrawlog` 页面
- [ ] 页面能显示提现记录数据
- [ ] AJAX请求返回正确的JSON数据
- [ ] 各个操作按钮（详情、审核、完成）功能正常
- [ ] 搜索和筛选功能正常

## 🚀 后续优化建议

1. **添加错误处理**：在控制器中添加更详细的错误处理和日志记录
2. **权限验证**：确保所有管理员都有适当的权限访问功能
3. **监控机制**：添加数据异常监控，及时发现类似问题
4. **文档完善**：更新操作手册，确保管理员知道如何正确使用功能

## 📞 紧急联系

如果问题仍未解决，请提供以下信息：

1. 当前登录的管理员账号和对应的aid
2. 浏览器开发者工具中的错误信息
3. 服务器错误日志内容
4. 具体的访问URL和操作步骤 