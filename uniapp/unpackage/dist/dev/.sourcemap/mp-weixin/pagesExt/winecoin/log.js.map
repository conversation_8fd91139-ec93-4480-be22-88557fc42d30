{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesExt/winecoin/log.vue?7a4c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesExt/winecoin/log.vue?b970", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesExt/winecoin/log.vue?53c1", "uni-app:///pagesExt/winecoin/log.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesExt/winecoin/log.vue?5657", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesExt/winecoin/log.vue?433d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "loadingMore", "isload", "pre_url", "logList", "selectedType", "currentPage", "pageSize", "totalCount", "hasMore", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "getdata", "refresh", "loadData", "that", "page", "limit", "params", "app", "uni", "title", "selectType", "loadMore", "getLogIcon", "getOtherUserDesc", "viewDetail", "formatAmount", "loaded"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5EA;AAAA;AAAA;AAAA;AAAi0B,CAAgB,iyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4Ir1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;;IAEA;IACA;MACA;IACA;IAEA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;MACA;IACA;EACA;EAEAC;IACAC;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MAEA;QACAC;MACA;QACAA;MACA;MAEA;QACAC;QACAC;MACA;;MAEA;MACA;MACA;QACAC;MACA;MAEAC;QACA;UACAJ;QACA;UACAA;QACA;QACAK;QAEA;UACA;YACAL;YACAK;cACAC;YACA;UACA;UAEAN;UACA;UAEA;YACAA;UACA;YACAA;UACA;;UAEA;UACAA;UAEAA;QACA;UACAI;QACA;MACA;IACA;IAEAG;MACA;MACA;QACA;QACA;MACA;IACA;IAEAC;MACA;QACA;QACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACAP;IACA;IAEAQ;MACA;MACA;IACA;IAEAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvTA;AAAA;AAAA;AAAA;AAA8qC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAlsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/winecoin/log.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/winecoin/log.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./log.vue?vue&type=template&id=64d1b5c3&\"\nvar renderjs\nimport script from \"./log.vue?vue&type=script&lang=js&\"\nexport * from \"./log.vue?vue&type=script&lang=js&\"\nimport style0 from \"./log.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/winecoin/log.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./log.vue?vue&type=template&id=64d1b5c3&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload ? _vm.logList.length : null\n  var l0 =\n    _vm.isload && g0 > 0\n      ? _vm.__map(_vm.logList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.getLogIcon(item.type)\n          var m1 = item.other_user\n            ? _vm.getOtherUserDesc(item.type, item.other_user)\n            : null\n          var m2 = _vm.formatAmount(item.amount)\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            m2: m2,\n          }\n        })\n      : null\n  var g1 = _vm.isload ? _vm.isload && _vm.logList.length === 0 : null\n  var g2 = _vm.isload ? _vm.logList.length : null\n  var g3 = _vm.isload && g2 > 0 ? !_vm.hasMore && _vm.logList.length > 0 : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./log.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./log.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<!-- 筛选选项 -->\n\t\t<scroll-view class=\"filter-tabs-container\" scroll-x=\"true\" show-scrollbar=\"false\">\n\t\t\t<view class=\"filter-tabs\">\n\t\t\t\t<view \n\t\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t\t:class=\"selectedType == '' ? 'active' : ''\" \n\t\t\t\t\t@tap=\"selectType\" \n\t\t\t\t\tdata-type=\"\"\n\t\t\t\t>\n\t\t\t\t\t全部\n\t\t\t\t</view>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t\t:class=\"selectedType == 'recharge' ? 'active' : ''\" \n\t\t\t\t\t@tap=\"selectType\" \n\t\t\t\t\tdata-type=\"recharge\"\n\t\t\t\t>\n\t\t\t\t\t充值\n\t\t\t\t</view>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t\t:class=\"selectedType == 'withdraw' ? 'active' : ''\" \n\t\t\t\t\t@tap=\"selectType\" \n\t\t\t\t\tdata-type=\"withdraw\"\n\t\t\t\t>\n\t\t\t\t\t提现\n\t\t\t\t</view>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t\t:class=\"selectedType == 'transfer_in' ? 'active' : ''\" \n\t\t\t\t\t@tap=\"selectType\" \n\t\t\t\t\tdata-type=\"transfer_in\"\n\t\t\t\t>\n\t\t\t\t\t转入\n\t\t\t\t</view>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t\t:class=\"selectedType == 'transfer_out' ? 'active' : ''\" \n\t\t\t\t\t@tap=\"selectType\" \n\t\t\t\t\tdata-type=\"transfer_out\"\n\t\t\t\t>\n\t\t\t\t\t转出\n\t\t\t\t</view>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t\t:class=\"selectedType == 'payment' ? 'active' : ''\" \n\t\t\t\t\t@tap=\"selectType\" \n\t\t\t\t\tdata-type=\"payment\"\n\t\t\t\t>\n\t\t\t\t\t支付\n\t\t\t\t</view>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t\t:class=\"selectedType == 'refund' ? 'active' : ''\" \n\t\t\t\t\t@tap=\"selectType\" \n\t\t\t\t\tdata-type=\"refund\"\n\t\t\t\t>\n\t\t\t\t\t退款\n\t\t\t\t</view>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t\t:class=\"selectedType == 'bonus' ? 'active' : ''\" \n\t\t\t\t\t@tap=\"selectType\" \n\t\t\t\t\tdata-type=\"bonus\"\n\t\t\t\t>\n\t\t\t\t\t奖励\n\t\t\t\t</view>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t\t:class=\"selectedType == 'fee' ? 'active' : ''\" \n\t\t\t\t\t@tap=\"selectType\" \n\t\t\t\t\tdata-type=\"fee\"\n\t\t\t\t>\n\t\t\t\t\t手续费\n\t\t\t\t</view>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"filter-tab\" \n\t\t\t\t\t:class=\"selectedType == 'activity' ? 'active' : ''\" \n\t\t\t\t\t@tap=\"selectType\" \n\t\t\t\t\tdata-type=\"activity\"\n\t\t\t\t>\n\t\t\t\t\t活动\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</scroll-view>\n\t\t\n\t\t<!-- 流水列表 -->\n\t\t<view class=\"log-list\" v-if=\"logList.length > 0\">\n\t\t\t<view \n\t\t\t\tclass=\"log-item\" \n\t\t\t\tv-for=\"(item, index) in logList\" \n\t\t\t\t:key=\"index\"\n\t\t\t\t@tap=\"viewDetail\" \n\t\t\t\t:data-id=\"item.id\"\n\t\t\t>\n\t\t\t\t<view class=\"log-icon\">\n\t\t\t\t\t<text class=\"log-symbol\">{{getLogIcon(item.type)}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"log-info\">\n\t\t\t\t\t<view class=\"log-title\">\n\t\t\t\t\t\t<text class=\"title-text\">{{item.type_desc}}</text>\n\t\t\t\t\t\t<text class=\"other-user\" v-if=\"item.other_user\">{{getOtherUserDesc(item.type, item.other_user)}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"log-time\">{{item.createtime}}</view>\n\t\t\t\t\t<view class=\"log-remark\" v-if=\"item.remark\">{{item.remark}}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"log-amount\">\n\t\t\t\t\t<text class=\"amount-value\" :class=\"item.amount >= 0 ? 'positive' : 'negative'\">\n\t\t\t\t\t\t{{formatAmount(item.amount)}}\n\t\t\t\t\t</text>\n\t\t\t\t\t<text class=\"balance-after\">余额: {{item.balance_after}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 空状态 -->\n\t\t<view class=\"empty-state\" v-if=\"isload && logList.length === 0\">\n\t\t\t<image class=\"empty-icon\" :src=\"pre_url+'/static/img/empty-transaction.png'\"></image>\n\t\t\t<text class=\"empty-text\">暂无交易记录</text>\n\t\t\t<text class=\"empty-desc\">快去充值酒币吧~</text>\n\t\t</view>\n\t\t\n\t\t<!-- 加载更多 -->\n\t\t<view class=\"load-more\" v-if=\"logList.length > 0\">\n\t\t\t<text v-if=\"!loadingMore && hasMore\" @tap=\"loadMore\">点击加载更多</text>\n\t\t\t<text v-if=\"loadingMore\">加载中...</text>\n\t\t\t<text v-if=\"!hasMore && logList.length > 0\">没有更多数据了</text>\n\t\t</view>\n\t</block>\n\t\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\topt: {},\n\t\t\tloading: false,\n\t\t\tloadingMore: false,\n\t\t\tisload: false,\n\t\t\tpre_url: app.globalData.pre_url,\n\t\t\tlogList: [],\n\t\t\tselectedType: '', // 选中的类型筛选\n\t\t\tcurrentPage: 1,\n\t\t\tpageSize: 20,\n\t\t\ttotalCount: 0,\n\t\t\thasMore: true\n\t\t}\n\t},\n\t\n\tonLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\t\n\t\t// 获取传入的类型参数\n\t\tif (opt.type) {\n\t\t\tthis.selectedType = opt.type;\n\t\t}\n\t\t\n\t\tthis.getdata();\n\t},\n\t\n\tonPullDownRefresh: function () {\n\t\tthis.refresh();\n\t},\n\t\n\tonReachBottom: function () {\n\t\tif (this.hasMore && !this.loadingMore) {\n\t\t\tthis.loadMore();\n\t\t}\n\t},\n\t\n\tmethods: {\n\t\tgetdata: function() {\n\t\t\tthis.currentPage = 1;\n\t\t\tthis.loadData();\n\t\t},\n\t\t\n\t\trefresh: function() {\n\t\t\tthis.currentPage = 1;\n\t\t\tthis.logList = [];\n\t\t\tthis.hasMore = true;\n\t\t\tthis.loadData();\n\t\t},\n\t\t\n\t\tloadData: function() {\n\t\t\tvar that = this;\n\t\t\tvar isRefresh = that.currentPage === 1;\n\t\t\t\n\t\t\tif (isRefresh) {\n\t\t\t\tthat.loading = true;\n\t\t\t} else {\n\t\t\t\tthat.loadingMore = true;\n\t\t\t}\n\t\t\t\n\t\t\tvar params = {\n\t\t\t\tpage: that.currentPage,\n\t\t\t\tlimit: that.pageSize\n\t\t\t};\n\t\t\t\n\t\t\t// 使用统一的账单接口\n\t\t\tvar apiUrl = 'ApiWineCoin/log';\n\t\t\tif (that.selectedType) {\n\t\t\t\tparams.type = that.selectedType;\n\t\t\t}\n\t\t\t\n\t\t\tapp.get(apiUrl, params, function (res) {\n\t\t\t\tif (isRefresh) {\n\t\t\t\t\tthat.loading = false;\n\t\t\t\t} else {\n\t\t\t\t\tthat.loadingMore = false;\n\t\t\t\t}\n\t\t\t\tuni.stopPullDownRefresh();\n\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tif (!that.isload) {\n\t\t\t\t\t\tthat.isload = true;\n\t\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\t\ttitle: '酒币明细'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\n\t\t\t\t\tthat.totalCount = res.count || 0;\n\t\t\t\t\tvar newLogs = res.logs || [];\n\n\t\t\t\t\tif (isRefresh) {\n\t\t\t\t\t\tthat.logList = newLogs;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthat.logList = that.logList.concat(newLogs);\n\t\t\t\t\t}\n\n\t\t\t\t\t// 判断是否还有更多数据\n\t\t\t\t\tthat.hasMore = that.logList.length < that.totalCount;\n\n\t\t\t\t\tthat.loaded();\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res.msg || '获取数据失败');\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\tselectType: function(e) {\n\t\t\tvar type = e.currentTarget.dataset.type;\n\t\t\tif (this.selectedType !== type) {\n\t\t\t\tthis.selectedType = type;\n\t\t\t\tthis.refresh();\n\t\t\t}\n\t\t},\n\t\t\n\t\tloadMore: function() {\n\t\t\tif (this.hasMore && !this.loadingMore) {\n\t\t\t\tthis.currentPage++;\n\t\t\t\tthis.loadData();\n\t\t\t}\n\t\t},\n\t\t\n\t\tgetLogIcon: function(type) {\n\t\t\tvar symbolMap = {\n\t\t\t\t'recharge': '+',\n\t\t\t\t'withdraw': '-',\n\t\t\t\t'transfer_in': '⬅',\n\t\t\t\t'transfer_out': '➡',\n\t\t\t\t'payment': '💳',\n\t\t\t\t'refund': '↩',\n\t\t\t\t'fee': '💰',\n\t\t\t\t'business_income': '🏪',\n\t\t\t\t'bonus': '🎁',\n\t\t\t\t'penalty': '⚠️',\n\t\t\t\t'adjustment': '🔧',\n\t\t\t\t'manual': '✋',\n\t\t\t\t'activity': '🎉',\n\t\t\t\t'settlement': '📊',\n\t\t\t\t'admin_recharge': '⭐',\n\t\t\t\t'admin_deduct': '⚠',\n\t\t\t\t'commission': '💎',\n\t\t\t\t'activity_reward': '🏆'\n\t\t\t};\n\t\t\treturn symbolMap[type] || '📝';\n\t\t},\n\t\t\n\t\tgetOtherUserDesc: function(type, otherUser) {\n\t\t\tif (type === 'transfer_in') {\n\t\t\t\treturn '来自: ' + otherUser;\n\t\t\t} else if (type === 'transfer_out') {\n\t\t\t\treturn '转给: ' + otherUser;\n\t\t\t}\n\t\t\treturn '';\n\t\t},\n\t\t\n\t\tviewDetail: function(e) {\n\t\t\tvar id = e.currentTarget.dataset.id;\n\t\t\tapp.goto('/pagesExt/winecoin/detail?id=' + id);\n\t\t},\n\n\t\tformatAmount: function(amount) {\n\t\t\t// 格式化数字金额\n\t\t\treturn (amount >= 0 ? '+' : '') + amount.toFixed(2);\n\t\t},\n\n\t\tloaded: function() {\n\t\t\tthis.loading = false;\n\t\t}\n\t}\n}\n</script>\n\n<style>\n.container {\n\tbackground-color: #F8FAFC;\n\tmin-height: 100vh;\n}\n\n/* 筛选选项 */\n.filter-tabs-container {\n\tmargin: 20rpx;\n\tborder-radius: 16rpx;\n\tbackground: #fff;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.filter-tabs {\n\tdisplay: flex;\n\tpadding: 20rpx;\n\twhite-space: nowrap;\n\tmin-width: fit-content;\n}\n\n.filter-tab {\n\tflex: none;\n\tmin-width: 120rpx;\n\ttext-align: center;\n\tpadding: 16rpx 24rpx;\n\tmargin-right: 16rpx;\n\tborder-radius: 12rpx;\n\tfont-size: 26rpx;\n\tcolor: #64748B;\n\ttransition: all 0.3s ease;\n\twhite-space: nowrap;\n}\n\n.filter-tab:last-child {\n\tmargin-right: 0;\n}\n\n.filter-tab.active {\n\tbackground: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);\n\tcolor: #fff;\n\tfont-weight: 600;\n}\n\n/* 流水列表 */\n.log-list {\n\tbackground: #fff;\n\tmargin: 20rpx;\n\tborder-radius: 16rpx;\n\toverflow: hidden;\n}\n\n.log-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 32rpx 24rpx;\n\tborder-bottom: 1rpx solid #F1F5F9;\n\ttransition: all 0.3s ease;\n\tposition: relative;\n}\n\n.log-item:last-child {\n\tborder-bottom: none;\n}\n\n.log-item:active {\n\tbackground: #F8FAFC;\n\ttransform: scale(0.98);\n}\n\n.log-icon {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 50%;\n\tbackground: linear-gradient(135deg, #F8FAFC 0%, #E2E8F0 100%);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 24rpx;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\tposition: relative;\n}\n\n.log-icon::before {\n\tcontent: '';\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tborder-radius: 50%;\n\tbackground: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.1) 100%);\n\topacity: 0;\n\ttransition: opacity 0.3s ease;\n}\n\n.log-item:active .log-icon::before {\n\topacity: 1;\n}\n\n.log-symbol {\n\tfont-size: 32rpx;\n\tcolor: #64748B;\n\tfont-weight: bold;\n\tz-index: 1;\n}\n\n.log-info {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.log-title {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 8rpx;\n}\n\n.title-text {\n\tcolor: #1E293B;\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tmargin-right: 16rpx;\n}\n\n.other-user {\n\tcolor: #64748B;\n\tfont-size: 24rpx;\n\tbackground: #F1F5F9;\n\tpadding: 4rpx 12rpx;\n\tborder-radius: 8rpx;\n}\n\n.log-time {\n\tcolor: #64748B;\n\tfont-size: 24rpx;\n\tmargin-bottom: 4rpx;\n}\n\n.log-remark {\n\tcolor: #94A3B8;\n\tfont-size: 22rpx;\n\tline-height: 1.4;\n}\n\n.log-amount {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: flex-end;\n}\n\n.amount-value {\n\tfont-size: 32rpx;\n\tfont-weight: 700;\n\tmargin-bottom: 8rpx;\n\ttext-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);\n\ttransition: all 0.3s ease;\n}\n\n.amount-value.positive {\n\tcolor: #10B981;\n\tbackground: linear-gradient(135deg, #10B981, #059669);\n\t-webkit-background-clip: text;\n\t-webkit-text-fill-color: transparent;\n\tbackground-clip: text;\n}\n\n.amount-value.negative {\n\tcolor: #EF4444;\n\tbackground: linear-gradient(135deg, #EF4444, #DC2626);\n\t-webkit-background-clip: text;\n\t-webkit-text-fill-color: transparent;\n\tbackground-clip: text;\n}\n\n.balance-after {\n\tcolor: #64748B;\n\tfont-size: 22rpx;\n}\n\n/* 空状态 */\n.empty-state {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tpadding: 120rpx 40rpx;\n\ttext-align: center;\n}\n\n.empty-icon {\n\twidth: 160rpx;\n\theight: 160rpx;\n\tmargin-bottom: 32rpx;\n\topacity: 0.6;\n}\n\n.empty-text {\n\tcolor: #64748B;\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tmargin-bottom: 16rpx;\n}\n\n.empty-desc {\n\tcolor: #94A3B8;\n\tfont-size: 24rpx;\n}\n\n/* 加载更多 */\n.load-more {\n\ttext-align: center;\n\tpadding: 40rpx;\n\tcolor: #64748B;\n\tfont-size: 26rpx;\n}\n</style> ", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./log.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./log.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754099085917\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}