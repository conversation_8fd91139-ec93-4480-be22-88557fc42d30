{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesExt/winecoin/detail.vue?34a8", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesExt/winecoin/detail.vue?c582", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesExt/winecoin/detail.vue?5629", "uni-app:///pagesExt/winecoin/detail.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesExt/winecoin/detail.vue?7c74", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesExt/winecoin/detail.vue?cbda"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "detail", "relationInfo", "otherUser", "withdrawInfo", "defaultAvatar", "computed", "transactionSymbol", "iconClass", "amountClass", "amountText", "transferUserTitle", "userAvatar", "onLoad", "app", "onPullDownRefresh", "methods", "loadData", "that", "id", "uni", "title", "getTransactionSymbol", "getIconClass", "viewOrder", "loaded"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6Lx1B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EAEAC;IACA;IACA;MACAC;MACA;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MACA;MACAC;MAEAJ;QAAAK;MAAA;QACAD;QACAE;QAEA;UACAF;UACAE;YACAC;UACA;;UAEA;UACAH;UACAA;UACAA;;UAEA;UACA;YACAA;UACA;YACAA;UACA;UAEAA;QACA;UACAJ;QACA;MACA;IACA;IAEAQ;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;QACAV;MACA;IACA;IAEAW;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrUA;AAAA;AAAA;AAAA;AAAirC,CAAgB,imCAAG,EAAC,C;;;;;;;;;;;ACArsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/winecoin/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/winecoin/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=868209bc&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/winecoin/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=868209bc&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<!-- 交易基本信息 -->\n\t\t<view class=\"main-card\">\n\t\t\t<view class=\"transaction-header\">\n\t\t\t\t<view class=\"transaction-icon\" :class=\"iconClass\">\n\t\t\t\t\t<text class=\"transaction-symbol\">{{transactionSymbol}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"transaction-info\">\n\t\t\t\t\t<text class=\"transaction-title\">{{detail.type_desc}}</text>\n\t\t\t\t\t<text class=\"transaction-time\">{{detail.createtime}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"transaction-amount\" :class=\"amountClass\">\n\t\t\t\t\t<text class=\"amount-value\">{{amountText}}</text>\n\t\t\t\t\t<text class=\"amount-unit\"></text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 交易详情 -->\n\t\t<view class=\"detail-card\">\n\t\t\t<view class=\"card-title\">交易详情</view>\n\t\t\t<view class=\"detail-list\">\n\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t<text class=\"label\">交易金额</text>\n\t\t\t\t\t<text class=\"value\" :class=\"amountClass\">\n\t\t\t\t\t\t{{amountText}} \n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t<text class=\"label\">交易时间</text>\n\t\t\t\t\t<text class=\"value\">{{detail.createtime}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail-item\" v-if=\"detail.remark\">\n\t\t\t\t\t<text class=\"label\">备注说明</text>\n\t\t\t\t\t<text class=\"value\">{{detail.remark}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 对方用户信息 (转账时显示) -->\n\t\t<view class=\"detail-card\" v-if=\"otherUser\">\n\t\t\t<view class=\"card-title\">{{transferUserTitle}}</view>\n\t\t\t<view class=\"user-info\">\n\t\t\t\t<image class=\"user-avatar\" :src=\"userAvatar\" mode=\"aspectFill\"></image>\n\t\t\t\t<view class=\"user-details\">\n\t\t\t\t\t<text class=\"user-name\">{{otherUser.nickname}}</text>\n\t\t\t\t\t<text class=\"user-id\">ID: {{otherUser.id}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 关联信息 -->\n\t\t<view class=\"detail-card\" v-if=\"relationInfo\">\n\t\t\t<view class=\"card-title\">{{relationInfo.type_name}}</view>\n\t\t\t\n\t\t\t<!-- 订单信息 -->\n\t\t\t<view class=\"relation-content\" v-if=\"relationInfo.type == 'order'\">\n\t\t\t\t<view class=\"detail-list\">\n\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t<text class=\"label\">订单号</text>\n\t\t\t\t\t\t<text class=\"value\">{{relationInfo.data.order_sn}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t<text class=\"label\">商品名称</text>\n\t\t\t\t\t\t<text class=\"value\">{{relationInfo.data.product_name}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t<text class=\"label\">订单金额</text>\n\t\t\t\t\t\t<text class=\"value\">¥{{relationInfo.data.amount}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail-item\" v-if=\"relationInfo.data.pay_time\">\n\t\t\t\t\t\t<text class=\"label\">支付时间</text>\n\t\t\t\t\t\t<text class=\"value\">{{relationInfo.data.pay_time}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t<text class=\"label\">订单状态</text>\n\t\t\t\t\t\t<text class=\"value status\">{{relationInfo.data.status}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"action-btn\" @tap=\"viewOrder\" :data-id=\"relationInfo.data.order_id\">\n\t\t\t\t\t查看订单详情\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 充值订单信息 -->\n\t\t\t<view class=\"relation-content\" v-if=\"relationInfo.type == 'recharge'\">\n\t\t\t\t<view class=\"detail-list\">\n\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t<text class=\"label\">充值订单号</text>\n\t\t\t\t\t\t<text class=\"value\">{{relationInfo.data.order_sn}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t<text class=\"label\">充值金额</text>\n\t\t\t\t\t\t<text class=\"value\">{{relationInfo.data.amount}} </text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t<text class=\"label\">支付方式</text>\n\t\t\t\t\t\t<text class=\"value\">{{relationInfo.data.pay_type}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t<text class=\"label\">状态</text>\n\t\t\t\t\t\t<text class=\"value status\">{{relationInfo.data.status}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\n\t\t</view>\n\n\t\t<!-- 新版提现详情 -->\n\t\t<view class=\"detail-card\" v-if=\"withdrawInfo\">\n\t\t\t<view class=\"card-title\">💸 提现详情</view>\n\t\t\t<view class=\"detail-list\">\n\t\t\t\t<view class=\"detail-item\" v-if=\"withdrawInfo.ordernum\">\n\t\t\t\t\t<text class=\"label\">订单号</text>\n\t\t\t\t\t<text class=\"value\">{{withdrawInfo.ordernum}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail-item\" v-if=\"withdrawInfo.withdraw_amount\">\n\t\t\t\t\t<text class=\"label\">提现金额</text>\n\t\t\t\t\t<text class=\"value\">{{withdrawInfo.withdraw_amount}} </text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail-item\" v-if=\"withdrawInfo.fee_amount\">\n\t\t\t\t\t<text class=\"label\">手续费</text>\n\t\t\t\t\t<text class=\"value\">{{withdrawInfo.fee_amount}} </text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail-item\" v-if=\"withdrawInfo.actual_amount\">\n\t\t\t\t\t<text class=\"label\">实际到账</text>\n\t\t\t\t\t<text class=\"value amount positive\">¥{{withdrawInfo.actual_amount}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail-item\" v-if=\"withdrawInfo.status_desc\">\n\t\t\t\t\t<text class=\"label\">提现状态</text>\n\t\t\t\t\t<text class=\"value status\">{{withdrawInfo.status_desc}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail-item\" v-if=\"withdrawInfo.bank_type\">\n\t\t\t\t\t<text class=\"label\">提现方式</text>\n\t\t\t\t\t<text class=\"value\">{{withdrawInfo.bank_type}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail-item\" v-if=\"withdrawInfo.bank_account\">\n\t\t\t\t\t<text class=\"label\">收款账户</text>\n\t\t\t\t\t<text class=\"value\">{{withdrawInfo.bank_account}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail-item\" v-if=\"withdrawInfo.bank_name\">\n\t\t\t\t\t<text class=\"label\">银行名称</text>\n\t\t\t\t\t<text class=\"value\">{{withdrawInfo.bank_name}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail-item\" v-if=\"withdrawInfo.account_name\">\n\t\t\t\t\t<text class=\"label\">收款人</text>\n\t\t\t\t\t<text class=\"value\">{{withdrawInfo.account_name}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail-item\" v-if=\"withdrawInfo.apply_time\">\n\t\t\t\t\t<text class=\"label\">申请时间</text>\n\t\t\t\t\t<text class=\"value\">{{withdrawInfo.apply_time}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail-item\" v-if=\"withdrawInfo.audit_time\">\n\t\t\t\t\t<text class=\"label\">审核时间</text>\n\t\t\t\t\t<text class=\"value\">{{withdrawInfo.audit_time}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail-item\" v-if=\"withdrawInfo.complete_time\">\n\t\t\t\t\t<text class=\"label\">完成时间</text>\n\t\t\t\t\t<text class=\"value\">{{withdrawInfo.complete_time}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"detail-item\" v-if=\"withdrawInfo.audit_remark\">\n\t\t\t\t\t<text class=\"label\">审核备注</text>\n\t\t\t\t\t<text class=\"value\">{{withdrawInfo.audit_remark}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 帮助说明 -->\n\t\t<view class=\"help-card\">\n\t\t\t<view class=\"help-title\">💡 温馨提示</view>\n\t\t\t<view class=\"help-content\">\n\t\t\t\t<text>• 酒币交易记录将永久保存，可随时查询</text>\n\t\t\t\t<text>• 如有疑问，请联系客服获取帮助</text>\n\t\t\t\t<text>• 转账和提现操作不可撤销，请谨慎操作</text>\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp()\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\topt: {},\n\t\t\tloading: false,\n\t\t\tisload: false,\n\t\t\tdetail: {},\n\t\t\trelationInfo: null,\n\t\t\totherUser: null,\n\t\t\twithdrawInfo: null,\n\t\t\tdefaultAvatar: app.globalData.pre_url + '/static/img/default_avatar.png'\n\t\t}\n\t},\n\n\tcomputed: {\n\t\ttransactionSymbol() {\n\t\t\treturn this.getTransactionSymbol(this.detail.type || '');\n\t\t},\n\t\ticonClass() {\n\t\t\treturn this.getIconClass(this.detail.type || '');\n\t\t},\n\t\tamountClass() {\n\t\t\treturn this.detail.amount >= 0 ? 'positive' : 'negative';\n\t\t},\n\t\tamountText() {\n\t\t\tconst amount = this.detail.amount || 0;\n\t\t\treturn (amount >= 0 ? '+' : '') + amount;\n\t\t},\n\t\ttransferUserTitle() {\n\t\t\treturn this.detail.type == 'transfer_in' ? '转账方' : '收款方';\n\t\t},\n\t\tuserAvatar() {\n\t\t\treturn (this.otherUser && this.otherUser.headimg) ? this.otherUser.headimg : this.defaultAvatar;\n\t\t}\n\t},\n\n\tonLoad: function(option) {\n\t\tthis.id = option.id || 0;\n\t\tif (!this.id) {\n\t\t\tapp.error('参数错误');\n\t\t\treturn;\n\t\t}\n\t\tthis.loadData();\n\t},\n\n\tonPullDownRefresh: function() {\n\t\tthis.loadData();\n\t},\n\n\tmethods: {\n\t\tloadData: function() {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\n\t\t\tapp.get('ApiWineCoin/detail', {id: that.id}, function(res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tuni.stopPullDownRefresh();\n\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tthat.isload = true;\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: '交易详情'\n\t\t\t\t\t});\n\n\t\t\t\t\t// 兼容新旧API格式\n\t\t\t\t\tthat.detail = res.detail || res.log;\n\t\t\t\t\tthat.relationInfo = res.relation_info;\n\t\t\t\t\tthat.otherUser = res.other_user;\n\n\t\t\t\t\t// 处理新的提现详情格式\n\t\t\t\t\tif (res.withdraw_info) {\n\t\t\t\t\t\tthat.withdrawInfo = res.withdraw_info;\n\t\t\t\t\t} else if (that.detail && that.detail.withdraw_info) {\n\t\t\t\t\t\tthat.withdrawInfo = that.detail.withdraw_info;\n\t\t\t\t\t}\n\n\t\t\t\t\tthat.loaded();\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res.msg || '获取详情失败');\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\tgetTransactionSymbol: function(type) {\n\t\t\tvar symbolMap = {\n\t\t\t\t'recharge': '+',\n\t\t\t\t'withdraw': '-',\n\t\t\t\t'transfer_in': '⬅',\n\t\t\t\t'transfer_out': '➡',\n\t\t\t\t'payment': '💳',\n\t\t\t\t'refund': '↩',\n\t\t\t\t'fee': '💰',\n\t\t\t\t'business_income': '🏪',\n\t\t\t\t'bonus': '🎁',\n\t\t\t\t'penalty': '⚠️',\n\t\t\t\t'adjustment': '🔧',\n\t\t\t\t'manual': '✋',\n\t\t\t\t'activity': '🎉',\n\t\t\t\t'settlement': '📊',\n\t\t\t\t'withdraw_return': '↺',\n\t\t\t\t'business_recharge': '🏢',\n\t\t\t\t'business_expense': '🏢',\n\t\t\t\t'commission': '💎',\n\t\t\t\t'gift': '🎁',\n\t\t\t\t'compensation': '💊',\n\t\t\t\t'admin_recharge': '⭐',\n\t\t\t\t'admin_deduct': '⚠',\n\t\t\t\t'activity_reward': '🏆',\n\t\t\t\t'consume': '🛒',\n\t\t\t\t'order': '📦'\n\t\t\t};\n\t\t\treturn symbolMap[type] || '📝';\n\t\t},\n\n\t\tgetIconClass: function(type) {\n\t\t\tif (['recharge', 'transfer_in', 'bonus', 'gift', 'commission', 'activity_reward', 'compensation', 'refund'].includes(type)) {\n\t\t\t\treturn 'positive';\n\t\t\t} else if (['withdraw', 'transfer_out', 'payment', 'fee', 'penalty', 'admin_deduct'].includes(type)) {\n\t\t\t\treturn 'negative';\n\t\t\t}\n\t\t\treturn 'neutral';\n\t\t},\n\n\t\tviewOrder: function(e) {\n\t\t\tvar orderId = e.currentTarget.dataset.id;\n\t\t\tif (orderId) {\n\t\t\t\tapp.goto('/pages/order/detail?id=' + orderId);\n\t\t\t}\n\t\t},\n\n\t\tloaded: function() {\n\t\t\tthis.loading = false;\n\t\t}\n\t}\n}\n</script>\n\n<style>\n.container {\n\tbackground-color: #F8FAFC;\n\tmin-height: 100vh;\n\tpadding-bottom: 120rpx;\n}\n\n/* 主卡片 */\n.main-card {\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tmargin: 20rpx;\n\tborder-radius: 24rpx;\n\tpadding: 40rpx;\n\tcolor: #fff;\n}\n\n.transaction-header {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.transaction-icon {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 32rpx;\n\tbackground: rgba(255, 255, 255, 0.2);\n\tbackdrop-filter: blur(10rpx);\n}\n\n.transaction-icon.positive {\n\tbackground: rgba(16, 185, 129, 0.3);\n}\n\n.transaction-icon.negative {\n\tbackground: rgba(239, 68, 68, 0.3);\n}\n\n.transaction-icon.neutral {\n\tbackground: rgba(156, 163, 175, 0.3);\n}\n\n.transaction-symbol {\n\tfont-size: 56rpx;\n\tfont-weight: bold;\n}\n\n.transaction-info {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.transaction-title {\n\tfont-size: 36rpx;\n\tfont-weight: 700;\n\tmargin-bottom: 8rpx;\n}\n\n.transaction-time {\n\tfont-size: 28rpx;\n\topacity: 0.8;\n}\n\n.transaction-amount {\n\ttext-align: right;\n}\n\n.amount-value {\n\tdisplay: block;\n\tfont-size: 48rpx;\n\tfont-weight: 700;\n\tline-height: 1;\n}\n\n.amount-unit {\n\tfont-size: 24rpx;\n\topacity: 0.8;\n\tmargin-top: 8rpx;\n}\n\n/* 详情卡片 */\n.detail-card {\n\tbackground: #fff;\n\tmargin: 20rpx;\n\tborder-radius: 16rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\n}\n\n.card-title {\n\tbackground: #F8FAFC;\n\tpadding: 24rpx 32rpx;\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #334155;\n\tborder-bottom: 1rpx solid #E2E8F0;\n}\n\n.detail-list {\n\tpadding: 0 32rpx;\n}\n\n.detail-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 32rpx 0;\n\tborder-bottom: 1rpx solid #F1F5F9;\n}\n\n.detail-item:last-child {\n\tborder-bottom: none;\n}\n\n.label {\n\tfont-size: 28rpx;\n\tcolor: #64748B;\n}\n\n.value {\n\tfont-size: 28rpx;\n\tcolor: #1E293B;\n\ttext-align: right;\n\tflex: 1;\n\tmargin-left: 32rpx;\n}\n\n.value.positive {\n\tcolor: #10B981;\n\tfont-weight: 600;\n}\n\n.value.negative {\n\tcolor: #EF4444;\n\tfont-weight: 600;\n}\n\n.value.status {\n\tbackground: #E0F2FE;\n\tcolor: #0369A1;\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 8rpx;\n\tfont-size: 24rpx;\n}\n\n/* 用户信息 */\n.user-info {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 32rpx;\n}\n\n.user-avatar {\n\twidth: 96rpx;\n\theight: 96rpx;\n\tborder-radius: 50%;\n\tmargin-right: 24rpx;\n}\n\n.user-details {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.user-name {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #1E293B;\n\tmargin-bottom: 8rpx;\n}\n\n.user-id {\n\tfont-size: 24rpx;\n\tcolor: #64748B;\n}\n\n/* 关联内容 */\n.relation-content {\n\tpadding-bottom: 32rpx;\n}\n\n.action-btn {\n\tmargin: 32rpx;\n\tmargin-top: 0;\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tcolor: #fff;\n\ttext-align: center;\n\tpadding: 24rpx;\n\tborder-radius: 12rpx;\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n}\n\n/* 帮助卡片 */\n.help-card {\n\tbackground: #FEF3C7;\n\tmargin: 20rpx;\n\tborder-radius: 16rpx;\n\tpadding: 32rpx;\n\tborder: 1rpx solid #FDE68A;\n}\n\n.help-title {\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tcolor: #92400E;\n\tmargin-bottom: 16rpx;\n}\n\n.help-content {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.help-content text {\n\tfont-size: 24rpx;\n\tcolor: #A16207;\n\tline-height: 1.6;\n\tmargin-bottom: 8rpx;\n}\n\n.help-content text:last-child {\n\tmargin-bottom: 0;\n}\n</style> ", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754053189819\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}