{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesExt/winecoin/recharge.vue?627c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesExt/winecoin/recharge.vue?576c", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesExt/winecoin/recharge.vue?c6eb", "uni-app:///pagesExt/winecoin/recharge.vue", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesExt/winecoin/recharge.vue?ff86", "webpack:////Users/<USER>/Downloads/tcphp/uniapp/pagesExt/winecoin/recharge.vue?7789"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "opt", "loading", "isload", "userinfo", "rechargeSet", "rechargeAmount", "selectedAmount", "selectedGive", "balanceBg", "btnBg", "onLoad", "onPullDownRefresh", "methods", "getdata", "that", "app", "uni", "title", "onAmountInput", "maxGiveAmount", "maxGive", "selectAmount", "go<PERSON>ech<PERSON>ge", "money", "give", "give_score", "console", "buildQueryString", "query", "loaded"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mLAEN;AACP,KAAK;AACL;AACA,aAAa,+LAEN;AACP,KAAK;AACL;AACA,aAAa,6KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgF11B;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MACA;MACAC;MAEAC;QACAD;QACAE;QAEA;UACAF;UACAE;YACAC;UACA;UAEAH;UACAA;;UAEA;UACA;YACAC;YACA;UACA;;UAEA;UACA;YACAA;YACA;UACA;UAEAD;QACA;UACAC;QACA;MACA;IACA;IAEAG;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;QACAH;QACA;MACA;;MAEA;MACA;QACA;QACA;QAEA;UACA;UACA;YACAI;YACAC;UACA;QACA;QAEA;MACA;IACA;IAEAC;MACA;MACA;MAEA;MACA;MACA;IACA;IAEAC;MACA;MACA;;MAEA;MACA;QACAP;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;;MAEA;MACAC;QACAC;MACA;;MAEA;MACAF;QACAQ;QACAC;QACAC;MACA;QACAT;QAEA;UACA;UACA;UACAD;QACA;UACAA;QACA;MACA;QACAC;QACAU;QACAX;MACA;IACA;IAEAY;MACA;MACA;QACAC;MACA;MACA;IACA;IAEAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnPA;AAAA;AAAA;AAAA;AAAmrC,CAAgB,mmCAAG,EAAC,C;;;;;;;;;;;ACAvsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesExt/winecoin/recharge.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesExt/winecoin/recharge.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./recharge.vue?vue&type=template&id=2832fc38&\"\nvar renderjs\nimport script from \"./recharge.vue?vue&type=script&lang=js&\"\nexport * from \"./recharge.vue?vue&type=script&lang=js&\"\nimport style0 from \"./recharge.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesExt/winecoin/recharge.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recharge.vue?vue&type=template&id=2832fc38&\"", "var components\ntry {\n  components = {\n    loading: function () {\n      return import(\n        /* webpackChunkName: \"components/loading/loading\" */ \"@/components/loading/loading.vue\"\n      )\n    },\n    dpTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/dp-tabbar/dp-tabbar\" */ \"@/components/dp-tabbar/dp-tabbar.vue\"\n      )\n    },\n    popmsg: function () {\n      return import(\n        /* webpackChunkName: \"components/popmsg/popmsg\" */ \"@/components/popmsg/popmsg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isload\n    ? _vm.rechargeSet.give_sets && _vm.rechargeSet.give_sets.length > 0\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recharge.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recharge.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"container\">\n\t<block v-if=\"isload\">\n\t\t<!-- 当前酒币余额 -->\n\t\t<view class=\"wine-balance\" :style=\"{background: balanceBg}\">\n\t\t\t<view class=\"balance-info\">\n\t\t\t\t<view class=\"balance-label\">我的酒币</view>\n\t\t\t\t<view class=\"balance-amount\">{{userinfo.wine_coin || '0.00'}}</view>\n\t\t\t\t<view class=\"balance-tips\" @tap=\"goto\" data-url=\"/pagesExt/winecoin/log\">\n\t\t\t\t\t<text>充值记录</text>\n\t\t\t\t\t<text class=\"iconfont iconjiantou\" style=\"font-size:20rpx\"></text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 充值金额选择 -->\n\t\t<view class=\"recharge-content\">\n\t\t\t<!-- 自定义金额输入 -->\n\t\t\t<view class=\"amount-section\" v-if=\"rechargeSet.can_input == 1\">\n\t\t\t\t<view class=\"section-title\">充值金额（元）</view>\n\t\t\t\t<view class=\"amount-input-box\">\n\t\t\t\t\t<text class=\"currency-symbol\">¥</text>\n\t\t\t\t\t<input \n\t\t\t\t\t\ttype=\"digit\" \n\t\t\t\t\t\tname=\"amount\" \n\t\t\t\t\t\t:value=\"rechargeAmount\" \n\t\t\t\t\t\tplaceholder=\"请输入充值金额\" \n\t\t\t\t\t\tplaceholder-style=\"color:#999;font-size:40rpx\" \n\t\t\t\t\t\t@input=\"onAmountInput\" \n\t\t\t\t\t\tstyle=\"font-size:60rpx\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"amount-tips\" v-if=\"rechargeSet.min_amount > 0\">\n\t\t\t\t\t最低充值{{rechargeSet.min_amount}}元\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 预设金额选项 -->\n\t\t\t<view class=\"preset-amounts\" v-if=\"rechargeSet.give_sets && rechargeSet.give_sets.length > 0\">\n\t\t\t\t<view class=\"section-title\" v-if=\"rechargeSet.can_input == 0\">选择充值金额</view>\n\t\t\t\t<view class=\"amount-grid\">\n\t\t\t\t\t<view \n\t\t\t\t\t\tv-for=\"(item, index) in rechargeSet.give_sets\" \n\t\t\t\t\t\t:key=\"index\" \n\t\t\t\t\t\tv-if=\"item.money > 0\" \n\t\t\t\t\t\tclass=\"amount-item\" \n\t\t\t\t\t\t:class=\"selectedAmount == item.money ? 'selected' : ''\" \n\t\t\t\t\t\t@tap=\"selectAmount\" \n\t\t\t\t\t\t:data-amount=\"item.money\"\n\t\t\t\t\t\t:data-give=\"item.give\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<text class=\"amount-money\">{{rechargeSet.can_input == 1 ? '满' : '充'}}{{item.money}}元</text>\n\t\t\t\t\t\t<text class=\"amount-give\" v-if=\"item.give > 0\">赠{{item.give}}酒币</text>\n\t\t\t\t\t\t<text class=\"amount-give\" v-if=\"item.give_score > 0\">+{{item.give_score}}积分</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 充值说明 -->\n\t\t\t<view class=\"recharge-desc\" v-if=\"rechargeSet.recharge_desc\">\n\t\t\t\t<view class=\"desc-title\">充值说明</view>\n\t\t\t\t<text class=\"desc-content\">{{rechargeSet.recharge_desc}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 充值按钮 -->\n\t\t<view class=\"recharge-actions\">\n\t\t\t<view class=\"recharge-btn\" @tap=\"goRecharge\" :style=\"{background: btnBg}\">\n\t\t\t\t立即充值\n\t\t\t</view>\n\t\t</view>\n\t</block>\n\t\n\t<loading v-if=\"loading\"></loading>\n\t<dp-tabbar :opt=\"opt\"></dp-tabbar>\n\t<popmsg ref=\"popmsg\"></popmsg>\n</view>\n</template>\n\n<script>\nvar app = getApp();\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\topt: {},\n\t\t\tloading: false,\n\t\t\tisload: false,\n\t\t\tuserinfo: {},\n\t\t\trechargeSet: {},\n\t\t\trechargeAmount: '', // 用户输入的充值金额\n\t\t\tselectedAmount: 0,  // 选中的预设金额\n\t\t\tselectedGive: 0,    // 选中金额对应的赠送\n\t\t\tbalanceBg: 'linear-gradient(135deg, #FFD700 0%, #FFA500 100%)',\n\t\t\tbtnBg: 'linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%)'\n\t\t}\n\t},\n\t\n\tonLoad: function (opt) {\n\t\tthis.opt = app.getopts(opt);\n\t\tthis.getdata();\n\t},\n\t\n\tonPullDownRefresh: function () {\n\t\tthis.getdata();\n\t},\n\t\n\tmethods: {\n\t\tgetdata: function() {\n\t\t\tvar that = this;\n\t\t\tthat.loading = true;\n\t\t\t\n\t\t\tapp.get('ApiWineCoin/recharge', {}, function (res) {\n\t\t\t\tthat.loading = false;\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t\n\t\t\t\tif (res.status == 1) {\n\t\t\t\t\tthat.isload = true;\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: '酒币充值'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tthat.userinfo = res.userinfo;\n\t\t\t\t\tthat.rechargeSet = res.recharge_set;\n\t\t\t\t\t\n\t\t\t\t\t// 检查是否允许充值\n\t\t\t\t\tif (that.rechargeSet.can_recharge == 0) {\n\t\t\t\t\t\tapp.goto('/pagesExt/winecoin/log', 'redirect');\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 检查配置是否合理\n\t\t\t\t\tif (that.rechargeSet.can_input == 0 && (!that.rechargeSet.give_sets || that.rechargeSet.give_sets.length == 0)) {\n\t\t\t\t\t\tapp.alert('后台未设置充值金额也未开启输入金额，请联系客服');\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthat.loaded();\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res.msg || '获取数据失败');\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\tonAmountInput: function(e) {\n\t\t\tvar amount = e.detail.value;\n\t\t\tthis.rechargeAmount = amount;\n\t\t\t\n\t\t\t// 清除预设选择\n\t\t\tthis.selectedAmount = 0;\n\t\t\tthis.selectedGive = 0;\n\t\t\t\n\t\t\t// 检查金额有效性\n\t\t\tif (parseFloat(amount) < 0) {\n\t\t\t\tapp.error('金额必须大于0');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 检查是否满足预设赠送条件\n\t\t\tif (this.rechargeSet.give_sets && this.rechargeSet.give_sets.length > 0) {\n\t\t\t\tvar maxGiveAmount = 0;\n\t\t\t\tvar maxGive = 0;\n\t\t\t\t\n\t\t\t\tfor (var i in this.rechargeSet.give_sets) {\n\t\t\t\t\tvar giveSet = this.rechargeSet.give_sets[i];\n\t\t\t\t\tif (parseFloat(amount) >= giveSet.money && giveSet.money > maxGiveAmount) {\n\t\t\t\t\t\tmaxGiveAmount = giveSet.money;\n\t\t\t\t\t\tmaxGive = giveSet.give;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.selectedGive = maxGive;\n\t\t\t}\n\t\t},\n\t\t\n\t\tselectAmount: function(e) {\n\t\t\tvar amount = parseFloat(e.currentTarget.dataset.amount);\n\t\t\tvar give = parseFloat(e.currentTarget.dataset.give || 0);\n\t\t\t\n\t\t\tthis.selectedAmount = amount;\n\t\t\tthis.selectedGive = give;\n\t\t\tthis.rechargeAmount = amount.toString();\n\t\t},\n\t\t\n\t\tgoRecharge: function() {\n\t\t\tvar that = this;\n\t\t\tvar amount = parseFloat(this.rechargeAmount);\n\t\t\t\n\t\t\t// 验证金额\n\t\t\tif (!amount || amount <= 0) {\n\t\t\t\tapp.error('请输入有效的充值金额');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tif (this.rechargeSet.min_amount && amount < this.rechargeSet.min_amount) {\n\t\t\t\tapp.error('充值金额不能低于' + this.rechargeSet.min_amount + '元');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tif (this.rechargeSet.max_amount && amount > this.rechargeSet.max_amount) {\n\t\t\t\tapp.error('充值金额不能超过' + this.rechargeSet.max_amount + '元');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 显示加载状态\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '创建订单中...'\n\t\t\t});\n\t\t\t\n\t\t\t// 调用API创建充值订单\n\t\t\tapp.post('ApiWineCoin/createRechargeOrder', {\n\t\t\t\tmoney: amount,\n\t\t\t\tgive: this.selectedGive || 0,\n\t\t\t\tgive_score: 0\n\t\t\t}, function(res) {\n\t\t\t\tuni.hideLoading();\n\t\t\t\t\n\t\t\t\tif (res.status === 1) {\n\t\t\t\t\t// 订单创建成功，跳转到支付页面\n\t\t\t\t\tvar payUrl = '/pagesExt/pay/pay?id=' + res.payorderid;\n\t\t\t\t\tapp.goto(payUrl);\n\t\t\t\t} else {\n\t\t\t\t\tapp.error(res.msg || '创建订单失败，请重试');\n\t\t\t\t}\n\t\t\t}, function(error) {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tconsole.error('创建充值订单失败:', error);\n\t\t\t\tapp.error('网络错误，请检查网络连接后重试');\n\t\t\t});\n\t\t},\n\t\t\n\t\tbuildQueryString: function(params) {\n\t\t\tvar query = [];\n\t\t\tfor (var key in params) {\n\t\t\t\tquery.push(key + '=' + encodeURIComponent(params[key]));\n\t\t\t}\n\t\t\treturn query.join('&');\n\t\t},\n\t\t\n\t\tloaded: function() {\n\t\t\tthis.loading = false;\n\t\t}\n\t}\n}\n</script>\n\n<style>\n.container {\n\tbackground-color: #F8FAFC;\n\tmin-height: 100vh;\n}\n\n/* 酒币余额显示 */\n.wine-balance {\n\tpadding: 40rpx 30rpx;\n\tmargin: 20rpx;\n\tborder-radius: 24rpx;\n\tcolor: #fff;\n}\n\n.balance-info {\n\ttext-align: center;\n}\n\n.balance-label {\n\tfont-size: 28rpx;\n\topacity: 0.9;\n\tmargin-bottom: 16rpx;\n}\n\n.balance-amount {\n\tfont-size: 72rpx;\n\tfont-weight: 700;\n\tmargin-bottom: 20rpx;\n}\n\n.balance-tips {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 24rpx;\n\topacity: 0.8;\n}\n\n.balance-tips text:first-child {\n\tmargin-right: 8rpx;\n}\n\n/* 充值内容 */\n.recharge-content {\n\tbackground: #fff;\n\tmargin: 20rpx;\n\tborder-radius: 16rpx;\n\toverflow: hidden;\n}\n\n.section-title {\n\tcolor: #1E293B;\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tpadding: 32rpx 24rpx 16rpx;\n}\n\n/* 金额输入 */\n.amount-section {\n\tpadding: 0 24rpx;\n}\n\n.amount-input-box {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 32rpx 24rpx;\n\tbackground: #F8FAFC;\n\tborder-radius: 12rpx;\n\tmargin-bottom: 16rpx;\n}\n\n.currency-symbol {\n\tcolor: #64748B;\n\tfont-size: 48rpx;\n\tmargin-right: 16rpx;\n}\n\n.amount-input-box input {\n\tflex: 1;\n\tcolor: #1E293B;\n\tfont-weight: 600;\n}\n\n.amount-tips {\n\tcolor: #64748B;\n\tfont-size: 24rpx;\n\tpadding-bottom: 24rpx;\n}\n\n/* 预设金额 */\n.preset-amounts {\n\tpadding: 0 24rpx 24rpx;\n}\n\n.amount-grid {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 16rpx;\n}\n\n.amount-item {\n\tflex: 1;\n\tmin-width: calc(50% - 8rpx);\n\tpadding: 32rpx 16rpx;\n\tborder: 2rpx solid #E2E8F0;\n\tborder-radius: 12rpx;\n\ttext-align: center;\n\tbackground: #fff;\n\ttransition: all 0.3s ease;\n}\n\n.amount-item.selected {\n\tborder-color: #FFD700;\n\tbackground: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.1) 100%);\n}\n\n.amount-money {\n\tdisplay: block;\n\tcolor: #1E293B;\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tmargin-bottom: 8rpx;\n}\n\n.amount-give {\n\tdisplay: block;\n\tcolor: #EF4444;\n\tfont-size: 24rpx;\n\tline-height: 1.4;\n}\n\n/* 充值说明 */\n.recharge-desc {\n\tpadding: 24rpx;\n\tborder-top: 1rpx solid #F1F5F9;\n}\n\n.desc-title {\n\tcolor: #1E293B;\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tmargin-bottom: 12rpx;\n}\n\n.desc-content {\n\tcolor: #64748B;\n\tfont-size: 24rpx;\n\tline-height: 1.6;\n}\n\n/* 充值按钮 */\n.recharge-actions {\n\tpadding: 40rpx 20rpx;\n}\n\n.recharge-btn {\n\twidth: 100%;\n\theight: 88rpx;\n\tborder-radius: 44rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tcolor: #fff;\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tbox-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);\n}\n</style> ", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recharge.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recharge.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754024009760\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}