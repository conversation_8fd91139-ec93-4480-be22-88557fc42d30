
.container {
	background-color: #F8FAFC;
	min-height: 100vh;
}

/* 酒币钱包卡片 */
.wine-wallet-card {
	margin: 20rpx;
	border-radius: 24rpx;
	overflow: hidden;
	box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.3);
}
.wallet-bg {
	padding: 40rpx 30rpx;
	position: relative;
}
.wallet-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
}
.wallet-title {
	display: flex;
	align-items: center;
	color: #fff;
	font-size: 32rpx;
	font-weight: 600;
}
.wallet-icon {
	width: 48rpx;
	height: 48rpx;
	margin-right: 16rpx;
}
.wallet-help {
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	color: #fff;
	font-size: 32rpx;
}
.balance-section {
	text-align: center;
	margin-bottom: 40rpx;
}
.balance-label {
	color: rgba(255, 255, 255, 0.8);
	font-size: 28rpx;
	margin-bottom: 16rpx;
}
.balance-amount {
	color: #fff;
	font-size: 72rpx;
	font-weight: 700;
	line-height: 1;
	margin-bottom: 8rpx;
}
.quick-actions {
	display: flex;
	justify-content: space-around;
}
.action-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	color: #fff;
	font-size: 24rpx;
	padding: 16rpx 12rpx;
	border-radius: 16rpx;
	min-width: 120rpx;
	transition: all 0.3s ease;
}
.action-symbol {
	width: 88rpx;
	height: 88rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 12rpx;
	background: rgba(255, 255, 255, 0.2);
	-webkit-backdrop-filter: blur(10rpx);
	        backdrop-filter: blur(10rpx);
	font-size: 48rpx;
	font-weight: bold;
	color: #fff;
}

/* 不同功能的特色背景 */
.recharge-item:active {
	background: rgba(16, 185, 129, 0.2);
}
.withdraw-item:active {
	background: rgba(239, 68, 68, 0.2);
}
.transfer-item:active {
	background: rgba(59, 130, 246, 0.2);
}
.record-item:active {
	background: rgba(156, 163, 175, 0.2);
}

/* 功能菜单 */
.feature-menu {
	background: #fff;
	margin: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
}
.menu-item {
	display: flex;
	align-items: center;
	padding: 32rpx 24rpx;
	border-bottom: 1rpx solid #F1F5F9;
}
.menu-item:last-child {
	border-bottom: none;
}
.menu-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 16rpx;
	background: #F3F4F6;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}
.statistics-icon {
	background: linear-gradient(135deg, #10B981 0%, #059669 100%);
}
.payment-icon {
	background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
}
.menu-symbol {
	font-size: 32rpx;
}
.menu-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}
.menu-title {
	color: #1E293B;
	font-size: 32rpx;
	font-weight: 600;
	margin-bottom: 8rpx;
}
.menu-desc {
	color: #64748B;
	font-size: 24rpx;
}
.menu-arrow {
	color: #CBD5E1;
	font-size: 24rpx;
}



