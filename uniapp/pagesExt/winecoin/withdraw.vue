<template>
	<view class="container">
		<view class="header">
			<view class="balance-card">
				<view class="balance-info">
					<text class="balance-label">可提现酒币</text>
					<text class="balance-amount">{{userinfo.wine_coin}}</text>
				</view>
				<view class="balance-icon">🍷</view>
			</view>
		</view>

		<view class="form-section">
			<view class="form-item">
				<view class="form-label">提现金额</view>
				<view class="amount-input-wrapper">
					<input 
						class="amount-input" 
						type="digit" 
						placeholder="请输入提现金额" 
						v-model="withdrawForm.amount"
						@input="calculateFee"
					/>
					<text class="currency">酒币</text>
				</view>
				<view class="amount-tips">
					<text>最小提现：{{withdrawSet.min_amount}}酒币</text>
					<text>最大提现：{{withdrawSet.max_amount}}酒币</text>
				</view>
			</view>

			<view class="form-item">
				<view class="form-label">提现方式</view>
				<view class="withdraw-methods">
					<view 
						class="method-item" 
						:class="{active: withdrawForm.bank_type === 'alipay'}"
						@tap="selectMethod('alipay')"
					>
						<view class="method-icon">💰</view>
						<text class="method-name">支付宝</text>
					</view>
					<view 
						class="method-item" 
						:class="{active: withdrawForm.bank_type === 'bank'}"
						@tap="selectMethod('bank')"
					>
						<view class="method-icon">🏦</view>
						<text class="method-name">银行卡</text>
					</view>
				</view>
			</view>

			<view class="form-item" v-if="withdrawForm.bank_type === 'alipay'">
				<view class="form-label">支付宝账号</view>
				<input 
					class="form-input" 
					placeholder="请输入支付宝账号" 
					v-model="withdrawForm.bank_account"
				/>
			</view>

			<view class="form-item" v-if="withdrawForm.bank_type === 'bank'">
				<view class="form-label">银行名称</view>
				<input 
					class="form-input" 
					placeholder="如：中国工商银行" 
					v-model="withdrawForm.bank_name"
				/>
			</view>

			<view class="form-item" v-if="withdrawForm.bank_type === 'bank'">
				<view class="form-label">银行卡号</view>
				<input 
					class="form-input" 
					placeholder="请输入银行卡号" 
					v-model="withdrawForm.bank_account"
				/>
			</view>

			<view class="form-item">
				<view class="form-label">持卡人姓名</view>
				<input 
					class="form-input" 
					placeholder="请输入真实姓名" 
					v-model="withdrawForm.account_name"
				/>
			</view>
		</view>

		<view class="fee-section" v-if="withdrawSet.fee_enable && withdrawForm.amount > 0">
			<view class="fee-item">
				<text class="fee-label">提现金额</text>
				<text class="fee-value">{{withdrawForm.amount}}酒币</text>
			</view>
			<view class="fee-item" v-if="feeAmount > 0">
				<text class="fee-label">手续费({{withdrawSet.fee_rate_percent}}%)</text>
				<text class="fee-value">{{feeAmount}}酒币</text>
			</view>
			<view class="fee-item" v-if="feeAmount == 0 && withdrawSet.fee_enable">
				<text class="fee-label">手续费</text>
				<text class="fee-value">免费</text>
			</view>
			<view class="fee-item total">
				<text class="fee-label">实际到账</text>
				<text class="fee-value">{{actualAmount}}元</text>
			</view>
		</view>

		<view class="tips-section">
			<view class="tips-title">提现说明</view>
			<view class="tips-content">
				<text>• {{withdrawSet.withdraw_desc}}</text>
				<text>• 请确保收款信息准确，错误信息可能导致提现失败</text>
				<text>• 提现申请提交后需要审核，审核通过后1-3个工作日到账</text>
				<text>• 手续费将从提现金额中扣除</text>
			</view>
		</view>

		<view class="submit-section">
			<button 
				class="submit-btn" 
				:class="{disabled: !canSubmit}"
				@tap="submitWithdraw"
				:disabled="!canSubmit"
			>
				申请提现
			</button>
		</view>

		<!-- 加载遮罩 -->
		<view class="loading-mask" v-if="loading">
			<view class="loading-content">
				<text>处理中...</text>
			</view>
		</view>
	</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			loading: false,
			userinfo: {
				wine_coin: '0.00'
			},
			withdrawSet: {
				min_amount: 10,
				max_amount: 10000,
				fee_rate: 0.01,
				withdraw_desc: '提现将收取一定手续费，1-3个工作日到账'
			},
			withdrawForm: {
				amount: '',
				bank_type: '',
				bank_account: '',
				bank_name: '',
				account_name: ''
			},
			feeAmount: 0,
			actualAmount: 0
		}
	},
	
	computed: {
		canSubmit() {
			return this.withdrawForm.amount > 0 && 
				   this.withdrawForm.bank_type && 
				   this.withdrawForm.bank_account && 
				   this.withdrawForm.account_name &&
				   (this.withdrawForm.bank_type !== 'bank' || this.withdrawForm.bank_name);
		}
	},
	
	onLoad() {
		this.getWithdrawData();
	},
	
	methods: {
		// 获取提现页面数据
		getWithdrawData() {
			var that = this;
			that.loading = true;
			
			app.get('ApiWineCoin/withdraw', {}, function (res) {
				that.loading = false;
				
				if (res.status === 1) {
					that.userinfo = res.userinfo || {};
					that.withdrawSet = res.withdraw_set || {};
					
					// 预填充用户已有信息
					if (that.userinfo.aliaccount) {
						that.withdrawForm.bank_account = that.userinfo.aliaccount;
						that.withdrawForm.bank_type = 'alipay';
					}
					if (that.userinfo.realname) {
						that.withdrawForm.account_name = that.userinfo.realname;
					}
				} else {
					app.error(res.msg || '获取数据失败');
				}
			}, function(error) {
				that.loading = false;
				app.error('网络错误，请稍后重试');
			});
		},
		
		// 选择提现方式
		selectMethod(type) {
			this.withdrawForm.bank_type = type;
			
			// 清空之前填写的信息
			this.withdrawForm.bank_account = '';
			this.withdrawForm.bank_name = '';
			
			// 如果选择支付宝，预填充已有信息
			if (type === 'alipay' && this.userinfo.aliaccount) {
				this.withdrawForm.bank_account = this.userinfo.aliaccount;
			}
		},
		
		// 计算手续费
		calculateFee() {
			const amount = parseFloat(this.withdrawForm.amount) || 0;
			if (amount > 0 && this.withdrawSet.fee_enable) {
				// 使用后台配置的费率计算手续费
				let fee = amount * this.withdrawSet.fee_rate;
				
				// 应用最小和最大手续费限制
				if (fee < this.withdrawSet.fee_min) {
					fee = this.withdrawSet.fee_min;
				}
				if (fee > this.withdrawSet.fee_max) {
					fee = this.withdrawSet.fee_max;
				}
				
				this.feeAmount = Math.round(fee * 100) / 100;
				this.actualAmount = Math.round((amount - this.feeAmount) * 100) / 100;
			} else {
				// 手续费功能关闭或金额为0
				this.feeAmount = 0;
				this.actualAmount = amount;
			}
		},
		
		// 提交提现申请
		submitWithdraw() {
			if (!this.canSubmit) {
				return;
			}
			
			const amount = parseFloat(this.withdrawForm.amount);
			
			// 验证金额范围
			if (amount < this.withdrawSet.min_amount) {
				app.error(`提现金额不能小于${this.withdrawSet.min_amount}酒币`);
				return;
			}
			
			if (amount > this.withdrawSet.max_amount) {
				app.error(`提现金额不能大于${this.withdrawSet.max_amount}酒币`);
				return;
			}
			
			// 验证余额
			const userBalance = parseFloat(this.userinfo.wine_coin);
			if (amount > userBalance) {
				app.error('提现金额不能大于可用余额');
				return;
			}
			
			var that = this;
			that.loading = true;
			
			app.post('ApiWineCoin/applyWithdraw', that.withdrawForm, function (res) {
				that.loading = false;
				
				if (res.status === 1) {
					uni.showModal({
						title: '提现申请成功',
						content: `提现申请已提交，订单号：${res.ordernum || ''}，请耐心等待审核`,
						showCancel: false,
						success: function () {
							// 跳转到提现记录页面
							app.goto('/pagesExt/winecoin/log?type=withdraw', 'redirectTo');
						}
					});
				} else {
					app.error(res.msg || '提现申请失败');
				}
			}, function(error) {
				that.loading = false;
				app.error('网络错误，请稍后重试');
			});
		}
	}
}
</script>

<style>
.container {
	background: #f5f5f5;
	min-height: 100vh;
	padding: 20rpx;
}

.header {
	margin-bottom: 30rpx;
}

.balance-card {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20rpx;
	padding: 40rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	color: white;
}

.balance-info {
	flex: 1;
}

.balance-label {
	font-size: 28rpx;
	opacity: 0.9;
	display: block;
	margin-bottom: 10rpx;
}

.balance-amount {
	font-size: 48rpx;
	font-weight: bold;
	display: block;
}

.balance-icon {
	font-size: 60rpx;
	opacity: 0.8;
}

.form-section {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.form-item {
	margin-bottom: 40rpx;
}

.form-item:last-child {
	margin-bottom: 0;
}

.form-label {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.amount-input-wrapper {
	position: relative;
	display: flex;
	align-items: center;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	padding: 0 20rpx;
	background: #fafafa;
}

.amount-input {
	flex: 1;
	height: 80rpx;
	font-size: 32rpx;
	color: #333;
}

.currency {
	font-size: 28rpx;
	color: #999;
	margin-left: 10rpx;
}

.amount-tips {
	display: flex;
	justify-content: space-between;
	margin-top: 15rpx;
	font-size: 24rpx;
	color: #999;
}

.withdraw-methods {
	display: flex;
	gap: 20rpx;
}

.method-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 30rpx 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	background: #fafafa;
	transition: all 0.3s;
}

.method-item.active {
	border-color: #667eea;
	background: #f0f4ff;
}

.method-icon {
	font-size: 40rpx;
	margin-bottom: 10rpx;
}

.method-name {
	font-size: 28rpx;
	color: #333;
}

.form-input {
	height: 80rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	padding: 0 20rpx;
	font-size: 32rpx;
	background: #fafafa;
}

.fee-section {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.fee-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.fee-item:last-child {
	margin-bottom: 0;
}

.fee-item.total {
	border-top: 1rpx solid #e0e0e0;
	padding-top: 20rpx;
	font-weight: 600;
}

.fee-label {
	font-size: 30rpx;
	color: #666;
}

.fee-value {
	font-size: 30rpx;
	color: #333;
}

.tips-section {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.tips-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.tips-content {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}

.tips-content text {
	display: block;
	margin-bottom: 10rpx;
}

.submit-section {
	padding: 30rpx 0;
}

.submit-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: 600;
	display: flex;
	align-items: center;
	justify-content: center;
}

.submit-btn.disabled {
	background: #ccc;
	color: #999;
}

.loading-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.loading-content {
	background: white;
	padding: 40rpx 60rpx;
	border-radius: 20rpx;
	font-size: 30rpx;
	color: #333;
}
</style> 