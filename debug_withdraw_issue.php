<?php
/**
 * 酒币提现记录调试脚本
 * 用于诊断提现记录不显示的问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 加载ThinkPHP框架
require_once 'vendor/autoload.php';

// 启动应用
$app = \think\App::getInstance();
$app->initialize();

echo "🔍 酒币提现记录问题诊断\n";
echo "================================\n\n";

// 1. 检查数据库连接
try {
    $db = \think\facade\Db::connect();
    echo "✅ 数据库连接正常\n";
    
    // 2. 检查提现表数据
    $totalCount = \think\facade\Db::name('wine_coin_withdraw')->count();
    echo "📊 提现记录总数: $totalCount\n";
    
    // 3. 按平台分组统计
    $countByAid = \think\facade\Db::name('wine_coin_withdraw')
        ->field('aid, COUNT(*) as count')
        ->group('aid')
        ->select()
        ->toArray();
    
    echo "📈 各平台提现记录数量:\n";
    foreach($countByAid as $item) {
        echo "   - 平台 {$item['aid']}: {$item['count']} 条记录\n";
    }
    echo "\n";
    
    // 4. 检查平台76的详细数据
    $aid76Records = \think\facade\Db::name('wine_coin_withdraw')
        ->where('aid', 76)
        ->field('id, ordernum, amount, status, createtime')
        ->order('id desc')
        ->select()
        ->toArray();
    
    echo "🎯 平台76的提现记录:\n";
    if(empty($aid76Records)) {
        echo "   ❌ 没有找到平台76的提现记录\n";
    } else {
        foreach($aid76Records as $record) {
            $statusText = ['待审核', '已通过', '已拒绝', '已完成'][$record['status']] ?? '未知';
            $createtime = date('Y-m-d H:i:s', $record['createtime']);
            echo "   - ID: {$record['id']}, 单号: {$record['ordernum']}, 金额: {$record['amount']}, 状态: {$statusText}, 时间: {$createtime}\n";
        }
    }
    echo "\n";
    
    // 5. 模拟控制器查询
    echo "🧪 模拟控制器查询 (aid=76):\n";
    $where = [];
    $where[] = ['aid','=',76];
    
    $count = \think\facade\Db::name('wine_coin_withdraw')->where($where)->count();
    $data = \think\facade\Db::name('wine_coin_withdraw')->where($where)->page(1,20)->order('id desc')->select()->toArray();
    
    echo "   - 查询条件: aid=76\n";
    echo "   - 查询结果数量: $count\n";
    echo "   - 返回数据条数: " . count($data) . "\n";
    
    if(count($data) > 0) {
        echo "   ✅ 控制器查询正常，能够获取到数据\n";
        echo "   📋 返回的第一条记录:\n";
        $first = $data[0];
        foreach($first as $key => $value) {
            echo "       $key: $value\n";
        }
    } else {
        echo "   ❌ 控制器查询无结果\n";
    }
    echo "\n";
    
    // 6. 检查是否有用户信息
    echo "👤 检查用户信息:\n";
    if(!empty($aid76Records)) {
        $mid = $aid76Records[0]['mid'] ?? 0;
        if($mid > 0) {
            $member = \think\facade\Db::name('member')->where('id', $mid)->find();
            if($member) {
                echo "   ✅ 用户ID $mid 存在: {$member['nickname']}\n";
            } else {
                echo "   ❌ 用户ID $mid 不存在\n";
            }
        }
    }
    echo "\n";
    
    // 7. 检查JSON响应格式
    echo "📝 模拟AJAX响应:\n";
    $responseData = [];
    foreach($data as $k => $v) {
        $item = $v;
        $item['createtime'] = date('Y-m-d H:i:s', $v['createtime']);
        $item['amount'] = number_format($v['amount'], 2);
        $item['status_text'] = ['待审核','审核通过','审核拒绝','已完成'][$v['status']] ?? '未知';
        $responseData[] = $item;
    }
    
    $response = [
        'code' => 0,
        'msg' => '查询成功',
        'count' => $count,
        'data' => $responseData
    ];
    
    echo "   JSON响应示例:\n";
    echo "   " . json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
    
} catch (Exception $e) {
    echo "❌ 发生错误: " . $e->getMessage() . "\n";
    echo "   文件: " . $e->getFile() . "\n";
    echo "   行号: " . $e->getLine() . "\n";
}

echo "\n================================\n";
echo "🎯 诊断建议:\n";
echo "1. 如果数据库有数据但前端不显示，检查登录的管理员账号对应的aid是否为76\n";
echo "2. 检查前端页面访问的URL是否正确: /WineCoin/withdrawlog\n";
echo "3. 检查浏览器开发者工具的Network面板，查看AJAX请求是否成功\n";
echo "4. 检查是否有JavaScript错误阻止了数据加载\n";
echo "5. 确认管理员有访问WineCoin控制器的权限\n";
?> 