# 同城一家人 - 技术平台

本项目是基于ThinkPHP框架开发的多端同城服务平台，支持微信小程序、H5、百度小程序、支付宝小程序等多个平台。

## 功能特色

- **多端支持**: 微信/百度/支付宝/QQ/抖音小程序 + H5 + UniApp
- **商城系统**: 完整的电商功能，包括商品管理、订单处理、支付集成
- **积分商城**: 积分兑换商品系统
- **酒币钱包**: 虚拟货币系统，支持充值、消费、转账等功能
- **生活服务**: 本地生活服务预订和管理
- **会员体系**: 完善的用户等级和权益管理
- **营销工具**: 优惠券、拼团、秒杀等营销功能

## 技术架构

- **后端框架**: ThinkPHP 6.x
- **前端框架**: UniApp (支持多端编译)
- **数据库**: MySQL
- **缓存**: Redis
- **支付**: 微信支付、支付宝支付、余额支付等

## 项目结构

```
tcphp/
├── app/                 # 应用目录
│   ├── controller/      # 控制器
│   ├── model/          # 模型
│   ├── common/         # 公共类库
│   └── view/           # 视图模板
├── config/             # 配置文件
├── uniapp/            # UniApp前端源码
├── mp-weixin/         # 微信小程序
├── mp-alipay/         # 支付宝小程序
├── h5/                # H5页面
└── docs/              # 项目文档
```

## 核心功能模块

### 1. 用户系统
- 用户注册登录
- 个人资料管理  
- 实名认证
- 会员等级体系

### 2. 商城系统
- 商品分类管理
- 购物车功能
- 订单管理
- 库存管理
- 物流追踪

### 3. 支付系统
- 微信支付
- 支付宝支付
- 余额支付
- 积分支付
- 酒币支付

### 4. 营销系统
- 优惠券管理
- 拼团活动
- 秒杀抢购
- 分销推广

### 5. 酒币钱包系统
- 酒币充值（支持多种套餐）
- 酒币消费
- 流水记录
- 余额管理

## 最近更新

- **⚙️ 酒币提现手续费配置优化（v1.5）**：
  - **百分比输入**：后台手续费率改为百分比输入，输入1表示1%，更直观易懂
  - **动态配置**：前端完全使用后台配置，不再使用硬编码值
  - **完整规则**：支持手续费开关、最小最大限制、费率百分比显示
  - **智能计算**：根据设定规则精确计算手续费，应用所有限制条件
  - **用户体验**：前端显示"手续费(1%)"，让用户清楚了解费率
  - **实时生效**：后台设置修改后前端立即生效

- **💳 酒币提现功能完整实现（v1.4）**：
  - **完整流程**：用户申请→管理员审核→标记完成的完整提现流程
  - **多种方式**：支持支付宝和银行卡两种提现方式
  - **安全机制**：预冻结资金、多重审核、手续费控制等安全保障
  - **管理工具**：完善的后台审核管理界面，支持批量操作
  - **自动化**：支持自动审核设置，提高处理效率
  - **配置灵活**：丰富的提现参数配置，适应不同业务需求
  - **用户体验**：现代化的前端申请界面，操作简单直观

- **🔒 酒币充值支付方式限制（v1.3）**：
  - **支付限制**：酒币充值只允许微信支付，提升资金安全性
  - **界面简化**：支付页面只显示微信支付选项，避免用户选择困难
  - **风险控制**：防止余额循环充值，统一资金流向管理
  - **合规优化**：单一支付渠道便于财务对账和监管审查
  - **技术实现**：在ApiPay控制器中针对winecoin_recharge类型进行特殊处理
  - **兼容处理**：现有订单不受影响，新订单执行新规则

- **🎯 酒币充值账单备注优化（v1.2）**：
  - **详细备注**：显示"酒币充值：支付10元，获得10酒币+赠送1酒币"格式
  - **信息完整**：清楚显示支付金额、获得酒币、赠送酒币
  - **用户体验**：用户可以快速了解每次充值的具体收益
  - **流水清晰**：一条记录包含完整信息，避免多条分散记录
  - **历史更新**：已更新现有用户的充值记录格式

- **🔧 酒币充值支付流程修复（v1.1）**：
  - **问题修复**：解决前端支付调用"设计重不存在"的关键错误
  - **新增接口**：创建 `ApiWineCoin/createRechargeOrder` 支付订单生成接口
  - **支付集成**：完善 `Payorder` 模型对 `winecoin_recharge` 类型的处理
  - **数据完善**：新增 `wine_coin_recharge_order` 充值订单表
  - **流程标准化**：与余额充值等模块保持一致的支付处理流程
  - **前端优化**：改进充值流程，增加订单创建状态提示
  - **完整测试**：提供详细的测试验证和故障排查指南

- **✨ 酒币充值套餐后台管理功能（v1.0）**：
  - **后台管理**：可视化配置充值套餐金额和赠送规则
  - **动态配置**：支持多个充值档位的灵活设置
  - **赠送机制**：可配置充值赠送酒币和积分
  - **前端集成**：前端自动读取后台配置的充值套餐
  - **数据库**：新增专门的充值套餐配置表
  - **权限控制**：集成到后台管理权限体系

## 安装部署

### 环境要求
- PHP >= 7.4
- MySQL >= 5.7
- Redis
- Composer

### 安装步骤
1. 克隆项目到本地
2. 执行 `composer install` 安装依赖
3. 配置数据库连接信息
4. 导入数据库结构
5. 配置Web服务器

## 文档链接

- [酒币提现功能实现说明](酒币提现功能实现说明.md) - 完整的酒币提现功能架构和使用指南
- [酒币提现问题修复说明](酒币提现问题修复说明.md) - 提现按钮无响应问题的深度分析和修复方案
- [酒币提现手续费配置优化说明](酒币提现手续费配置优化说明.md) - 手续费配置从硬编码到动态配置的全面优化方案
- [自动打款功能集成建议](自动打款功能集成建议.md) - 自动打款功能的技术方案和实施建议
- [酒币充值支付方式限制](酒币充值支付方式限制说明.md) - 酒币充值只允许微信支付的功能说明
- [酒币充值账单备注优化](酒币充值账单备注优化说明.md) - 账单备注显示优化的详细说明
- [酒币充值支付流程修复](酒币充值支付流程修复指南.md) - 酒币充值支付问题的完整修复方案和测试指南
- [酒币充值套餐后台管理](docs/winecoin_recharge_package_management.md) - 酒币充值套餐后台管理功能完整说明
- [酒币钱包完整文档](docs/backend_winecoin_complete.md) - 酒币钱包系统的完整技术文档

## 开发团队

本项目由经验丰富的开发团队维护，致力于为用户提供稳定、高效的同城服务平台。

## 许可证

请查看项目根目录的许可证文件了解使用条款。

---

**注意**: 本项目仅供学习和研究使用，商业使用请联系开发团队获得授权。 